package qpublic.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class AccountApprole(
  accountId: DynamicValue[Long] = DynamicValue.Unavailable,
  roleId: DynamicValue[Int] = DynamicValue.Unavailable,
)

object AccountApprole {
  import quill.QuillContext._

  implicit def setAccountApprole(v: AccountApprole): Seq[DynamicSet[qpublic.AccountApprole, _]] = {
    val set = ValueSetter[qpublic.AccountApprole]
    Seq(
      set(_.accountId)(v.accountId),
      set(_.roleId)(v.roleId),
    )
  }
}
