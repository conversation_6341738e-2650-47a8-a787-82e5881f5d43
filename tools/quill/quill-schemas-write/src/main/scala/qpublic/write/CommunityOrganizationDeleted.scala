package qpublic.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class CommunityOrganizationDeleted(
  id: DynamicValue[Long] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
)

object CommunityOrganizationDeleted {
  import quill.QuillContext._

  implicit def setCommunityOrganizationDeleted(v: CommunityOrganizationDeleted)
    : Seq[DynamicSet[qpublic.CommunityOrganizationDeleted, _]] = {
    val set = ValueSetter[qpublic.CommunityOrganizationDeleted]
    Seq(
      set(_.id)(v.id),
      set(_.updateDate)(v.updateDate),
      set(_.updateAccountId)(v.updateAccountId),
    )
  }
}
