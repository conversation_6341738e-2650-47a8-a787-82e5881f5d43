package qpublic.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class Mood(
  id: DynamicValue[Long] = DynamicValue.Unavailable,
  actorDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  farmId: DynamicValue[Option[Long]] = DynamicValue.Unavailable,
  accountId: DynamicValue[Long] = DynamicValue.Unavailable,
  score: DynamicValue[Option[Short]] = DynamicValue.Unavailable,
  comment: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  solved: DynamicValue[Boolean] = DynamicValue.Unavailable,
  solverId: DynamicValue[Option[Long]] = DynamicValue.Unavailable,
  updateDate: DynamicValue[Option[java.time.Instant]] = DynamicValue.Unavailable,
  initialUpdateDate: DynamicValue[Option[java.time.Instant]] = DynamicValue.Unavailable,
  solverComment: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  netPromoterScore: DynamicValue[Option[Short]] = DynamicValue.Unavailable,
)

object Mood {
  import quill.QuillContext._

  implicit def setMood(v: Mood): Seq[DynamicSet[qpublic.Mood, _]] = {
    val set = ValueSetter[qpublic.Mood]
    Seq(
      set(_.id)(v.id),
      set(_.actorDate)(v.actorDate),
      set(_.farmId)(v.farmId),
      set(_.accountId)(v.accountId),
      set(_.score)(v.score),
      set(_.comment)(v.comment),
      set(_.solved)(v.solved),
      set(_.solverId)(v.solverId),
      set(_.updateDate)(v.updateDate),
      set(_.initialUpdateDate)(v.initialUpdateDate),
      set(_.solverComment)(v.solverComment),
      set(_.netPromoterScore)(v.netPromoterScore),
    )
  }
}
