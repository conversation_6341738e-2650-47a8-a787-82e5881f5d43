package qroot.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class MedapplicationtypeLang(
  createDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  createAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
  languageId: DynamicValue[String] = DynamicValue.Unavailable,
  code: DynamicValue[String] = DynamicValue.Unavailable,
  name: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  description: DynamicValue[Option[String]] = DynamicValue.Unavailable,
)

object MedapplicationtypeLang {
  import quill.QuillContext._

  implicit def setMedapplicationtypeLang(v: MedapplicationtypeLang): Seq[DynamicSet[qroot.MedapplicationtypeLang, _]] = {
    val set = ValueSetter[qroot.MedapplicationtypeLang]
    Seq(
      set(_.createDate)(v.createDate),
      set(_.updateDate)(v.updateDate),
      set(_.createAccountId)(v.createAccountId),
      set(_.updateAccountId)(v.updateAccountId),
      set(_.languageId)(v.languageId),
      set(_.code)(v.code),
      set(_.name)(v.name),
      set(_.description)(v.description),
    )
  }
}
