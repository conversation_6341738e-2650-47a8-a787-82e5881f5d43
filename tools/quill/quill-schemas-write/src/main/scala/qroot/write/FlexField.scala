package qroot.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class FlexField(
  id: DynamicValue[Short] = DynamicValue.Unavailable,
  name: DynamicValue[String] = DynamicValue.Unavailable,
  mandatory: DynamicValue[Boolean] = DynamicValue.Unavailable,
  pattern: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  hint: DynamicValue[Option[String]] = DynamicValue.Unavailable,
)

object FlexField {
  import quill.QuillContext._

  implicit def setFlexField(v: FlexField): Seq[DynamicSet[qroot.FlexField, _]] = {
    val set = ValueSetter[qroot.FlexField]
    Seq(
      set(_.id)(v.id),
      set(_.name)(v.name),
      set(_.mandatory)(v.mandatory),
      set(_.pattern)(v.pattern),
      set(_.hint)(v.hint),
    )
  }
}
