package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class DanavlTestingLogsDeleted(
  id: DynamicValue[Long] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
)

object DanavlTestingLogsDeleted {
  import quill.QuillContext._

  implicit def setDanavlTestingLogsDeleted(v: DanavlTestingLogsDeleted): Seq[DynamicSet[qfarm.DanavlTestingLogsDeleted, _]] = {
    val set = ValueSetter[qfarm.DanavlTestingLogsDeleted]
    Seq(
      set(_.id)(v.id),
      set(_.updateDate)(v.updateDate),
      set(_.updateAccountId)(v.updateAccountId),
    )
  }
}
