package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class Servingevent(
  createDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  createAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
  id: DynamicValue[Long] = DynamicValue.Unavailable,
  servingId: DynamicValue[Long] = DynamicValue.Unavailable,
  semenbatchId: DynamicValue[Option[Long]] = DynamicValue.Unavailable,
  comment: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  actorId: DynamicValue[Option[Long]] = DynamicValue.Unavailable,
  actorDate: DynamicValue[Option[java.time.Instant]] = DynamicValue.Unavailable,
  locationId: DynamicValue[Option[Long]] = DynamicValue.Unavailable,
  serviceTypeCode: DynamicValue[Option[String]] = DynamicValue.Unavailable,
)

object Servingevent {
  import quill.QuillContext._

  implicit def setServingevent(v: Servingevent): Seq[DynamicSet[qfarm.Servingevent, _]] = {
    val set = ValueSetter[qfarm.Servingevent]
    Seq(
      set(_.createDate)(v.createDate),
      set(_.updateDate)(v.updateDate),
      set(_.createAccountId)(v.createAccountId),
      set(_.updateAccountId)(v.updateAccountId),
      set(_.id)(v.id),
      set(_.servingId)(v.servingId),
      set(_.semenbatchId)(v.semenbatchId),
      set(_.comment)(v.comment),
      set(_.actorId)(v.actorId),
      set(_.actorDate)(v.actorDate),
      set(_.locationId)(v.locationId),
      set(_.serviceTypeCode)(v.serviceTypeCode),
    )
  }
}
