package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class BusinessSync(
  id: DynamicValue[Long] = DynamicValue.Unavailable,
  traceabilityId: DynamicValue[String] = DynamicValue.Unavailable,
  syncDate: DynamicValue[Option[java.time.Instant]] = DynamicValue.Unavailable,
  syncStatus: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  parentId: DynamicValue[Option[Long]] = DynamicValue.Unavailable,
)

object BusinessSync {
  import quill.QuillContext._

  implicit def setBusinessSync(v: BusinessSync): Seq[DynamicSet[qfarm.BusinessSync, _]] = {
    val set = ValueSetter[qfarm.BusinessSync]
    Seq(
      set(_.id)(v.id),
      set(_.traceabilityId)(v.traceabilityId),
      set(_.syncDate)(v.syncDate),
      set(_.syncStatus)(v.syncStatus),
      set(_.parentId)(v.parentId),
    )
  }
}
