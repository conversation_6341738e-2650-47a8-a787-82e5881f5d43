package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class LocationRwaDeleted(
  id: DynamicValue[Long] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
)

object LocationRwaDeleted {
  import quill.QuillContext._

  implicit def setLocationRwaDeleted(v: LocationRwaDeleted): Seq[DynamicSet[qfarm.LocationRwaDeleted, _]] = {
    val set = ValueSetter[qfarm.LocationRwaDeleted]
    Seq(
      set(_.id)(v.id),
      set(_.updateDate)(v.updateDate),
      set(_.updateAccountId)(v.updateAccountId),
    )
  }
}
