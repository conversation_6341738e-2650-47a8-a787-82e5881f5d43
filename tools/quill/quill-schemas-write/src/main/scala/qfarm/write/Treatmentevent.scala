package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class Treatmentevent(
  createDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  createAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
  id: DynamicValue[Long] = DynamicValue.Unavailable,
  treatmentId: DynamicValue[Long] = DynamicValue.Unavailable,
  amount: DynamicValue[Option[scala.math.BigDecimal]] = DynamicValue.Unavailable,
  finished: DynamicValue[Boolean] = DynamicValue.Unavailable,
  date: DynamicValue[java.time.LocalDateTime] = DynamicValue.Unavailable,
  comment: DynamicValue[Option[String]] = DynamicValue.Unavailable,
  actorId: DynamicValue[Option[Long]] = DynamicValue.Unavailable,
  actorDate: DynamicValue[Option[java.time.Instant]] = DynamicValue.Unavailable,
)

object Treatmentevent {
  import quill.QuillContext._

  implicit def setTreatmentevent(v: Treatmentevent): Seq[DynamicSet[qfarm.Treatmentevent, _]] = {
    val set = ValueSetter[qfarm.Treatmentevent]
    Seq(
      set(_.createDate)(v.createDate),
      set(_.updateDate)(v.updateDate),
      set(_.createAccountId)(v.createAccountId),
      set(_.updateAccountId)(v.updateAccountId),
      set(_.id)(v.id),
      set(_.treatmentId)(v.treatmentId),
      set(_.amount)(v.amount),
      set(_.finished)(v.finished),
      set(_.date)(v.date),
      set(_.comment)(v.comment),
      set(_.actorId)(v.actorId),
      set(_.actorDate)(v.actorDate),
    )
  }
}
