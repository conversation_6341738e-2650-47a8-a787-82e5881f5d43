package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class SowDeleted(
  id: DynamicValue[Long] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
  sownumber: DynamicValue[Option[String]] = DynamicValue.Unavailable,
)

object SowDeleted {
  import quill.QuillContext._

  implicit def setSowDeleted(v: SowDeleted): Seq[DynamicSet[qfarm.SowDeleted, _]] = {
    val set = ValueSetter[qfarm.SowDeleted]
    Seq(
      set(_.id)(v.id),
      set(_.updateDate)(v.updateDate),
      set(_.updateAccountId)(v.updateAccountId),
      set(_.sownumber)(v.sownumber),
    )
  }
}
