package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class ServingremarksDisabled(
  code: DynamicValue[String] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
)

object ServingremarksDisabled {
  import quill.QuillContext._

  implicit def setServingremarksDisabled(v: ServingremarksDisabled): Seq[DynamicSet[qfarm.ServingremarksDisabled, _]] = {
    val set = ValueSetter[qfarm.ServingremarksDisabled]
    Seq(
      set(_.code)(v.code),
      set(_.updateDate)(v.updateDate),
      set(_.updateAccountId)(v.updateAccountId),
    )
  }
}
