package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class ProgenyTypeDisabled(
  code: DynamicValue[String] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
)

object ProgenyTypeDisabled {
  import quill.QuillContext._

  implicit def setProgenyTypeDisabled(v: ProgenyTypeDisabled): Seq[DynamicSet[qfarm.ProgenyTypeDisabled, _]] = {
    val set = ValueSetter[qfarm.ProgenyTypeDisabled]
    Seq(
      set(_.code)(v.code),
      set(_.updateDate)(v.updateDate),
      set(_.updateAccountId)(v.updateAccountId),
    )
  }
}
