package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class HerdconfigDeleted(
  farmnumber: DynamicValue[String] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
)

object HerdconfigDeleted {
  import quill.QuillContext._

  implicit def setHerdconfigDeleted(v: HerdconfigDeleted): Seq[DynamicSet[qfarm.HerdconfigDeleted, _]] = {
    val set = ValueSetter[qfarm.HerdconfigDeleted]
    Seq(
      set(_.farmnumber)(v.farmnumber),
      set(_.updateDate)(v.updateDate),
    )
  }
}
