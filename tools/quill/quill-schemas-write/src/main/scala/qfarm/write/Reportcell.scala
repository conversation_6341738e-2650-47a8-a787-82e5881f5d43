package qfarm.write
import sjsgrid.shared.form.DynamicValue
import scala.language.implicitConversions

case class Reportcell(
  createDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  updateDate: DynamicValue[java.time.Instant] = DynamicValue.Unavailable,
  createAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
  updateAccountId: DynamicValue[Long] = DynamicValue.Unavailable,
  id: DynamicValue[Long] = DynamicValue.Unavailable,
  reportId: DynamicValue[Long] = DynamicValue.Unavailable,
  reportlinetypeId: DynamicValue[Long] = DynamicValue.Unavailable,
  reportcolumntypeId: DynamicValue[Long] = DynamicValue.Unavailable,
  value: DynamicValue[Option[scala.math.BigDecimal]] = DynamicValue.Unavailable,
  fromdate: DynamicValue[java.time.LocalDate] = DynamicValue.Unavailable,
  todate: DynamicValue[java.time.LocalDate] = DynamicValue.Unavailable,
)

object Reportcell {
  import quill.QuillContext._

  implicit def setReportcell(v: Reportcell): Seq[DynamicSet[qfarm.Reportcell, _]] = {
    val set = ValueSetter[qfarm.Reportcell]
    Seq(
      set(_.createDate)(v.createDate),
      set(_.updateDate)(v.updateDate),
      set(_.createAccountId)(v.createAccountId),
      set(_.updateAccountId)(v.updateAccountId),
      set(_.id)(v.id),
      set(_.reportId)(v.reportId),
      set(_.reportlinetypeId)(v.reportlinetypeId),
      set(_.reportcolumntypeId)(v.reportcolumntypeId),
      set(_.value)(v.value),
      set(_.fromdate)(v.fromdate),
      set(_.todate)(v.todate),
    )
  }
}
