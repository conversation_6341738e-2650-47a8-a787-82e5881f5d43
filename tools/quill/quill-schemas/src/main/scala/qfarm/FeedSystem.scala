package qfarm

case class FeedSystem(
  createDate: java.time.Instant,
  updateDate: java.time.Instant,
  createAccountId: Long,
  updateAccountId: Long,
  id: Long,
  name: String,
  description: Option[String],
  `type`: String,
  locationId: Option[Long],
  inhabitanttypeCode: Option[String],
  configuration: Option[String],
  installationTimestamp: Option[java.time.Instant],
  clientMaxScheduledTalkResponseId: Long,
  disabled: Option[Boolean],
)
