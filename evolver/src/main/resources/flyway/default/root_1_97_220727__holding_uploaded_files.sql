create table if not exists holding_uploaded_files
(
    id           serial primary key,
    filename     varchar(255)  not null,
    filesize     bigint,
    filepath     varchar(4000) not null,
    checksum     varchar(40)   not null,
    storage_type varchar(40) default 'local'::character varying,
    source_type  varchar(40),
    actor_id     bigint,
    metadata     jsonb,
    like common.tracked including all
);
select common.trackable_table(null, 'holding_uploaded_files');
