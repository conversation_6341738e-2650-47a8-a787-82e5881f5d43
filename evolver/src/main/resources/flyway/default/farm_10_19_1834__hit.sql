-- for the HIT entities please consult https://www1.hi-tier.de/Entwicklung/Konzept/_asp/dd00002.asp
--
-- create table if not exists hit_death
-- (
--     farm_centralrn   bigint      not null,
--     production_unit  varchar(50) not null,
--     death_year       int         not null,
--     death_week       int         not null,
--     big_amount       int         not null,
--     small_amount     int,
--     remaining_amount int         not null,
--     constraint hit_death_pkey primary key (farm_centralrn, production_unit, death_year, death_week)
-- );
--
-- create table if not exists hit_movement
-- (
--     farm_centralrn       bigint   not null,
--     other_farm_centralrn bigint   not null,
--     movement_type        smallint not null check (movement_type in (1, 2)),
--     movement_date        date     not null,
--     line_number          int      not null,
--     number_of_animals    int      not null,
--     other_movement_date  date,
--     country_code         char(2),
--     guid                 uuid,
--     constraint hit_movement_pkey primary key (farm_centralrn, other_farm_centralrn, movement_type, movement_date,
--                                               line_number)
-- );
--
-- create table if not exists hit_inventory
-- (
--     farm_centralrn        bigint not null,
--     inventory_date        date   not null,
--     sows                  int    not null,
--     finishers             int    not null,
--     sucklings_and_weaners int,
--     slaughter_finishers   int,
--     constraint hit_inventory_pkey primary key (farm_centralrn, inventory_date)
-- );

create table if not exists hit_change
(
    like common.tracked including all,
    farm_centralrn bigint not null,
    change_date    date   not null,
    change_form    int    not null,
    change_kind    int    not null,
    change_count   int    not null,
    constraint hit_tam_bver_pkey primary key (farm_centralrn, change_date, change_form, change_kind)
);

create table if not exists hit_change_computed
(
    like hit_change,
    constraint hit_change_computed_pkey primary key (farm_centralrn, change_date, change_form, change_kind)
);

create table if not exists hit_change_confirmed
(
    like hit_change,
    constraint hit_change_confirmed_pkey primary key (farm_centralrn, change_date, change_form, change_kind)
);

create table if not exists hit_change_confirmed_deleted
(
    farm_centralrn    bigint                                         not null,
    change_date       date                                           not null,
    change_form       int                                            not null,
    change_kind       int                                            not null,
    update_date       timestamp with time zone default now()         not null,
    update_account_id bigint                   default '-1'::integer not null,
    update_ui         application_type_domain  default 'S'::bpchar,
    constraint hit_change_confirmed_deleted_pkey primary key (farm_centralrn, change_date, change_form, change_kind)
);

create or replace function hit_change_confirmed_created_birtf()
    returns trigger as
$x$
begin
    select current_timestamp
         , get_tenant_account()
         , get_tenant_ui()
         , current_timestamp
         , get_tenant_account()
         , get_tenant_ui()
    into strict new.create_date, new.create_account_id, new.create_ui, new.update_date, new.update_account_id, new.update_ui;
    delete
    from hit_change_confirmed_deleted
    where (farm_centralrn, change_date, change_form, change_kind) =
          (new.farm_centralrn, new.change_date, new.change_form, new.change_kind);
    return new;
end;
$x$
    language plpgsql;

create or replace function hit_change_created_birtf()
    returns trigger as
$x$
begin
    select current_timestamp
         , get_tenant_account()
         , get_tenant_ui()
         , current_timestamp
         , get_tenant_account()
         , get_tenant_ui()
    into strict new.create_date, new.create_account_id, new.create_ui, new.update_date, new.update_account_id, new.update_ui;
    return new;
end;
$x$
    language plpgsql;

drop trigger if exists hit_change_created_bir on hit_change;
create trigger hit_change_created_bir
    before insert
    on hit_change
    for each row execute procedure hit_change_created_birtf();
drop trigger if exists hit_change_created_bir on hit_change_computed;
create trigger hit_change_created_bir
    before insert
    on hit_change_computed
    for each row execute procedure hit_change_created_birtf();

create or replace function hit_change_confirmed_deleted_adrtf()
    returns trigger as
$x$
begin
    insert into hit_change_confirmed_deleted ( farm_centralrn, change_date, change_form, change_kind, update_date
                                             , update_account_id, update_ui)
    select old.farm_centralrn
         , old.change_date
         , old.change_form
         , old.change_kind
         , current_timestamp
         , get_tenant_account()
         , get_tenant_ui();
    return old;
end;
$x$
    language plpgsql;

drop trigger if exists hit_change_confirmed_tracking_bir on hit_change_confirmed;
create trigger hit_change_confirmed_tracking_bir
    before insert
    on hit_change_confirmed
    for each row
execute procedure hit_change_confirmed_created_birtf();

drop trigger if exists hit_change_confirmed_tracking_adr on hit_change_confirmed;
create trigger hit_change_confirmed_tracking_adr
    after delete
    on hit_change_confirmed
    for each row
execute procedure hit_change_confirmed_deleted_adrtf();

drop trigger if exists hit_change_confirmed_tracking_bur on hit_change_confirmed;
create trigger hit_change_confirmed_tracking_bur
    before update
    on hit_change_confirmed
    for each row
execute procedure general_updated_burtf();

drop trigger if exists hit_change_computed_tracking_bur on hit_change_computed;
create trigger hit_change_computed_tracking_bur
    before update
    on hit_change_computed
    for each row
execute procedure general_updated_burtf();

drop trigger if exists hit_change_tracking_bur on hit_change;
create trigger hit_change_tracking_bur
    before update
    on hit_change
    for each row
execute procedure general_updated_burtf();
