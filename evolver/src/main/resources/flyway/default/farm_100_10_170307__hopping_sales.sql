create or replace function hop_gilt_event_aiudr_tf() returns trigger as $$
declare
  new_id bigint;
  old_id bigint;
begin
  case tg_op
    when 'INSERT' then
    new_id := new.gilt_id;
    old_id := null;
    perform 1 from gilt where id = new.gilt_id for update;
    when 'UPDATE' then
    new_id := new.gilt_id;
    old_id := old.gilt_id;
    perform 1 from gilt where id = new.gilt_id or id = old.gilt_id for update;
    when 'DELETE' then
    new_id := null;
    old_id := old.gilt_id;
    perform 1 from gilt where id = old.gilt_id for update;
  end case;

  if new_id is not null or old_id is not null then
    with lasthop as (
        select distinct on (x.id)
          x.id                                              as the_id,
          coalesce(h.actor_date, x.giltdate)                as the_date,
          coalesce(h.to_location_id, x.created_location_id) as the_location_id,
          w.actor_date                                      as weight_date,
          w.weight                                          as weight
        from
          gilt x
          left join hop h on x.id = h.gilt_id
          left join hop w on x.id = w.gilt_id and w.weight is not null
        where x.id in (old_id, new_id)
        order by x.id, h.actor_date desc, h.id desc, w.actor_date desc, w.id desc
    ), ug as (
      update gilt g set
        location_id    = h.the_location_id,
        last_seen_date = h.the_date,
        last_hop_weight = h.weight,
        last_hop_weight_date = h.weight_date
      from lasthop h
      where
        g.id = h.the_id
        and (g.location_id is distinct from h.the_location_id
             or g.last_seen_date is distinct from h.the_date
             or g.last_hop_weight is distinct from h.weight
             or g.last_hop_weight_date is distinct from h.weight_date)
    ) update transferindividualout ti set -- Move the sales location to the last location of the animal. Note, this will change it only if the sale is still a not shpipped
        location_id = h.the_location_id
      from transferout t, lasthop h
      where t.id = ti.transferout_id and t.actor_date is null and t.shipping_date <> h.the_date and ti.gilt_id = h.the_id and (ti.location_id is distinct from h.the_location_id) and tg_op in ('INSERT', 'UPDATE');
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;

create or replace function hop_boar_event_aiudr_tf() returns trigger as $$
declare
  new_id bigint := null;
  old_id bigint := null;
begin
  case tg_op
    when 'INSERT' then
    new_id := new.boar_id;
    old_id := null;
    perform 1 from boar where id = new.boar_id for update;
    when 'UPDATE' then
    new_id := new.boar_id;
    old_id := old.boar_id;
    perform 1 from boar where id = new.boar_id or id = old.boar_id for update;
    when 'DELETE' then
    new_id := null;
    old_id := old.boar_id;
    perform 1 from boar where id = old.boar_id for update;
  end case;

  if new_id is not null or old_id is not null then
    with lasthop as (
        select distinct on (x.id)
          x.id                                              as the_id,
          coalesce(h.actor_date, x.boardate)                as the_date,
          coalesce(h.to_location_id, x.created_location_id) as the_location_id
        from
          boar x
          left join hop h on x.id = h.boar_id
        where x.id in (old_id, new_id)
        order by x.id, h.actor_date desc, h.id desc
    ), ub as (
    update boar b set
      location_id    = h.the_location_id,
      last_seen_date = h.the_date
    from lasthop h
    where
      b.id = h.the_id
      and (b.location_id is distinct from h.the_location_id
           or b.last_seen_date is distinct from h.the_date)
    ) update transferindividualout ti set -- Move the sales location to the last location of the animal. Note, this will change it only if the sale is still not shipped
      location_id = h.the_location_id
    from transferout t, lasthop h
    where t.id = ti.transferout_id and t.actor_date is null and t.shipping_date <> h.the_date and ti.boar_id = h.the_id and (ti.location_id is distinct from h.the_location_id) and tg_op in ('INSERT', 'UPDATE');
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;
