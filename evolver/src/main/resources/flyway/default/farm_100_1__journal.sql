drop trigger if exists transferout_death_aur on transferout;
drop trigger if exists transferout_journal_aiudr on transferout;
drop view if exists journal_view;

alter table transferout drop if exists reservation;
alter table transferindividualout alter location_id set not null;

create trigger transferout_death_aur
    after update of actor_date, livesell
    on transferout
    for each row execute procedure transferout_death_aurtf();

drop trigger if exists transferout_livesell_aucr on transferout;
create constraint trigger transferout_livesell_aucr
    after update of livesell on transferout
    deferrable initially deferred
    for each row
    execute procedure common.update_sow_and_serving_states_tf();

-- Store the journal in the table `JOURNAL`
create or replace function recreate_journal() returns void as $$
begin
  lock table journal in exclusive mode;
  truncate journal;
  insert into journal (location_id, inhabitanttype_code, actor_date, amount, saldoamount, stocktaking)
    select
      location_id, inhabitanttype_code, actor_date, amount, saldoamount, stocktaking from journal_view
    where location_id is not null and actor_date is not null;
end;
$$ language plpgsql set search_path from current;

-- Recomputes the journal's saldos in a specific location, for a specific type from a specific date
create or replace function recompute_journal_saldo(p_fromdate            date, p_location_id bigint,
                                                   p_inhabitanttype_code text)
  returns void as $$
begin
  update journal j
  set amount = x.amounts [1], saldoamount = x.amounts [2] from
    (
      select
        id,
        actor_date,
        location_id,
        inhabitanttype_code,
        stocktaking,
        amount,
        saldoamount,
        journalsum(coalesce(amount, 0) :: integer, coalesce(saldoamount, 0), stocktaking = 1)
        over
        (partition by location_id, inhabitanttype_code
          order by actor_date, stocktaking) amounts
      from
        (select *
         from (select
                 -1                               id,
                 p_fromdate - '1 day' :: interval actor_date,
                 location_id,
                 inhabitanttype_code,
                 1                                stocktaking,
                 0                                amount,
                 saldoamount
               from journal j
               where j.actor_date < p_fromdate and j.location_id = p_location_id and
                     j.inhabitanttype_code = p_inhabitanttype_code
               order by j.actor_date desc, j.stocktaking desc
               limit 1) prev
         union all
         select
           id,
           actor_date,
           location_id,
           inhabitanttype_code,
           stocktaking,
           amount,
           saldoamount
         from journal j
         where j.location_id = p_location_id and j.inhabitanttype_code = p_inhabitanttype_code and
               j.actor_date >= p_fromdate) part
    ) x
  where j.id = x.id and
        (j.amount <> x.amounts [1] or j.saldoamount <> x.amounts [2] or j.amount is null or j.saldoamount is null);
end;

$$ language plpgsql;

-- Marks the journal's day as invalid

create or replace function invalidate_journal_day(p_date date) returns void as $$
begin
  insert into journal_invalid (actor_date) select p_date
                                           where p_date is not null;
end;
$$ language plpgsql set search_path from current;

-- Recomputes the amounts in the journal for one day. The saldos (and stocktaking corrections) are left null and not corrected for the following days.

-- Recomputes the saldos of the journal marked to be recomputed

create or replace function recompute_journal_saldo() returns void as $$
declare
  r record;
begin
  create temporary table if not exists temp_journal_tosaldo (
    actor_date          date,
    location_id         bigint,
    inhabitanttype_code varchar(15)
  ) on commit delete rows;
  for r in select
             location_id, inhabitanttype_code, min(actor_date) actor_date from temp_journal_tosaldo group by
    location_id, inhabitanttype_code loop
    perform recompute_journal_saldo(r.actor_date, r.location_id, r.inhabitanttype_code);
  end loop;
  truncate temp_journal_tosaldo;
end;
$$ language plpgsql set search_path from current;

create or replace function journal_actor_date_aiudr_tf() returns trigger as $$
begin
  if tg_op = 'INSERT' then
    perform invalidate_journal_day((new.actor_date at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  elsif tg_op = 'UPDATE' then
    perform invalidate_journal_day((new.actor_date at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
    if (new.actor_date <> old.actor_date) then
      perform invalidate_journal_day((old.actor_date at time zone o.timezonename) :: date) from organization o where
        id = ${farmId};
    end if;
  elsif tg_op = 'DELETE' then
    perform invalidate_journal_day((old.actor_date at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;


create or replace function transferindividualin_journal_aiudr_tf() returns trigger as $$
declare
  olddate date := null;
  newdate date := null;
begin
  if tg_op in ('INSERT', 'UPDATE') then
    select (t.actor_date at time zone o.timezonename) :: date into newdate
    from organization o, transferin t
    where o.id = ${farmId} and t.id = new.transferin_id;
  end if;
  if tg_op in ('DELETE', 'UPDATE') then
    select (t.actor_date at time zone o.timezonename) :: date into olddate
    from organization o, transferin t
    where o.id = ${farmId} and t.id = old.transferin_id;
  end if;
  if olddate is not null and newdate is distinct from olddate then
    perform invalidate_journal_day(olddate);
  end if;
  if newdate is not null and newdate is distinct from olddate then
    perform invalidate_journal_day(newdate);
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;

create or replace function journal_effectivedate_aiudr_tf() returns trigger as $$
begin
  if tg_op = 'INSERT' then
    perform invalidate_journal_day((new.effectivedate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  elsif tg_op = 'UPDATE' and
        (new.effectivedate <> old.effectivedate or (new.effectivedate is null and old.effectivedate is not null) or
         (new.effectivedate is not null and old.effectivedate is null)) then
    perform invalidate_journal_day((new.effectivedate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
    perform invalidate_journal_day((old.effectivedate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  elsif tg_op = 'DELETE' then
    perform invalidate_journal_day((old.effectivedate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;

create or replace function journal_sowdate_aiudr_tf() returns trigger as $$
begin
  if tg_op = 'INSERT' then
    perform invalidate_journal_day((new.sowdate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  elsif tg_op = 'UPDATE' and (new.sowdate is distinct from old.sowdate or
                                new.created_location_id is distinct from old.created_location_id) then
    perform invalidate_journal_day((new.sowdate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
    if new.sowdate is distinct from old.sowdate then
      perform invalidate_journal_day((old.sowdate at time zone o.timezonename) :: date) from organization o where
        id = ${farmId};
    end if;
  elseif tg_op = 'UPDATE' and new.par1date is distinct from old.par1date then

    -- We need also to update the journal of all the future events, because the sow changed type between PAR0 and SOW (death and movements only)
    perform invalidate_journal_day((old.par1date at time zone o.timezonename) :: date) from organization o where
      o.id = ${farmId} and old.par1date is not null;
    perform invalidate_journal_day((new.par1date at time zone o.timezonename) :: date) from organization o where
      o.id = ${farmId} and new.par1date is not null;
    perform invalidate_journal_day((d.actor_date at time zone o.timezonename) :: date) from dead d, organization o where
      o.id = ${farmId} and d.sow_id = new.id;
    perform invalidate_journal_day((h.actor_date at time zone o.timezonename) :: date) from hop h, organization o where
      o.id = ${farmId} and h.sow_id = new.id and (h.actor_date > new.par1date or h.actor_date > old.par1date);
  elsif tg_op = 'DELETE' then
    perform invalidate_journal_day((old.sowdate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;

create or replace function journal_boardate_aiudr_tf() returns trigger as $$
begin
  if tg_op = 'INSERT' then
    perform invalidate_journal_day((new.boardate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  elsif tg_op = 'UPDATE' and new.boardate <> old.boardate then
    perform invalidate_journal_day((new.boardate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
    perform invalidate_journal_day((old.boardate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  elsif tg_op = 'DELETE' then
    perform invalidate_journal_day((old.boardate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;

create or replace function journal_fromdate_aiudr_tf() returns trigger as $$
begin
  if tg_op = 'INSERT' then
    perform invalidate_journal_day((new.fromdate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  elsif tg_op = 'UPDATE' and (new.fromdate <> old.fromdate or (new.fromdate is null and old.fromdate is not null) or
                                (new.fromdate is not null and old.fromdate is null)) then
    perform invalidate_journal_day((new.fromdate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
    perform invalidate_journal_day((old.fromdate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  elsif tg_op = 'DELETE' then
    perform invalidate_journal_day((old.fromdate at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;

drop trigger if exists dead_journal_aiudr on dead;
create trigger dead_journal_aiudr
after insert or delete or update of actor_date, sow_id, boar_id, gilt_id, serving_id, location_id, amount
on dead
for each row execute procedure journal_actor_date_aiudr_tf();


drop trigger if exists stocktakingday_journal_aiudr on stocktakingday;
create trigger stocktakingday_journal_aiudr
after insert or delete or update of effectivedate
on stocktakingday
for each row execute procedure journal_effectivedate_aiudr_tf();

create or replace function journal_stocktaking_aiudr_tf() returns trigger as $$
begin
  if tg_op in ('INSERT', 'UPDATE') then
    perform invalidate_journal_day((t.effectivedate at time zone o.timezonename) :: date) from
      organization o, stocktakingday t where o.id = ${farmId} and t.id = new.stocktakingday_id;
  end if;
  if tg_op in ('DELETE', 'UPDATE') then
    perform invalidate_journal_day((t.effectivedate at time zone o.timezonename) :: date) from
      organization o, stocktakingday t where o.id = ${farmId} and t.id = old.stocktakingday_id;
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;

drop trigger if exists stocktaking_journal_aiudr on stocktaking;
create trigger stocktaking_journal_aiudr
after insert or delete or update of stocktakingday_id, amount
on stocktaking
for each row execute procedure journal_stocktaking_aiudr_tf();

create or replace function journal_serving_aiudr_tf() returns trigger as $$
declare
  x record;
begin
  if tg_op in ('INSERT', 'UPDATE') then
    perform invalidate_journal_day((new.farrow_endtime at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
    for x in select distinct (w.actor_date at time zone o.timezonename) :: date weaning_date
             from weaned w, organization o
             where w.serving_id = new.id and o.id = ${farmId} loop
      perform invalidate_journal_day(x.weaning_date);
    end loop;
  end if;
  if tg_op in ('DELETE', 'UPDATE') then
    perform invalidate_journal_day((old.farrow_endtime at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;

drop trigger if exists serving_journal_aiudr on serving;
create trigger serving_journal_aiudr
after insert or delete or update of farrow_location_id, farrow_endtime, liveborn, actor_date
on serving
for each row execute procedure journal_serving_aiudr_tf();

-- Table PIGQUALITY
create or replace function journal_pigqualityout_aiudr_tf() returns trigger as $$
begin
  if tg_op in ('INSERT', 'UPDATE') then
    perform invalidate_journal_day((t.actor_date at time zone o.timezonename) :: date) from
      organization o, transferout t where o.id = ${farmId} and t.id = new.transferout_id;
  end if;
  if tg_op in ('DELETE', 'UPDATE') then
    perform invalidate_journal_day((t.actor_date at time zone o.timezonename) :: date) from
      organization o, transferout t where o.id = ${farmId} and t.id = old.transferout_id;
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;

drop trigger if exists pigqualityout_journal_aiudr on pigqualityout;
create trigger pigqualityout_journal_aiudr
after insert or delete or update of transferout_id, pigamount, location_id
on pigqualityout
for each row execute procedure journal_pigqualityout_aiudr_tf();

create or replace function journal_pigqualityin_aiudr_tf() returns trigger as $$
begin
  if tg_op in ('INSERT', 'UPDATE') then
    perform invalidate_journal_day((t.actor_date at time zone o.timezonename) :: date) from
      organization o, transferin t where o.id = ${farmId} and t.id = new.transferin_id;
  end if;
  if tg_op in ('DELETE', 'UPDATE') then
    perform invalidate_journal_day((t.actor_date at time zone o.timezonename) :: date) from
      organization o, transferin t where o.id = ${farmId} and t.id = old.transferin_id;
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;

drop trigger if exists pigqualityin_journal_aiudr on pigqualityin;
create trigger pigqualityin_journal_aiudr
after insert or delete or update of transferin_id, pigamount, location_id
on pigqualityin
for each row execute procedure journal_pigqualityin_aiudr_tf();

create or replace function journal_transfer_aiudr_tf() returns trigger as $$
begin
  if tg_op in ('INSERT', 'UPDATE') then
    perform invalidate_journal_day((new.actor_date at time zone o.timezonename) :: date) from organization o where
      o.id = ${farmId};
  end if;
  if tg_op in ('DELETE', 'UPDATE') then
    perform invalidate_journal_day((old.actor_date at time zone o.timezonename) :: date) from organization o where
      o.id = ${farmId};
  end if;
  return null;
end
$$ language plpgsql set search_path from current;

drop trigger if exists transferin_journal_aiudr on transferin;
create trigger transferin_journal_aiudr
after insert or delete or update of actor_date
on transferin
for each row execute procedure journal_transfer_aiudr_tf();

drop trigger if exists transferout_journal_aiudr on transferout;
create trigger transferout_journal_aiudr
after insert or delete or update of actor_date
on transferout
for each row execute procedure journal_transfer_aiudr_tf();

drop trigger if exists transferloc_journal_aiudr on transferloc;
create trigger transferloc_journal_aiudr
after insert or delete or update of actor_date
on transferloc
for each row execute procedure journal_transfer_aiudr_tf();

create or replace function journal_pigqualityloc_aiudr_tf() returns trigger as $$
begin
  if tg_op in ('INSERT', 'UPDATE') then
    perform invalidate_journal_day((t.actor_date at time zone o.timezonename) :: date) from
      organization o, transferloc t where o.id = ${farmId} and t.id = new.transferloc_id;
  end if;
  if tg_op in ('DELETE', 'UPDATE') then
    perform invalidate_journal_day((t.actor_date at time zone o.timezonename) :: date) from
      organization o, transferloc t where o.id = ${farmId} and t.id = old.transferloc_id;
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;

drop trigger if exists pigqualityloc_journal_aiudr on pigqualityloc;
create trigger pigqualityloc_journal_aiudr
after insert or delete or update of transferloc_id, pigamount, fromlocation_id, tolocation_id
on pigqualityloc
for each row execute procedure journal_pigqualityloc_aiudr_tf();

drop trigger if exists transferindividualin_journal_aiudr on transferindividualin;
create trigger transferindividualin_journal_aiudr
after insert or delete or update of transferin_id, gilt_id, location_id
on transferindividualin
for each row execute procedure transferindividualin_journal_aiudr_tf();

drop trigger if exists weaned_journal_aiudr on weaned;
create trigger weaned_journal_aiudr
after insert or delete or update of actor_date, serving_id, amount, location_id
on weaned
for each row execute procedure journal_actor_date_aiudr_tf();

drop trigger if exists fostering_journal_aiudr on fostering;
create trigger fostering_journal_aiudr
after insert or delete or update of actor_date, amount, from_location_id, to_location_id
on fostering
for each row execute procedure journal_actor_date_aiudr_tf();

create or replace function journal_hop_aiudr_tf() returns trigger as $$
begin
  if tg_op = 'INSERT' then
    perform invalidate_journal_day((new.actor_date at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
    perform invalidate_journal_day((h.actor_date at time zone o.timezonename) :: date) from organization o cross join hop h
      where o.id = ${farmId} and (new.sow_id, new.gilt_id, new.boar_id) is not distinct from (h.sow_id, h.gilt_id, h.boar_id) and (h.actor_date, h.id) > (new.actor_date, new.id) order by h.actor_date, h.id limit 1;
  elsif tg_op = 'UPDATE' and ((new.actor_date, new.to_location_id, new.sow_id, new.gilt_id, new.boar_id) is distinct from (old.actor_date, old.to_location_id, old.sow_id, old.gilt_id, old.boar_id)) then
    perform invalidate_journal_day((new.actor_date at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
    if new.actor_date is distinct from old.actor_date then
      perform invalidate_journal_day((old.actor_date at time zone o.timezonename) :: date) from organization o where
        id = ${farmId};
    end if;
    perform invalidate_journal_day((h.actor_date at time zone o.timezonename) :: date) from organization o cross join hop h
    where o.id = ${farmId} and (old.sow_id, old.gilt_id, old.boar_id) is not distinct from (h.sow_id, h.gilt_id, h.boar_id) and (h.actor_date, h.id) > (old.actor_date, old.id) order by h.actor_date, h.id limit 1;
    perform invalidate_journal_day((h.actor_date at time zone o.timezonename) :: date) from organization o cross join hop h
    where o.id = ${farmId} and (new.sow_id, new.gilt_id, new.boar_id) is not distinct from (h.sow_id, h.gilt_id, h.boar_id) and (h.actor_date, h.id) > (new.actor_date, new.id) order by h.actor_date, h.id limit 1;
  elsif tg_op = 'DELETE' then
    perform invalidate_journal_day((old.actor_date at time zone o.timezonename) :: date) from organization o where
      id = ${farmId};
    perform invalidate_journal_day((h.actor_date at time zone o.timezonename) :: date) from organization o cross join hop h
    where o.id = ${farmId} and (old.sow_id, old.gilt_id, old.boar_id) is not distinct from (h.sow_id, h.gilt_id, h.boar_id) and (h.actor_date, h.id) > (old.actor_date, old.id) order by h.actor_date, h.id limit 1;
  end if;
  return null;
end;
$$ language plpgsql set search_path from current;


drop trigger if exists hop_journal_aiudr on hop;
create trigger hop_journal_aiudr
after insert or delete or update
on hop
for each row execute procedure journal_hop_aiudr_tf();

drop trigger if exists sow_journal_aiudr on sow;
create trigger sow_journal_aiudr
after insert or delete or update of sowdate, created_location_id, par1date, par1location_id
on sow
for each row execute procedure journal_sowdate_aiudr_tf();

drop trigger if exists boar_journal_aiudr on boar;
create trigger boar_journal_aiudr
after insert or delete or update of boardate, created_location_id
on boar
for each row execute procedure journal_boardate_aiudr_tf();

drop trigger if exists pigltrans_journal_aiudr on pigltrans;
create trigger pigltrans_journal_aiudr
  after insert or delete or update of actor_date, from_location_id, to_location_id, amount
  on pigltrans
  for each row execute procedure journal_actor_date_aiudr_tf();

create or replace view journal_view as
  with par as (
      select
             get_setting('ind_gilt_selling_ignored') is not distinct from 'true' gsignored,
             get_setting('ind_gilt_death_ignored') is not distinct from 'true' gdignored,
             not(get_setting('pigl_tracking'::text) is distinct from 'true'::text) AS pigltracking,
             o.timezonename tz from organization o
      where
            o.id = ${farmId}
  ), explicitstockdate as (
      select sd.effectivedate  as effectivedate
      from stocktakingday sd
  ), eloc as (
      select e.location_id, e.actor_date as effectivedate
      from empty_location e join location l on l.id = e.location_id and l.id = l.journaling_id
  ), gilthop as (select hn.id
                      , g.id                                        gilt_id
                      , (hn.actor_date at time zone par.tz) :: date actor_date
                      , lf.journaling_id                            from_location_id
                      , lf.fornamed                                 from_location_fornamed
                      , case
                            when lf.inhabitanttype_code = 'SOW' and
                                 hn.actor_date - g.birthdate < '50 days' :: interval then 'PIGL'
                            when lf.inhabitanttype_code = 'SOW' then 'GILT'
                            else lf.inhabitanttype_code end         from_inh_type
                      , lt.journaling_id                            to_location_id
                      , case
                            when lt.inhabitanttype_code = 'SOW' and
                                 hn.actor_date - g.birthdate < '50 days' :: interval
                                then 'PIGL'
                            when lt.inhabitanttype_code = 'SOW' then 'GILT'
                            else lt.inhabitanttype_code end         to_inh_type
                      , case
                            when coalesce(d.effectivedate, elf.effectivedate) is null or
                                 coalesce(d.effectivedate, elf.effectivedate) >= hn.actor_date then 0
                            else 2 end                              stocktaking_from
                      , case
                            when coalesce(d.effectivedate, elt.effectivedate) is null or
                                 coalesce(d.effectivedate, elt.effectivedate) >= hn.actor_date then 0
                            else 2 end                              stocktaking_to
                 from par
                          cross join gilt g
                          inner join hop hn on hn.gilt_id = g.id
                          join location lf on lf.id = hn.from_location_id
                          join location lt on lt.id = hn.to_location_id
                          left join explicitstockdate d on (hn.actor_date at time zone par.tz) :: date =
                                                           (d.effectivedate at time zone par.tz) :: date
                          left join eloc elf on lf.journaling_id = elf.location_id and
                                                (hn.actor_date at time zone par.tz) :: date =
                                                (elf.effectivedate at time zone par.tz) :: date
                          left join eloc elt on lt.journaling_id = elt.location_id and
                                                (hn.actor_date at time zone par.tz) :: date =
                                                (elt.effectivedate at time zone par.tz) :: date
  ), x_dead_sows as (
      select
             (q.actor_date at time zone par.tz) :: date actor_date,
             l.journaling_id as location_id,
             case when q.actor_date >= s.par1date then 'SOW' :: text else 'PAR0' :: text end inhabitanttype_code,
             -sum(q.amount) :: integer amount, null :: integer saldoamount,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
      from par cross join dead q inner join location l on l.id = q.location_id and l.inhabitanttype_code in ('SOW','GILT')
               inner join sow s on q.sow_id = s.id
               left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
               left join eloc el on el.location_id = l.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
      group by
               date(q.actor_date at time zone par.tz), l.journaling_id,
               case when q.actor_date >= s.par1date then 'SOW' :: text else 'PAR0' :: text end,
               case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
  ), x_moved_gilts as (
    select
           actor_date, from_location_id, from_inh_type, -count(id) amount, null :: integer saldoamount, stocktaking_from as stocktaking
    from gilthop where from_location_fornamed
    group by actor_date, from_location_id, from_inh_type, stocktaking_from
    union all
    select
           actor_date, to_location_id, to_inh_type, +count(id) amount, null :: integer saldoamount, stocktaking_to as stocktaking
    from gilthop where from_location_fornamed
    group by actor_date, to_location_id, to_inh_type, stocktaking_to
  ), x_dead_boars as (
      select
             (q.actor_date at time zone par.tz) :: date actor_date, l.journaling_id, 'BOAR' :: text inhabitanttype_code,
             -sum(q.amount) amount, null :: integer saldoamount,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
      from par cross join dead q inner join location l on l.id = q.location_id and l.inhabitanttype_code = 'BOAR'
               left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
               left join eloc el on el.location_id = l.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
      where boar_id is not null
      group by
               (q.actor_date at time zone par.tz):: date, l.journaling_id, case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
  ), x_dead_piglets as (
      select
             (q.actor_date at time zone par.tz) :: date actor_date,
             l.journaling_id,
             'PIGL' :: text inhabitanttype_code,
             -sum(q.amount) amount,
             null :: integer saldoamount,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
      from par cross join dead q
               inner join serving s on s.id = q.serving_id
               inner join location l on l.id = case
                                                    when par.pigltracking then q.location_id
                                                    else s.farrow_location_id
              end
               left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
               left join eloc el on el.location_id = l.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
      where serving_id is not null and s.farrow_location_id is not null
      group by (q.actor_date at time zone par.tz) :: date,
               l.journaling_id,
               case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
  ), x_dead_gilts as (
      select
             (q.actor_date at time zone par.tz) :: date actor_date,
             l.journaling_id,
             case
                  when h.inhabitanttype_code = 'SOW' and q.actor_date - s.farrow_endtime < '50 days' :: interval then 'PIGL'
                  when h.inhabitanttype_code = 'SOW' then 'GILT'
                  else h.inhabitanttype_code end :: text inhabitanttype_code,
             -sum(q.amount) amount,
             null :: integer saldoamount,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
      from par cross join dead q
               inner join location h on h.id = q.location_id
               inner join gilt g on q.gilt_id = g.id
               left join serving s on g.serving_id = s.id
               left join location l on l.id = case
                                                   when h.inhabitanttype_code = 'SOW' and q.actor_date - s.farrow_endtime < '50 days' :: interval and not par.pigltracking then s.farrow_location_id
                                                   else q.location_id
              end
               left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
               left join eloc el on el.location_id = l.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
      where q.gilt_id is not null and q.serving_id is null and
              q.deathtype_code is not null and not par.gdignored
      group by (q.actor_date at time zone par.tz) :: date, l.journaling_id, h.inhabitanttype_code,
               case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end,
               case
                    when h.inhabitanttype_code = 'SOW' and q.actor_date - s.farrow_endtime < '50 days' :: interval then 'PIGL'
                    when h.inhabitanttype_code = 'SOW' then 'GILT'
                    else h.inhabitanttype_code end :: text
  ), x_sold_gilts as (
      select
             (k.actor_date at time zone par.tz) :: date actor_date,
             l.journaling_id location_id,
             case
                  when h.inhabitanttype_code = 'SOW' and k.actor_date - s.farrow_endtime < '50 days' :: interval then 'PIGL'
                  when h.inhabitanttype_code = 'SOW' then 'GILT'
                  else h.inhabitanttype_code end :: text inhabitanttype_code,
             -count(tio.id) amount,
             null :: integer saldoamount,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= k.actor_date then 0 else 2 end stocktaking
      from par cross join transferindividualout tio
               inner join location h on h.id = tio.location_id
               inner join gilt g on tio.gilt_id = g.id
               inner join transferout k on tio.transferout_id = k.id
               left join serving s on g.serving_id = s.id
               left join location l on l.id = case
                                                   when h.inhabitanttype_code = 'SOW' and k.actor_date - s.farrow_endtime < '50 days' :: interval and not par.pigltracking then s.farrow_location_id
                                                   else tio.location_id
              end
               left join explicitstockdate t on (k.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
               left join eloc el on el.location_id = l.journaling_id and (k.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
      where k.actor_date is not null and not par.gsignored
      group by (k.actor_date at time zone par.tz) :: date, l.journaling_id, h.inhabitanttype_code,
               case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= k.actor_date then 0 else 2 end, s.farrow_location_id, k.actor_date - s.farrow_endtime < '50 days' :: interval
  ), x_dead_groups as (
      select
             (q.actor_date at time zone par.tz) :: date actor_date, h.journaling_id,
             case
                  when h.inhabitanttype_code = 'SOW' then 'GILT'
                  else h.inhabitanttype_code
                 end inhabitanttype_code,
             -sum(q.amount) amount,
             null :: integer saldoamount,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
      from par cross join dead q
               inner join location h on h.id = q.location_id
               left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date =
                                                (t.effectivedate at time zone
                                                 par.tz) :: date
               left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
      where sow_id is null and boar_id is null and serving_id is null and gilt_id is null
      group by (q.actor_date at time zone par.tz) :: date, h.journaling_id, h.inhabitanttype_code,
               case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
  ), x_moved_groups as (
    select (r.actor_date at time zone par.tz) :: date actor_date,
           h.journaling_id,
           case when h.inhabitanttype_code = 'SOW' then 'GILT' else h.inhabitanttype_code end inhabitanttype_code,
           sum(-q.pigamount) amount, null :: integer saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
    from par cross join pigqualityout q
             inner join transferout r on r.id = q.transferout_id and r.actor_date is not null
             inner join location h on h.id = q.location_id
             left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
                                              (t.effectivedate at time zone
                                               par.tz) :: date
             left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    where q.pigamount is not null
    group by (r.actor_date at time zone par.tz) :: date, h.journaling_id,
             h.inhabitanttype_code,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end

    union all
    select (r.actor_date at time zone par.tz) :: date actor_date, h.journaling_id,
           case when h.inhabitanttype_code = 'SOW' then 'GILT' else h.inhabitanttype_code end inhabitanttype_code,
           sum(q.pigamount) amount, null :: integer saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end
    from par cross join pigqualityin q
             inner join transferin r on r.id = q.transferin_id
             inner join location h on h.id = q.location_id
             left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
                                              (t.effectivedate at time zone
                                               par.tz) :: date
             left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    where q.pigamount is not null
    group by (r.actor_date at time zone par.tz) :: date, h.journaling_id, h.inhabitanttype_code,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end

    union all
    select
           (r.actor_date at time zone par.tz) :: date actor_date, h.journaling_id,
           case when h.inhabitanttype_code = 'SOW' then 'GILT' else h.inhabitanttype_code end inhabitanttype_code,
           sum(-q.pigamount) amount,
           null :: integer saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
    from par cross join pigqualityloc q
             inner join transferloc r on r.id = q.transferloc_id
             inner join location h on h.id = q.fromlocation_id
             left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
                                              (t.effectivedate at time zone
                                               par.tz) :: date
             left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    where q.pigamount is not null
    group by (r.actor_date at time zone par.tz) :: date, h.journaling_id, h.inhabitanttype_code,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end
    union all
    select (r.actor_date at time zone par.tz) :: date actor_date, h.journaling_id,
           case when h.inhabitanttype_code = 'SOW' then 'GILT' else h.inhabitanttype_code end inhabitanttype_code,
           sum(q.pigamount) amount,

           null :: integer saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
    from par cross join pigqualityloc q
             inner join transferloc r on r.id = q.transferloc_id
             inner join location h on h.id = q.tolocation_id
             left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
                                              (t.effectivedate at time zone
                                               par.tz) :: date
             left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    where q.pigamount is not null
    group by (r.actor_date at time zone par.tz) :: date, h.journaling_id, h.inhabitanttype_code,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end
  ), x_fostering as (
    select
           (r.actor_date at time zone par.tz) :: date actor_date,
           h.journaling_id, 'PIGL' inhabitanttype_code,
           sum(-r.amount) amount, null :: integer saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
    from par cross join fostering r
             join location h on h.id = r.from_location_id
             left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
                                              (t.effectivedate at time zone
                                               par.tz) :: date
             left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    group by (r.actor_date at time zone par.tz) :: date, h.journaling_id,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end

    union all

    select
           (r.actor_date at time zone par.tz) :: date actor_date,
           h.journaling_id, 'PIGL' inhabitanttype_code,
           sum(r.amount) amount, null :: integer saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
    from par cross join fostering r
             join location h on h.id = r.to_location_id
             left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
                                              (t.effectivedate at time zone
                                               par.tz) :: date
             left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    group by (r.actor_date at time zone par.tz) :: date, h.journaling_id,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end

  ), x_bought_gilts as (
      select
             (r.actor_date at time zone par.tz) :: date actor_date,
             h.journaling_id,
             case when h.inhabitanttype_code = 'SOW' then 'GILT' else h.inhabitanttype_code end inhabitanttype_code,
             count(q.gilt_id) amount, null :: integer saldoamount,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
      from par cross join transferindividualin q
               inner join transferin r on q.transferin_id = r.id
               inner join location h on h.id = q.location_id
               left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
                                                (t.effectivedate at time zone
                                                 par.tz) :: date
               left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
      where q.gilt_id is not null
      group by (r.actor_date at time zone par.tz) :: date, h.journaling_id,
               case when h.inhabitanttype_code = 'SOW' then 'GILT' else h.inhabitanttype_code end,
               case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end
  ), x_bought_boars as (
      select
             (r.actor_date at time zone par.tz) :: date actor_date, h.journaling_id, h.inhabitanttype_code,
             count(q.boar_id) amount, null :: integer saldoamount,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
      from par cross join transferindividualin q
               inner join transferin r on q.transferin_id = r.id
               inner join location h on h.id = q.location_id
               left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
                                                (t.effectivedate at time zone
                                                 par.tz) :: date
               left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
      where q.boar_id is not null
      group by (r.actor_date at time zone par.tz) :: date, h.journaling_id, h.inhabitanttype_code,
               case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end
  ), x_bought_sows as (
      select
             (r.actor_date at time zone par.tz) :: date actor_date,
             h.journaling_id,
             case when r.actor_date >= s.par1date then 'SOW' else 'PAR0' end inhabitanttype_code,
             count(q.sow_id) amount, null :: integer saldoamount,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
      from par cross join transferindividualin q
               inner join transferin r on q.transferin_id = r.id
               inner join sow s on q.sow_id = s.id
               inner join location h on h.id = q.location_id
               left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
               left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
      where q.sow_id is not null
      group by (r.actor_date at time zone par.tz) :: date, h.journaling_id, case when r.actor_date >= s.par1date then 'SOW' else 'PAR0' end,
               case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end
  ), x_stocktaking as (
      select
             (t.effectivedate at time zone par.tz) :: date, s.location_id, s.inhabitanttype_code, null :: integer amount,
             s.amount saldoamount, 1 stocktaking
      from par cross join
               stocktakingday t inner join stocktaking s on t.id = s.stocktakingday_id
  ), x_emptyloc as (
      select
          distinct (e.effectivedate at time zone par.tz) :: date, e.location_id, l.inhabitanttype_code, null :: integer amount,
                      0 as saldoamount, 1 stocktaking
      from par cross join eloc e
               inner join location l on l.journaling_id = e.location_id
               left join stocktakingday sd on (e.effectivedate at time zone par.tz) :: date = (sd.effectivedate at time zone par.tz) :: date
               left join stocktaking st on st.stocktakingday_id = sd.id and st.location_id = e.location_id and st.inhabitanttype_code = l.inhabitanttype_code
      where st.id is null

  ), weanedfrom AS (
      SELECT DISTINCT ON (w.id) w.create_date,
             w.update_date,
             w.create_account_id,
             w.update_account_id,
             w.create_ui,
             w.update_ui,
             w.id,
             w.serving_id,
             null::numeric as weight,
             w.amount,
             w.comment,
             w.actor_id,
             w.actor_date,
             w.nursery,
             w.location_id,
             w.sow_location_id,
             COALESCE(ph.to_location_id, f.farrow_location_id) AS from_location_id
      FROM par
             CROSS JOIN weaned w
             JOIN hop wh ON wh.weaned_id = w.id
             JOIN hop fh ON fh.farrowing_id = w.serving_id
             JOIN serving f ON f.id = w.serving_id
             LEFT JOIN hop ph ON ph.sow_id = f.sow_id AND (ROW(fh.actor_date, fh.id) < ROW(ph.actor_date, ph.id)) AND (ROW(ph.actor_date, ph.id) < ROW(wh.actor_date, wh.id)) AND par.pigltracking
      ORDER BY w.id, ph.actor_date DESC, ph.id DESC
  ), x_weanings as (
    select
           (q.actor_date at time zone par.tz) :: date,
           wl.journaling_id,
           wl.inhabitanttype_code,
           sum(q.amount)   amount,
           null :: integer saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
    from par
           cross join weaned q
           inner join location wl on q.location_id = wl.id
           left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date =
                                            (t.effectivedate at time zone
                                             par.tz) :: date
           left join eloc el on el.location_id = wl.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    where q.amount is not null
    group by (q.actor_date at time zone par.tz) :: date, wl.journaling_id,
             wl.inhabitanttype_code,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
    --- Weanings - removing PIGLs
    union all
    select
           (q.actor_date at time zone par.tz) :: date,
           h.journaling_id,
           'PIGL' :: character varying,
           (-sum(q.amount))                                   as amount,
           null :: integer                                    as saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end as stocktaking
    from par
           cross join weanedfrom q
           join location h on h.id = q.from_location_id
           left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
           left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    where (q.amount is not null)
    group by q.actor_date at time zone par.tz, h.journaling_id,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
  ), x_moved_piglets as (
    select
           (q.actor_date at time zone par.tz) :: date,
           h.journaling_id,
           'PIGL'::character varying,
           (-sum(q.amount)) as amount,
           null ::integer as saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end as stocktaking
    from par cross join pigltrans q
             join location h on h.id = q.from_location_id
             left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
             left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    where par.pigltracking
    group by (q.actor_date at time zone par.tz) :: date, h.journaling_id,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
    union all
    select
           (q.actor_date at time zone par.tz) :: date,
           h.journaling_id,
           'PIGL'::character varying,
           (sum(q.amount)) as amount,
           null ::integer as saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end as stocktaking
    from par cross join pigltrans q
             join location h on h.id = q.to_location_id
             left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
             left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    where par.pigltracking
    group by (q.actor_date at time zone par.tz) :: date, h.journaling_id,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end

  ), x_farrowings as (
      select
             (q.farrow_endtime at time zone par.tz) :: date, l.journaling_id, 'PIGL'::text, sum(q.liveborn) amount, null :: integer saldoamount,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.farrow_endtime then 0 else 2 end as stocktaking
      from par cross join serving q
               inner join location l on l.id = q.farrow_location_id
               left join explicitstockdate t on (q.farrow_endtime at time zone par.tz) :: date =
                                                (t.effectivedate at time zone par.tz) :: date
               left join eloc el on el.location_id = l.journaling_id and (q.farrow_endtime at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
      where q.liveborn is not null and q.farrow_endtime is not null
      group by (q.farrow_endtime at time zone par.tz) :: date, l.journaling_id,
               case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.farrow_endtime then 0 else 2 end
  ), x_moved_sows as (
    select * from (select distinct on (q.id)
                       (q.actor_date at time zone par.tz) :: date actor_date,
                          h.journaling_id,
                          case when h.inhabitanttype_code not in ('SOW','GILT') then h.inhabitanttype_code
                               when q.actor_date > s.par1date then 'SOW' :: text
                               else 'PAR0' :: text end inhabitanttype_code, -1 :: integer amount, null :: integer saldoamount,
                          case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
                   from par cross join hop q
                            inner join sow s on q.sow_id = s.id
                            left join hop qq on q.sow_id = qq.sow_id and
                                                (q.actor_date > qq.actor_date or
                                                 q.actor_date = qq.actor_date and
                                                 q.id > qq.id)
                            left join location h on h.id = coalesce(qq.to_location_id, s.created_location_id)
                            left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date =
                                                             (t.effectivedate at time zone par.tz) :: date
                            left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
                   where q.sow_id is not null
                   order by q.id, qq.actor_date desc, qq.id desc) xx
    union all

    select
           (q.actor_date at time zone par.tz) :: date actor_date, l.journaling_id,
           case
               when l.inhabitanttype_code not in ('SOW','GILT') then l.inhabitanttype_code
               when q.actor_date > s.par1date then 'SOW' :: text
               else 'PAR0' :: text
               end inhabitanttype_code,
           count(q.id) :: integer amount, null :: integer saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
    from par cross join hop q inner join location l on l.id = q.to_location_id
             inner join sow s on q.sow_id = s.id
             left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date =
                                              (t.effectivedate at time zone
                                               par.tz) :: date
             left join eloc el on el.location_id = l.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    group by (q.actor_date at time zone par.tz) :: date, l.journaling_id,
        case
            when l.inhabitanttype_code not in ('SOW','GILT') then l.inhabitanttype_code
            when q.actor_date > s.par1date then 'SOW' :: text
            else 'PAR0' :: text
            end,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
  ), x_moved_boars as (
    select * from (select distinct on (q.id)
                       (q.actor_date at time zone par.tz) :: date actor_date, h.journaling_id,
                          h.inhabitanttype_code, -1 :: integer amount, null :: integer saldoamount,
                          case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
                   from par cross join hop q
                            inner join boar s on q.boar_id = s.id
                            left join hop qq on q.boar_id = qq.boar_id and
                                                (q.actor_date > qq.actor_date or
                                                 q.actor_date = qq.actor_date and
                                                 q.id > qq.id)
                            left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date =
                                                             (t.effectivedate at time zone par.tz) :: date
                            left join location h on coalesce(qq.to_location_id, s.created_location_id) = h.id
                            left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
                   where q.boar_id is not null and h.id is not null
                   order by q.id, qq.actor_date desc, qq.id desc) xx
    union all

    select
           (q.actor_date at time zone par.tz) :: date actor_date, h.journaling_id, h.inhabitanttype_code,
           count(q.id) :: integer amount, null :: integer saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
    from par cross join hop q
             inner join location h on q.to_location_id = h.id
             left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date =
                                              (t.effectivedate at time zone
                                               par.tz) :: date
             left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    where boar_id is not null
    group by (q.actor_date at time zone par.tz) :: date, h.journaling_id, h.inhabitanttype_code,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
  ), x_par0 as (
    select
           (q.sowdate at time zone par.tz) :: date actor_date, h.journaling_id, 'PAR0' :: text inhabitanttype_code,
           count(q.id) :: integer amount, null :: integer saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.sowdate then 0 else 2 end stocktaking
    from par cross join sow q
             join location h on h.id = q.created_location_id
             left join explicitstockdate t on (q.sowdate at time zone par.tz) :: date =
                                              (t.effectivedate at time zone
                                               par.tz) :: date
             left join eloc el on el.location_id = h.journaling_id and (q.sowdate at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
             left join transferindividualin ti on ti.sow_id = q.id
    where sowdate is not null and ti.id is null
    group by (q.sowdate at time zone par.tz) :: date, h.journaling_id,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.sowdate then 0 else 2 end
    union all
    select
           (q.sowdate at time zone par.tz) :: date actor_date, h.journaling_id, 'GILT' :: text inhabitanttype_code,
           -count(q.id) :: integer amount, null :: integer saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.sowdate then 0 else 2 end stocktaking
    from par cross join sow q
             join location h on h.id = q.created_location_id
             left join explicitstockdate t on (q.sowdate at time zone par.tz) :: date =
                                              (t.effectivedate at time zone
                                               par.tz) :: date
             left join eloc el on el.location_id = h.journaling_id and (q.sowdate at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
             left join transferindividualin ti on ti.sow_id = q.id
    where sowdate is not null and ti.id is null
    group by (q.sowdate at time zone par.tz) :: date, h.journaling_id,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.sowdate then 0 else 2 end
  ), x_par1 as (
    select
           (q.par1date at time zone par.tz) :: date actor_date, h.journaling_id, 'SOW' :: text inhabitanttype_code,
           count(q.id) :: integer amount, null :: integer saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.par1date then 0 else 2 end stocktaking
    from par cross join sow q
             join location h on h.id = q.par1location_id
             left join explicitstockdate t on (q.par1date at time zone par.tz) :: date =
                                              (t.effectivedate at time zone
                                               par.tz) :: date
             left join eloc el on el.location_id = h.journaling_id and (q.par1date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    where par1date is not null
    group by (q.par1date at time zone par.tz) :: date, h.journaling_id,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.par1date then 0 else 2 end

    -- Removing farrowing parity 0 sows (par1date)

    union all
    select
           (q.par1date at time zone par.tz) :: date actor_date, h.journaling_id, 'PAR0' :: text inhabitanttype_code,
           -count(q.id) :: integer amount, null :: integer saldoamount,
           case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.par1date then 0 else 2 end stocktaking
    from par cross join sow q
             join location h on h.id = q.par1location_id
             left join explicitstockdate t on (q.par1date at time zone par.tz) :: date =
                                              (t.effectivedate at time zone
                                               par.tz) :: date
             left join eloc el on el.location_id = h.journaling_id and (q.par1date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
    where par1date is not null
    group by (q.par1date at time zone par.tz) :: date, h.journaling_id,
             case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.par1date then 0 else 2 end
  ), basic as (
    select * from x_dead_sows
    union all
    select * from x_moved_gilts
    union all
    select * from x_dead_boars
    union all
    select * from x_dead_piglets
    union all
    select * from x_dead_gilts
    union all
    select * from x_sold_gilts
    union all
    select * from x_dead_groups
    union all
    select * from x_moved_groups
    union all
    select * from x_fostering
    union all
    select * from x_bought_gilts
    union all
    select * from x_bought_boars
    union all
    select * from x_bought_sows
    union all
    select * from x_stocktaking
    union all
    select * from x_emptyloc
    union all
    select * from x_weanings
    union all
    select * from x_moved_sows
    union all
    select * from x_moved_boars
    union all
    select * from x_moved_piglets
    union all
    select * from x_par0
    union all
    select * from x_par1
    union all
    select * from x_farrowings
  ), daygroups as (
      select
             b.actor_date, l.journaling_id location_id, b.inhabitanttype_code, sum(b.amount) amount, last(b.saldoamount) saldoamount, b.stocktaking
      from basic b inner join location l on b.location_id = l.id
      group by b.actor_date, l.journaling_id, b.inhabitanttype_code, b.stocktaking
  ), jou as (
      select
             actor_date, location_id, inhabitanttype_code, stocktaking,
             journalsum(coalesce(amount, 0) :: integer, coalesce(saldoamount, 0), stocktaking = 1) over
               (partition by location_id, inhabitanttype_code order by actor_date, stocktaking)
      from daygroups
  )
  select
         actor_date, location_id, inhabitanttype_code, stocktaking, journalsum [1] amount, journalsum [2] saldoamount, null::numeric weight
  from jou;



create or replace function recompute_journal_day(p_date date) returns void as $$
declare
  v_start    timestamp with time zone;
  v_end      timestamp with time zone;
  v_timezone text;
begin
  create temporary table if not exists temp_journal_tosaldo (
    actor_date          date,
    location_id         bigint,
    inhabitanttype_code varchar(15)
  ) on commit delete rows;
  select timezonename into strict v_timezone from organization where id = get_tenant_organization();
  select
    p_date :: timestamp at time zone v_timezone,
    (p_date + 1) :: timestamp at time zone v_timezone into strict v_start, v_end;
  raise notice 'Date range: % - %', v_start, v_end;
  with par as (
        select
        get_setting('ind_gilt_selling_ignored') is not distinct from 'true' gsignored,
        get_setting('ind_gilt_death_ignored') is not distinct from 'true' gdignored,
        not(get_setting('pigl_tracking'::text) is distinct from 'true'::text) AS pigltracking,
        o.timezonename tz
        from organization o
        where
        o.id = get_tenant_organization()
        ), explicitstockdate as (
        select sd.effectivedate  as effectivedate
        from stocktakingday sd cross join par where sd.effectivedate >= v_start and sd.effectivedate < v_end
        ), eloc as (
        select e.location_id, e.actor_date as effectivedate
        from par cross join empty_location e join location l on l.id = e.location_id and l.id = l.journaling_id
        where e.actor_date >= v_start and e.actor_date < v_end
        ), gilthop as (select hn.id
                                , g.id                                        gilt_id
                                , (hn.actor_date at time zone par.tz) :: date actor_date
                                , lf.journaling_id                            from_location_id
                                , lf.fornamed                                 from_location_fornamed
                                , case
                                      when lf.inhabitanttype_code = 'SOW' and
                                           hn.actor_date - g.birthdate < '50 days' :: interval then 'PIGL'
                                      when lf.inhabitanttype_code = 'SOW' then 'GILT'
                                      else lf.inhabitanttype_code end         from_inh_type
                                , lt.journaling_id                            to_location_id
                                , case
                                      when lt.inhabitanttype_code = 'SOW' and
                                           hn.actor_date - g.birthdate < '50 days' :: interval
                                          then 'PIGL'
                                      when lt.inhabitanttype_code = 'SOW' then 'GILT'
                                      else lt.inhabitanttype_code end         to_inh_type
                                , case
                                      when coalesce(d.effectivedate, elf.effectivedate) is null or
                                           coalesce(d.effectivedate, elf.effectivedate) >= hn.actor_date then 0
                                      else 2 end                              stocktaking_from
                                , case
                                      when coalesce(d.effectivedate, elt.effectivedate) is null or
                                           coalesce(d.effectivedate, elt.effectivedate) >= hn.actor_date then 0
                                      else 2 end                              stocktaking_to
                           from par
                                    cross join gilt g
                                    inner join hop hn on hn.gilt_id = g.id
                                    join location lf on lf.id = hn.from_location_id
                                    join location lt on lt.id = hn.to_location_id
                                    left join explicitstockdate d on (hn.actor_date at time zone par.tz) :: date =
                                                                     (d.effectivedate at time zone par.tz) :: date
                                    left join eloc elf on lf.journaling_id =
                                                          elf.location_id and
                                                          (hn.actor_date at time zone par.tz) :: date =
                                                          (elf.effectivedate at time zone par.tz) :: date
                                    left join eloc elt on lt.journaling_id = elt.location_id and
                                                          (hn.actor_date at time zone par.tz) :: date =
                                                          (elt.effectivedate at time zone par.tz) :: date
        ), x_dead_sows as (
        select
        (q.actor_date at time zone par.tz) :: date actor_date,
        l.journaling_id as location_id,
        case when q.actor_date >= s.par1date then 'SOW' :: text else 'PAR0' :: text end inhabitanttype_code,
        -sum(q.amount) :: integer amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
        from par cross join dead q inner join location l on l.id = q.location_id and l.inhabitanttype_code in ('SOW', 'GILT')
        inner join sow s on q.sow_id = s.id
        left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
        left join eloc el on el.location_id = l.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.actor_date >= v_start and q.actor_date < v_end
        group by
        date(q.actor_date at time zone par.tz), l.journaling_id,
        case when q.actor_date >= s.par1date then 'SOW' :: text else 'PAR0' :: text end,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
        ), x_moved_gilts as (
        select
        actor_date, from_location_id, from_inh_type, -count(id) amount, null :: integer saldoamount, stocktaking_from as stocktaking
        from par cross join gilthop where from_location_fornamed
        and actor_date >= v_start and actor_date < v_end
        group by actor_date, from_location_id, from_inh_type, stocktaking_from
    union all
        select
        actor_date, to_location_id, to_inh_type, +count(id) amount, null :: integer saldoamount, stocktaking_to as stocktaking
        from par cross join gilthop where from_location_fornamed
        and actor_date >= v_start and actor_date < v_end
        group by actor_date, to_location_id, to_inh_type, stocktaking_to
        ), x_dead_boars as (
        select
        (q.actor_date at time zone par.tz) :: date actor_date, l.journaling_id, 'BOAR' :: text inhabitanttype_code,
        -sum(q.amount) amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
        from par cross join dead q inner join location l on l.id = q.location_id and l.inhabitanttype_code = 'BOAR'
        left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
        left join eloc el on el.location_id = l.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where boar_id is not null and q.actor_date >= v_start and q.actor_date < v_end
        group by
        (q.actor_date at time zone par.tz):: date, l.journaling_id, case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
        ), x_dead_piglets as (
        select
        (q.actor_date at time zone par.tz) :: date actor_date,
        l.journaling_id,
        'PIGL' :: text inhabitanttype_code,
        -sum(q.amount) amount,
        null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
        from par cross join dead q
        inner join serving s on s.id = q.serving_id
        inner join location l on l.id = case
        when par.pigltracking then q.location_id
        else s.farrow_location_id
        end
        left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
        left join eloc el on el.location_id = l.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where serving_id is not null and s.farrow_location_id is not null
        and q.actor_date >= v_start and q.actor_date < v_end
        group by (q.actor_date at time zone par.tz) :: date,
        l.journaling_id,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
        ), x_dead_gilts as (
        select
        (q.actor_date at time zone par.tz) :: date actor_date,
        l.journaling_id,
        case
        when h.inhabitanttype_code = 'SOW' and q.actor_date - s.farrow_endtime < '50 days' :: interval then 'PIGL'
        when h.inhabitanttype_code = 'SOW' then 'GILT'
        else h.inhabitanttype_code end :: text inhabitanttype_code,
        -sum(q.amount) amount,
        null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
        from par cross join dead q
        inner join location h on h.id = q.location_id
        inner join gilt g on q.gilt_id = g.id
        left join serving s on g.serving_id = s.id
        left join location l on l.id = case
        when h.inhabitanttype_code = 'SOW' and q.actor_date - s.farrow_endtime < '50 days' :: interval and not par.pigltracking then s.farrow_location_id
        else q.location_id
        end
        left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
        left join eloc el on el.location_id = l.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.gilt_id is not null and q.serving_id is null and
        q.deathtype_code is not null and not par.gdignored
        and q.actor_date >= v_start and q.actor_date < v_end
        group by (q.actor_date at time zone par.tz) :: date, l.journaling_id, h.inhabitanttype_code,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end,
        case
        when h.inhabitanttype_code = 'SOW' and q.actor_date - s.farrow_endtime < '50 days' :: interval then 'PIGL'
        when h.inhabitanttype_code = 'SOW' then 'GILT'
        else h.inhabitanttype_code end :: text
        ), x_sold_gilts as (
        select
        (k.actor_date at time zone par.tz) :: date actor_date,
        l.journaling_id location_id,
        case
        when h.inhabitanttype_code = 'SOW' and k.actor_date - s.farrow_endtime < '50 days' :: interval then 'PIGL'
        when h.inhabitanttype_code = 'SOW' then 'GILT'
        else h.inhabitanttype_code end :: text inhabitanttype_code,
        -count(tio.id) amount,
        null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= k.actor_date then 0 else 2 end stocktaking
        from par cross join transferindividualout tio
        inner join location h on h.id = tio.location_id
        inner join gilt g on tio.gilt_id = g.id
        inner join transferout k on tio.transferout_id = k.id
        left join serving s on g.serving_id = s.id
        left join location l on l.id = case
        when h.inhabitanttype_code = 'SOW' and k.actor_date - s.farrow_endtime < '50 days' :: interval and not par.pigltracking then s.farrow_location_id
        else tio.location_id
        end
        left join explicitstockdate t on (k.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
        left join eloc el on el.location_id = l.journaling_id and (k.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where k.actor_date is not null and not par.gsignored
        and k.actor_date >= v_start and k.actor_date < v_end
        group by (k.actor_date at time zone par.tz) :: date, l.journaling_id, h.inhabitanttype_code,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= k.actor_date then 0 else 2 end, s.farrow_location_id, k.actor_date - s.farrow_endtime < '50 days' :: interval
        ), x_dead_groups as (
        select
        (q.actor_date at time zone par.tz) :: date actor_date, h.journaling_id,
        case
        when h.inhabitanttype_code = 'SOW' then 'GILT'
        else h.inhabitanttype_code
        end inhabitanttype_code,
        -sum(q.amount) amount,
        null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
        from par cross join dead q
        inner join location h on h.id = q.location_id
        left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where sow_id is null and boar_id is null and serving_id is null and gilt_id is null
        and q.actor_date >= v_start and q.actor_date < v_end
        group by (q.actor_date at time zone par.tz) :: date, h.journaling_id, h.inhabitanttype_code,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
        ), x_moved_groups as (
        select (r.actor_date at time zone par.tz) :: date actor_date,
        h.journaling_id,
        case when h.inhabitanttype_code = 'SOW' then 'GILT' else h.inhabitanttype_code end inhabitanttype_code,
        sum(-q.pigamount) amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
        from par cross join pigqualityout q
        inner join transferout r on r.id = q.transferout_id and r.actor_date is not null
        inner join location h on h.id = q.location_id
        left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.pigamount is not null
        and r.actor_date >= v_start and r.actor_date < v_end
        group by (r.actor_date at time zone par.tz) :: date, h.journaling_id,
        h.inhabitanttype_code,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end

    union all
        select (r.actor_date at time zone par.tz) :: date actor_date, h.journaling_id,
        case when h.inhabitanttype_code = 'SOW' then 'GILT' else h.inhabitanttype_code end inhabitanttype_code,
        sum(q.pigamount) amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end
        from par cross join pigqualityin q
        inner join transferin r on r.id = q.transferin_id
        inner join location h on h.id = q.location_id
        left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.pigamount is not null
        and r.actor_date >= v_start and r.actor_date < v_end
        group by (r.actor_date at time zone par.tz) :: date, h.journaling_id, h.inhabitanttype_code,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end

    union all
        select
        (r.actor_date at time zone par.tz) :: date actor_date, h.journaling_id,
        case when h.inhabitanttype_code = 'SOW' then 'GILT' else h.inhabitanttype_code end inhabitanttype_code,
        sum(-q.pigamount) amount,
        null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
        from par cross join pigqualityloc q
        inner join transferloc r on r.id = q.transferloc_id
        inner join location h on h.id = q.fromlocation_id
        left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.pigamount is not null
        and r.actor_date >= v_start and r.actor_date < v_end
        group by (r.actor_date at time zone par.tz) :: date, h.journaling_id, h.inhabitanttype_code,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end
    union all
        select (r.actor_date at time zone par.tz) :: date actor_date, h.journaling_id,
        case when h.inhabitanttype_code = 'SOW' then 'GILT' else h.inhabitanttype_code end inhabitanttype_code,
        sum(q.pigamount) amount,

        null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
        from par cross join pigqualityloc q
        inner join transferloc r on r.id = q.transferloc_id
        inner join location h on h.id = q.tolocation_id
        left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.pigamount is not null
        and r.actor_date >= v_start and r.actor_date < v_end
        group by (r.actor_date at time zone par.tz) :: date, h.journaling_id, h.inhabitanttype_code,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end
        ), x_fostering as (
        select
        (r.actor_date at time zone par.tz) :: date actor_date,
        h.journaling_id, 'PIGL' inhabitanttype_code,
        sum(-r.amount) amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
        from par cross join fostering r
        join location h on h.id = r.from_location_id
        left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where r.actor_date >= v_start and r.actor_date < v_end
        group by (r.actor_date at time zone par.tz) :: date, h.journaling_id,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end

    union all

        select
        (r.actor_date at time zone par.tz) :: date actor_date,
        h.journaling_id, 'PIGL' inhabitanttype_code,
        sum(r.amount) amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
        from par cross join fostering r
        join location h on h.id = r.to_location_id
        left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where r.actor_date >= v_start and r.actor_date < v_end
        group by (r.actor_date at time zone par.tz) :: date, h.journaling_id,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end

        ), x_bought_gilts as (
        select
        (r.actor_date at time zone par.tz) :: date actor_date,
        h.journaling_id,
        case when h.inhabitanttype_code = 'SOW' then 'GILT' else h.inhabitanttype_code end inhabitanttype_code,
        count(q.gilt_id) amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
        from par cross join transferindividualin q
        inner join transferin r on q.transferin_id = r.id
        inner join location h on h.id = q.location_id
        left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.gilt_id is not null
        and r.actor_date >= v_start and r.actor_date < v_end
        group by (r.actor_date at time zone par.tz) :: date, h.journaling_id,
        case when h.inhabitanttype_code = 'SOW' then 'GILT' else h.inhabitanttype_code end,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end
        ), x_bought_boars as (
        select
        (r.actor_date at time zone par.tz) :: date actor_date, h.journaling_id, h.inhabitanttype_code,
        count(q.boar_id) amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
        from par cross join transferindividualin q
        inner join transferin r on q.transferin_id = r.id
        inner join location h on h.id = q.location_id
        left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.boar_id is not null
        and r.actor_date >= v_start and r.actor_date < v_end
        group by (r.actor_date at time zone par.tz) :: date, h.journaling_id, h.inhabitanttype_code,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end
        ), x_bought_sows as (
        select
        (r.actor_date at time zone par.tz) :: date actor_date,
        h.journaling_id,
        case when r.actor_date >= s.par1date then 'SOW' else 'PAR0' end inhabitanttype_code,
        count(q.sow_id) amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end stocktaking
        from par cross join transferindividualin q
        inner join transferin r on q.transferin_id = r.id
        inner join sow s on q.sow_id = s.id
        inner join location h on h.id = q.location_id
        left join explicitstockdate t on (r.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (r.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.sow_id is not null
        and r.actor_date >= v_start and r.actor_date < v_end
        group by (r.actor_date at time zone par.tz) :: date, h.journaling_id, case when r.actor_date >= s.par1date then 'SOW' else 'PAR0' end,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= r.actor_date then 0 else 2 end
        ), x_stocktaking as (
        select
        (t.effectivedate at time zone par.tz) :: date, s.location_id, s.inhabitanttype_code, null :: integer amount,
        s.amount saldoamount, 1 stocktaking
        from par cross join
        stocktakingday t inner join stocktaking s on t.id = s.stocktakingday_id
        where t.effectivedate >= v_start and t.effectivedate < v_end
        ), x_emptyloc as (
        select
        distinct (e.effectivedate at time zone par.tz) :: date, e.location_id, l.inhabitanttype_code, null :: integer amount,
        0 as saldoamount, 1 stocktaking
        from par cross join eloc e
        inner join location l on l.journaling_id = e.location_id
        left join stocktakingday sd on (e.effectivedate at time zone par.tz) :: date = (sd.effectivedate at time zone par.tz) :: date
        left join stocktaking st on st.stocktakingday_id = sd.id and st.location_id = e.location_id and st.inhabitanttype_code = l.inhabitanttype_code
        where st.id is null
        and e.effectivedate >= v_start and e.effectivedate < v_end
        ), weanedfrom AS (
        SELECT DISTINCT ON (w.id) w.create_date,
        w.update_date,
        w.create_account_id,
        w.update_account_id,
        w.create_ui,
        w.update_ui,
        w.id,
        w.serving_id,
        null::numeric weight,
        w.amount,
        w.comment,
        w.actor_id,
        w.actor_date,
        w.nursery,
        w.location_id,
        w.sow_location_id,
        COALESCE(ph.to_location_id, f.farrow_location_id) AS from_location_id
        FROM par
        CROSS JOIN weaned w
        JOIN hop wh ON wh.weaned_id = w.id
        JOIN hop fh ON fh.farrowing_id = w.serving_id
        JOIN serving f ON f.id = w.serving_id
        LEFT JOIN hop ph ON ph.sow_id = f.sow_id AND (ROW(fh.actor_date, fh.id) < ROW(ph.actor_date, ph.id)) AND (ROW(ph.actor_date, ph.id) < ROW(wh.actor_date, wh.id)) AND par.pigltracking
        where w.actor_date >= v_start and w.actor_date < v_end
        ORDER BY w.id, ph.actor_date DESC, ph.id DESC
        ), x_weanings as (
        select
        (q.actor_date at time zone par.tz) :: date,
        wl.journaling_id,
        wl.inhabitanttype_code,
        sum(q.amount)   amount,
        null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
        from par
        cross join weaned q
        inner join location wl on q.location_id = wl.id
        left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = wl.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.amount is not null
        and q.actor_date >= v_start and q.actor_date < v_end
        group by (q.actor_date at time zone par.tz) :: date, wl.journaling_id,
        wl.inhabitanttype_code,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
        --- Weanings - removing PIGLs
    union all
        select
        (q.actor_date at time zone par.tz) :: date,
        h.journaling_id,
        'PIGL' :: character varying,
        (-sum(q.amount))                                   as amount,
        null :: integer                                    as saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end as stocktaking
        from par
        cross join weanedfrom q
        join location h on h.id = q.from_location_id
        left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.amount is not null
        and q.actor_date >= v_start and q.actor_date < v_end
        group by q.actor_date at time zone par.tz, h.journaling_id,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
        ), x_moved_piglets as (
        select
        (q.actor_date at time zone par.tz) :: date,
        h.journaling_id,
        'PIGL'::character varying,
        (-sum(q.amount)) as amount,
        null ::integer as saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end as stocktaking
        from par cross join pigltrans q
        join location h on h.id = q.from_location_id
        left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where par.pigltracking
        and q.actor_date >= v_start and q.actor_date < v_end
        group by (q.actor_date at time zone par.tz) :: date, h.journaling_id,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
    union all
        select
        (q.actor_date at time zone par.tz) :: date,
        h.journaling_id,
        'PIGL'::character varying,
        (sum(q.amount)) as amount,
        null ::integer as saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end as stocktaking
        from par cross join pigltrans q
        join location h on h.id = q.to_location_id
        left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date = (t.effectivedate at time zone par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where par.pigltracking
        and q.actor_date >= v_start and q.actor_date < v_end
        group by (q.actor_date at time zone par.tz) :: date, h.journaling_id,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end

        ), x_farrowings as (
        select
        (q.farrow_endtime at time zone par.tz) :: date, l.journaling_id, 'PIGL'::text, sum(q.liveborn) amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.farrow_endtime then 0 else 2 end as stocktaking
        from par cross join serving q
        inner join location l on l.id = q.farrow_location_id
        left join explicitstockdate t on (q.farrow_endtime at time zone par.tz) :: date =
        (t.effectivedate at time zone par.tz) :: date
        left join eloc el on el.location_id = l.journaling_id and (q.farrow_endtime at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.liveborn is not null and q.farrow_endtime is not null
        and q.farrow_endtime >= v_start and q.farrow_endtime < v_end
        group by (q.farrow_endtime at time zone par.tz) :: date, l.journaling_id,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.farrow_endtime then 0 else 2 end
        ), x_moved_sows as (
        select * from (select distinct on (q.id)
        (q.actor_date at time zone par.tz) :: date actor_date,
        h.journaling_id,
        case when h.inhabitanttype_code not in ('SOW','GILT') then h.inhabitanttype_code when q.actor_date > s.par1date then 'SOW' :: text
        else 'PAR0' :: text end inhabitanttype_code, -1 :: integer amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
        from par cross join hop q
        inner join sow s on q.sow_id = s.id
        left join hop qq on q.sow_id = qq.sow_id and
        (q.actor_date > qq.actor_date or
        q.actor_date = qq.actor_date and
        q.id > qq.id)
        left join location h on h.id = coalesce(qq.to_location_id, s.created_location_id)
        left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.sow_id is not null
        and q.actor_date >= v_start and q.actor_date < v_end
        order by q.id, qq.actor_date desc, qq.id desc) xx
    union all

        select
        (q.actor_date at time zone par.tz) :: date actor_date, l.journaling_id,
        case when l.inhabitanttype_code not in ('SOW','GILT') then l.inhabitanttype_code when q.actor_date > s.par1date then 'SOW' :: text else 'PAR0' :: text end inhabitanttype_code,
        count(q.id) :: integer amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
        from par cross join hop q inner join location l on l.id = q.to_location_id
        inner join sow s on q.sow_id = s.id
        left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = l.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.actor_date >= v_start and q.actor_date < v_end
        group by (q.actor_date at time zone par.tz) :: date, l.journaling_id,
        case when l.inhabitanttype_code not in ('SOW','GILT') then l.inhabitanttype_code when q.actor_date > s.par1date then 'SOW' :: text else 'PAR0' :: text end,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
        ), x_moved_boars as (
        select * from (select distinct on (q.id)
        (q.actor_date at time zone par.tz) :: date actor_date, h.journaling_id,
        h.inhabitanttype_code, -1 :: integer amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
        from par cross join hop q
        inner join boar s on q.boar_id = s.id
        left join hop qq on q.boar_id = qq.boar_id and
        (q.actor_date > qq.actor_date or
        q.actor_date = qq.actor_date and
        q.id > qq.id)
        left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone par.tz) :: date
        left join location h on coalesce(qq.to_location_id, s.created_location_id) = h.id
        left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.boar_id is not null and h.id is not null
        and q.actor_date >= v_start and q.actor_date < v_end
        order by q.id, qq.actor_date desc, qq.id desc) xx
    union all

        select
        (q.actor_date at time zone par.tz) :: date actor_date, h.journaling_id, h.inhabitanttype_code,
        count(q.id) :: integer amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end stocktaking
        from par cross join hop q
        inner join location h on q.to_location_id = h.id
        left join explicitstockdate t on (q.actor_date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (q.actor_date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where boar_id is not null
        and q.actor_date >= v_start and q.actor_date < v_end
        group by (q.actor_date at time zone par.tz) :: date, h.journaling_id, h.inhabitanttype_code,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.actor_date then 0 else 2 end
        ), x_par0 as (
        select
        (q.sowdate at time zone par.tz) :: date actor_date, h.journaling_id, 'PAR0' :: text inhabitanttype_code,
        count(q.id) :: integer amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.sowdate then 0 else 2 end stocktaking
        from par cross join sow q
        join location h on h.id = q.created_location_id
        left join explicitstockdate t on (q.sowdate at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (q.sowdate at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        left join transferindividualin ti on ti.sow_id = q.id
        where q.sowdate is not null and ti.id is null
        and q.sowdate >= v_start and q.sowdate < v_end
        group by (q.sowdate at time zone par.tz) :: date, h.journaling_id,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.sowdate then 0 else 2 end
    union all
        select
        (q.sowdate at time zone par.tz) :: date actor_date, h.journaling_id, 'GILT' :: text inhabitanttype_code,
        -count(q.id) :: integer amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.sowdate then 0 else 2 end stocktaking
        from par cross join sow q
        join location h on h.id = q.created_location_id
        left join explicitstockdate t on (q.sowdate at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (q.sowdate at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        left join transferindividualin ti on ti.sow_id = q.id
        where q.sowdate is not null and ti.id is null
        and q.sowdate >= v_start and q.sowdate < v_end
        group by (q.sowdate at time zone par.tz) :: date, h.journaling_id,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.sowdate then 0 else 2 end
        ), x_par1 as (
        select
        (q.par1date at time zone par.tz) :: date actor_date, h.journaling_id, 'SOW' :: text inhabitanttype_code,
        count(q.id) :: integer amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.par1date then 0 else 2 end stocktaking
        from par cross join sow q
        join location h on h.id = q.par1location_id
        left join explicitstockdate t on (q.par1date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (q.par1date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.par1date is not null
        and q.par1date >= v_start and q.par1date < v_end
        group by (q.par1date at time zone par.tz) :: date, h.journaling_id,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.par1date then 0 else 2 end

        -- Removing farrowing parity 0 sows (par1date)

    union all
        select
        (q.par1date at time zone par.tz) :: date actor_date, h.journaling_id, 'PAR0' :: text inhabitanttype_code,
        -count(q.id) :: integer amount, null :: integer saldoamount,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.par1date then 0 else 2 end stocktaking
        from par cross join sow q
        join location h on h.id = q.par1location_id
        left join explicitstockdate t on (q.par1date at time zone par.tz) :: date =
        (t.effectivedate at time zone
        par.tz) :: date
        left join eloc el on el.location_id = h.journaling_id and (q.par1date at time zone par.tz) :: date = (el.effectivedate at time zone par.tz) :: date
        where q.par1date is not null
        and q.par1date >= v_start and q.par1date < v_end
        group by (q.par1date at time zone par.tz) :: date, h.journaling_id,
        case when coalesce(t.effectivedate, el.effectivedate) is null or coalesce(t.effectivedate, el.effectivedate) >= q.par1date then 0 else 2 end
        ), basic as (
        select * from x_dead_sows
    union all
        select * from x_moved_gilts
    union all
        select * from x_dead_boars
    union all
        select * from x_dead_piglets
    union all
        select * from x_dead_gilts
    union all
        select * from x_sold_gilts
    union all
        select * from x_dead_groups
    union all
        select * from x_moved_groups
    union all
        select * from x_fostering
    union all
        select * from x_bought_gilts
    union all
        select * from x_bought_boars
    union all
        select * from x_bought_sows
    union all
        select * from x_stocktaking
    union all
        select * from x_emptyloc
    union all
        select * from x_weanings
    union all
        select * from x_moved_sows
    union all
        select * from x_moved_boars
    union all
        select * from x_moved_piglets
    union all
        select * from x_par0
    union all
        select * from x_par1
    union all
        select * from x_farrowings
        ),
      deleted as (delete from journal where actor_date = p_date returning actor_date, inhabitanttype_code, location_id),
      inserted as (
      insert into journal (actor_date, location_id, inhabitanttype_code, amount, weight, saldoamount, stocktaking)
        select
          b.actor_date, l.journaling_id location_id, b.inhabitanttype_code, sum(b.amount)       amount, null weight,
                                                        last(b.saldoamount) saldoamount, b.stocktaking
        from
           basic b inner join location l on l.id = b.location_id
        where b.actor_date is not null and b.inhabitanttype_code is not null and b.stocktaking is not null
        group by b.actor_date, l.journaling_id, b.inhabitanttype_code, b.stocktaking
      returning actor_date, inhabitanttype_code, location_id)
  insert into temp_journal_tosaldo (actor_date, inhabitanttype_code, location_id)
    select
      actor_date, inhabitanttype_code, location_id from deleted
    where inhabitanttype_code is not null and location_id is not null
    union select
            actor_date, inhabitanttype_code, location_id from inserted
          where inhabitanttype_code is not null and location_id is not null;

end;
$$ language plpgsql set search_path from current;

-- Recomputes the journal on the days marked as invalid and then recomputes the saldos, so that the journal is valid afterwards
create or replace function refresh_journal() returns void as $$
declare
  r record;
begin
  lock table journal in exclusive mode;
  if (select count(distinct actor_date) from journal_invalid) < 100 then
    for r in with d as (delete from journal_invalid returning actor_date) select distinct actor_date from d loop
      perform recompute_journal_day(r.actor_date);
    end loop;
    perform recompute_journal_saldo();
  else
    delete from journal_invalid;
    perform recreate_journal();
  end if;
end;
$$ language plpgsql set search_path from current;

--
-- UNCOMMENT, IF AFTER YOUR CHANGES ALL JOURNALS MUST BE RECOMPUTED
insert into journal_sanity(id) select 1 where not exists (select 1 from journal_sanity);

