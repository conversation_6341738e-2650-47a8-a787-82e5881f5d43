drop trigger if exists serving_sowstate_aucf on serving;
create constraint trigger serving_sowstate_aucf
    after update of sow_id, actor_date, location_id, serving_group, breed on serving  -- todo: should we check the other columns? (farrowing?)
    deferrable initially deferred
    for each row
    when (common.invalidate_sow_state_id(new.sow_id, old.sow_id))
execute procedure common.update_sow_and_serving_states_tf();

with ls as (select distinct on (i.sow_id) i.id as serving_id, i.sow_id, i.actor_date, i.state, i.serving_group, i.liveborn, i.breed, i.animalid
            from serving i
            order by i.sow_id, i.actor_date desc, i.id desc
), lsg as (
    select distinct on (i.sow_id) i.id as serving_id, i.sow_id, i.actor_date,
                                  i.farrow_starttime, i.liveborn, i.stillborn, i.weakborn, i.mummificated, i.femalepiglets
    from serving i where i.farrow_starttime is not null
    order by i.sow_id, i.farrow_starttime desc, i.id desc
), lsl as (
    select distinct on (i.sow_id) i.id as serving_id, i.sow_id, i.actor_date, i.farrow_starttime
    from serving i where i.farrow_starttime is not null and i.state = 7
    order by i.sow_id, i.farrow_starttime desc, i.id desc
), wpl as (
    select lsl.sow_id, w.serving_id, sum(w.amount) amount, max(w.actor_date) actor_date
    from lsl join weaned w on w.serving_id = lsl.serving_id group by lsl.sow_id, w.serving_id
), ff as (
    select ls.sow_id, ls.serving_id, sum(f.amount) amount
    from ls join fostering f on f.from_serving_id = ls.serving_id
    where (ls.state = 4 or ls.state = 5 or ls.state = 6)
    group by ls.sow_id, ls.serving_id
), ft as (
    select ls.sow_id, ls.serving_id, sum(f.amount) amount
    from ls join fostering f on f.to_serving_id = ls.serving_id
    where (ls.state = 4 or ls.state = 5 or ls.state = 6)
    group by ls.sow_id, ls.serving_id
), ds as (
    select ls.sow_id, ls.serving_id, sum(f.amount) amount
    from ls join dead f on f.serving_id = ls.serving_id
    where (ls.state = 4 or ls.state = 5 or ls.state = 6)
    group by ls.sow_id, ls.serving_id
), lw as (
    select ls.sow_id, ls.serving_id, sum(f.amount) amount
    from ls join weaned f on f.serving_id = ls.serving_id
    where (ls.state = 4 or ls.state = 5 or ls.state = 6)
    group by ls.sow_id, ls.serving_id
), su as (
    select ls.sow_id, ls.liveborn - coalesce(ff.amount, 0) + coalesce(ft.amount, 0) - coalesce(ds.amount, 0) - coalesce(lw.amount, 0) amount
    from ls
             left join ff on ff.serving_id = ls.serving_id
             left join ft on ft.serving_id = ls.serving_id
             left join ds on ds.serving_id = ls.serving_id
             left join lw on lw.serving_id = ls.serving_id
    where (ls.state = 4 or ls.state = 5 or ls.state = 6)
)
insert into x_sow_current as x(id, serving_id, serving_group, serving_date, serving_state, serving_date_gestation, serving_date_lactation, liveborn, stillborn, mummificated, femalepiglets, weakborn, gestation_farrow_starttime, lactation_farrow_starttime, piglets_under, last_weaned, last_weaning_date, breed, animalid)
select ls.sow_id, ls.serving_id, ls.serving_group, ls.actor_date serving_date, ls.state serving_state,
       lsg.actor_date serving_date_gestation, lsl.actor_date serving_date_lactation, lsg.liveborn, lsg.stillborn, lsg.mummificated, lsg.femalepiglets, lsg.weakborn, lsg.farrow_starttime gestation_farrow_starttime, lsl.farrow_starttime lactation_farrow_starttime,
       su.amount piglets_under, wpl.amount last_weaned, wpl.actor_date, ls.breed, ls.animalid
from ls
         left join lsg on ls.sow_id = lsg.sow_id
         left join lsl on ls.sow_id = lsl.sow_id
         left join wpl on ls.sow_id = wpl.sow_id
         left join su on ls.sow_id = su.sow_id
on conflict (id) do update set (serving_id, serving_group, serving_date, serving_state, serving_date_gestation, serving_date_lactation, liveborn, stillborn, mummificated, femalepiglets, weakborn, gestation_farrow_starttime, lactation_farrow_starttime, piglets_under, last_weaned, last_weaning_date, breed, animalid)
                                   = (EXCLUDED.serving_id, EXCLUDED.serving_group, EXCLUDED.serving_date, EXCLUDED.serving_state, EXCLUDED.serving_date_gestation, EXCLUDED.serving_date_lactation, EXCLUDED.liveborn, EXCLUDED.stillborn, EXCLUDED.mummificated,
                                      EXCLUDED.femalepiglets, EXCLUDED.weakborn, EXCLUDED.gestation_farrow_starttime, EXCLUDED.lactation_farrow_starttime, EXCLUDED.piglets_under, EXCLUDED.last_weaned, EXCLUDED.last_weaning_date, EXCLUDED.breed, EXCLUDED.animalid)
where (EXCLUDED.serving_id, EXCLUDED.serving_group, EXCLUDED.serving_date, EXCLUDED.serving_state, EXCLUDED.serving_date_gestation, EXCLUDED.serving_date_lactation, EXCLUDED.liveborn, EXCLUDED.stillborn, EXCLUDED.mummificated,
       EXCLUDED.femalepiglets, EXCLUDED.weakborn, EXCLUDED.gestation_farrow_starttime, EXCLUDED.lactation_farrow_starttime, EXCLUDED.piglets_under, EXCLUDED.last_weaned, EXCLUDED.last_weaning_date, EXCLUDED.breed, EXCLUDED.animalid) is distinct from
      (x.serving_id, x.serving_group, x.serving_date, x.serving_state, x.serving_date_gestation, x.serving_date_lactation, x.liveborn, x.stillborn, x.mummificated,
       x.femalepiglets, x.weakborn, x.gestation_farrow_starttime, x.lactation_farrow_starttime, x.piglets_under, x.last_weaned, x.last_weaning_date, x.breed, x.animalid);

