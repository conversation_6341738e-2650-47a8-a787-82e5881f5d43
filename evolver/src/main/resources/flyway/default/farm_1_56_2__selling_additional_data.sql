do $$
begin
  if not exists (select 1 from information_schema.columns where table_schema = '${farmSchema}' and table_name = 'location' and column_name = 'addressname') then
    alter table location
      add addressname character varying (80);
  end if;
end;
$$;

do $$
begin
  if not exists (select 1 from information_schema.columns where table_schema = '${farmSchema}' and table_name = 'transferout' and column_name = 'shippingcomment') then
    alter table transferout
      add shippingcomment character varying (300);
  end if;
end;
$$;
