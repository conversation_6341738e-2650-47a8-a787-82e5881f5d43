create table if not exists medicine_storage (
  like common.tracked including all,
  id                     bigint default nextval('id_seq') not null primary key,
  name                   var<PERSON>r(80) unique not null,
  comment                text
);

select common.trackable_table(null, 'medicine_storage');

create table if not exists medicine_storage_transfer_type (
  id                     bigint default nextval('id_seq') not null primary key,
  name                   varchar(80) unique not null
);

create table if not exists medicine_storage_item (
  id                     bigint default nextval('id_seq') not null primary key,
  medicine_id            bigint not null references medicine(id) deferrable initially immediate,
  cure_id                bigint references cure(id) deferrable initially immediate
);

create table if not exists medicine_storage_transfer (
  like common.tracked including all,
  id                     bigint default nextval('id_seq') not null primary key,
  stock_item_id          bigint not null references medicine_storage_item(id) deferrable initially immediate,
  actor_date             timestamptz not null,
  amount                 numeric(12,2) not null,
  from_storage_id        bigint references medicine_storage(id) deferrable initially immediate,
  to_storage_id          bigint references medicine_storage(id) deferrable initially immediate,
  storage_transfer_type  bigint not null references medicine_storage_transfeR_type(id) deferrable initially immediate,
  comment                text,
  constraint storage_transfer_from_or_to_not_null check (from_storage_id is not null or to_storage_id is not null)
);

select common.trackable_table(null, 'medicine_storage_transfer');

create table if not exists medicine_storage_inventure (
  like common.tracked including all,
  id                     bigint default nextval('id_seq') not null primary key,
  actor_date             timestamptz not null,
  storage_id             bigint not null references medicine_storage(id) deferrable initially immediate,
  stock_item_id          bigint not null references medicine_storage_item(id) deferrable initially immediate,
  amount                 numeric(12,2) not null,
  comment                text
);

select common.trackable_table(null, 'medicine_storage_inventure');

