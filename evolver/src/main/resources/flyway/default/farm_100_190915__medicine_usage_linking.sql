create or replace function link_medicine_usages() returns void as
$$
declare
    prescription_ids bigint[];
begin
    select array_agg(distinct prescription_id) into prescription_ids from medicine_usage_to_link;
    with recursive dtl as (delete from medicine_usage_to_link returning *)
       , tl as (select distinct
                       sow_id
                     , boar_id
                     , gilt_id
                     , serving_id
                     , location_id
                     , prescription_id
                     , treatment_group
                from dtl)
       , pmu as (select distinct on (mu.id)
                        mu.id
                      , mu.actor_date
                      , mu.location_id
                      , mu.prescription_id
                      , mu.illnesstype_code
                      , mu.inhabitanttype_code
                      , mu.sow_id
                      , mu.boar_id
                      , mu.gilt_id
                      , mu.serving_id
                      , mu.animals_count
                      , mu.treatment_group
                      , cu.id cure_id
                      , cu.repetition
                      , cu.repetition_delay
                 from medicine_usage mu
                          inner join prescription pr on pr.id = mu.prescription_id
                          inner join cure cu
                                     on (
                                         cu.prescription_id = pr.id
                                             -- and cu.repetition > 1
                                             and (
                                             (
                                                 cu.inhabitanttype_code = mu.inhabitanttype_code
                                                     and cu.illnesstype_code = mu.illnesstype_code
                                                 )
                                                 or (
                                                 cu.inhabitanttype_code = mu.inhabitanttype_code
                                                     and cu.illnesstype_code is null
                                                 )
                                                 or (
                                                 cu.illnesstype_code = mu.illnesstype_code
                                                     and cu.inhabitanttype_code is null
                                                 )
                                                 or (
                                                 cu.inhabitanttype_code is null
                                                     and cu.illnesstype_code is null
                                                 )
                                             )
                                         )
                          inner join tl on (
                     tl.prescription_id = mu.prescription_id and (
                         tl.sow_id = mu.sow_id or
                         tl.boar_id = mu.boar_id or
                         tl.gilt_id = mu.gilt_id or
                         tl.serving_id = mu.serving_id or
                         (tl.sow_id is null and tl.boar_id is null and tl.gilt_id is null and tl.serving_id is null and
                          mu.sow_id is null and mu.boar_id is null and mu.gilt_id is null and mu.serving_id is null and
                          tl.location_id = mu.location_id and tl.treatment_group is not distinct from mu.treatment_group
                             )
                         )
                     )
                 order by mu.id, pr.id, cu.illnesstype_code is null, cu.inhabitanttype_code is null, cu.create_date)
       , shots as (select count(pmu.id)
                          over sameBatch as n
                        , *
                   from pmu
                   window sameBatch as (
                           partition by
                               pmu.cure_id,
                               pmu.illnesstype_code,
                               pmu.inhabitanttype_code,
                               pmu.sow_id,
                               pmu.boar_id,
                               pmu.gilt_id,
                               pmu.serving_id,
                               case
                                   when (
                                       pmu.sow_id is null
                                           and pmu.boar_id is null
                                           and pmu.gilt_id is null
                                           and pmu.serving_id is null
                                       )
                                       then pmu.location_id
                                   else cast(null as bigint)
                                   end,
                               pmu.treatment_group,
                               case
                                   when pmu.treatment_group is null and pmu.serving_id is null
                                       then pmu.animals_count
                                   else null end
                           order by
                               pmu.actor_date asc,
                               pmu.id asc )
                   order by pmu.cure_id, pmu.illnesstype_code, pmu.inhabitanttype_code, pmu.sow_id, pmu.boar_id
                          , pmu.gilt_id, pmu.serving_id, case
                                                             when (
                                                                 pmu.sow_id is null
                                                                     and pmu.boar_id is null
                                                                     and pmu.gilt_id is null
                                                                     and pmu.serving_id is null
                                                                 )
                                                                 then pmu.location_id
                                                             else cast(null as bigint) end
                          , pmu.treatment_group, case
                                                     when pmu.treatment_group is null and pmu.serving_id is null
                                                         then pmu.animals_count
                                                     else null end
                          , pmu.actor_date asc, pmu.id asc)
       , shotsN as (select id
                         , 1          r
                         , id as      first_id
                         , actor_date start_date
                         , n
                         , actor_date
                         , location_id
                         , treatment_group
                         , prescription_id
                         , inhabitanttype_code
                         , illnesstype_code
                         , sow_id
                         , boar_id
                         , gilt_id
                         , serving_id
                         , animals_count
                         , cure_id
                         , repetition
                         , repetition_delay
                    from shots
                    where shots.n = 1
                    union all
                    select s.id
                         -- 24 hours grace period for late application of the medicine
                         , case
                               when
                                   p.start_date + p.repetition * p.repetition_delay * '1h' :: interval +
                                   '24h' :: interval >= s.actor_date and
                                   p.r + 1 <= p.repetition
                                   then p.r + 1
                               else 1 end            r
                         , case
                               when
                                   p.start_date + p.repetition * p.repetition_delay * '1h' :: interval +
                                   '24h' :: interval >= s.actor_date and
                                   p.r + 1 <= p.repetition
                                   then p.first_id
                               else s.id end         first_id
                         , case
                               when
                                   p.start_date + p.repetition * p.repetition_delay * '1h' :: interval +
                                   '24h' :: interval >= s.actor_date and
                                   p.r + 1 <= p.repetition
                                   then p.start_date
                               else s.actor_date end start_date
                         , s.n
                         , s.actor_date
                         , s.location_id
                         , s.treatment_group
                         , s.prescription_id
                         , s.inhabitanttype_code
                         , s.illnesstype_code
                         , s.sow_id
                         , s.boar_id
                         , s.gilt_id
                         , s.serving_id
                         , s.animals_count
                         , s.cure_id
                         , s.repetition
                         , s.repetition_delay
                    from shots s
                             inner join shotsN p on (
                                                     s.cure_id,
                                                     s.illnesstype_code,
                                                     s.inhabitanttype_code,
                                                     s.sow_id,
                                                     s.boar_id,
                                                     s.gilt_id,
                                                     s.serving_id,
                                                     case
                                                         when (
                                                             s.sow_id is null
                                                                 and s.boar_id is null
                                                                 and s.gilt_id is null
                                                                 and s.serving_id is null
                                                             )
                                                             then s.location_id
                                                         else cast(null as bigint) end,
                                                     s.treatment_group,
                                                     case
                                                         when s.treatment_group is null and s.serving_id is null
                                                             then s.animals_count
                                                         else null end
                                                        ) is not distinct from (
                                                                                p.cure_id,
                                                                                p.illnesstype_code,
                                                                                p.inhabitanttype_code,
                                                                                p.sow_id,
                                                                                p.boar_id,
                                                                                p.gilt_id,
                                                                                p.serving_id,
                                                                                case
                                                                                    when (
                                                                                        p.sow_id is null
                                                                                            and p.boar_id is null
                                                                                            and p.gilt_id is null
                                                                                            and p.serving_id is null
                                                                                        )
                                                                                        then p.location_id
                                                                                    else cast(null as bigint) end,
                                                                                p.treatment_group,
                                                                                case
                                                                                    when p.treatment_group is null and p.serving_id is null
                                                                                        then p.animals_count
                                                                                    else null end
                        )
                        and p.n + 1 = s.n)
    -- select * from shotsN;
    update medicine_usage mu
    set first_usage_id = s.first_id
      , cure_id        = s.cure_id
      , repeat_no      = s.r
    from shotsN s
    where s.id = mu.id
      and (mu.first_usage_id, mu.cure_id, mu.repeat_no) is distinct from (s.first_id, s.cure_id, s.r);

    --
    perform common.recompute_current_prescriptions(prescription_ids);
end;
$$ language plpgsql
    set search_path from current;

create or replace function medicine_usage_mark_for_link_aiudtf() returns trigger as
$$
begin
    if tg_op in ('DELETE') then
        insert into medicine_usage_to_link(sow_id, boar_id, gilt_id, serving_id, location_id, prescription_id, treatment_group)
        values (old.sow_id, old.boar_id, old.gilt_id, old.serving_id, old.location_id, old.prescription_id, old.treatment_group);
    elsif tg_op in ('INSERT') then
        insert into medicine_usage_to_link(sow_id, boar_id, gilt_id, serving_id, location_id, prescription_id, treatment_group)
        values (new.sow_id, new.boar_id, new.gilt_id, new.serving_id, new.location_id, new.prescription_id, new.treatment_group);
    elsif tg_op in ('UPDATE')
        and
          (old.sow_id, old.boar_id, old.gilt_id, old.serving_id, old.location_id, old.prescription_id, old.actor_date,
           old.illnesstype_code, old.treatment_group, old.animals_count, old.amount_used) is distinct from (new.sow_id,
                                                                                                            new.boar_id,
                                                                                                            new.gilt_id,
                                                                                                            new.serving_id,
                                                                                                            new.location_id,
                                                                                                            new.prescription_id,
                                                                                                            new.actor_date,
                                                                                                            new.illnesstype_code,
                                                                                                            new.treatment_group,
                                                                                                            new.animals_count,
                                                                                                            new.amount_used)
    then
        insert into medicine_usage_to_link(sow_id, boar_id, gilt_id, serving_id, location_id, prescription_id, treatment_group)
        values (new.sow_id, new.boar_id, new.gilt_id, new.serving_id, new.location_id, new.prescription_id, new.treatment_group);
        insert into medicine_usage_to_link(sow_id, boar_id, gilt_id, serving_id, location_id, prescription_id, treatment_group)
        values (old.sow_id, old.boar_id, old.gilt_id, old.serving_id, old.location_id, old.prescription_id, old.treatment_group);
    end if;
    return null;
end;
$$ language plpgsql set search_path from current;

drop trigger if exists medicine_usage_link_auidtf on medicine_usage;
drop trigger if exists medicine_usage_link_auidr on medicine_usage;
create trigger medicine_usage_link_auidr
    after delete or insert or update of sow_id, boar_id, gilt_id, serving_id, location_id, prescription_id, actor_date, illnesstype_code, treatment_group, animals_count, amount_used
    on medicine_usage
    for each row
execute procedure medicine_usage_mark_for_link_aiudtf();

create or replace function medicine_storage_transfer_aiudrtf() returns trigger as
$$
declare prescription_ids bigint[];
begin
    if tg_op = 'UPDATE' then
        with x as (
            select new.prescription_id
            union select old.prescription_id
        )
        select array_agg(prescription_id) into prescription_ids from x;
    elsif tg_op = 'INSERT' then
        select array_agg(new.prescription_id) into prescription_ids;
    elsif tg_op = 'DELETE' then
        select array_agg(old.prescription_id) into prescription_ids;
    end if;
    perform common.recompute_current_prescriptions(prescription_ids);
    return null;
end;
$$ language plpgsql set search_path from current;

drop trigger if exists medicine_storage_transfer_aiudr on medicine_storage_transfer;
create trigger medicine_storage_transfer_aiudr
    after delete or insert or update of actor_date, amount, storage_id, to_storage_id, prescription_id, storage_transfer_type, sum
    on medicine_storage_transfer
    for each row
execute procedure medicine_storage_transfer_aiudrtf();

drop trigger if exists medicine_usage_link_auids on medicine_usage;
create trigger medicine_usage_link_auids
    after delete or insert or update of sow_id, boar_id, gilt_id, serving_id, location_id, prescription_id, actor_date, illnesstype_code, treatment_group, animals_count, amount_used
    on medicine_usage
    for each statement
execute procedure medicine_usage_mark_for_link_aiudstf();
