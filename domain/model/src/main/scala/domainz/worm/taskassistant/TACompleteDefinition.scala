package domainz.worm.taskassistant

import com.cloudfarms.wormshared.recurrence.DateGenerator
import com.cloudfarms.wormshared.enum.{ PSDurationGrouping => PSDurationGroupingK }
import domainz.common.AnimalType
import domainz.worm.DefinitionId
import domainz.worm.pigscreener.{ PSCompleteDefinition, PSDurationGrouping }
import pushka.annotation.pushka

import java.time.LocalDate
import sjsgrid.shared.common.PushkaPicklers._

import java.util

import kotlinx.datetime.ConvertersKt.toKotlinLocalDate
import kotlinx.datetime.ConvertersKt.toJavaLocalDate

@pushka
sealed trait TACompleteDefinition {
  def activeFrom: LocalDate
  def activeToOpt: Option[LocalDate]

  def repeatDays: Int

  def common: TACompleteDefinition.Common

  def commentOpt: Option[String]

  def showAhead: Int

  def recurrenceWeekDays: Seq[Int]

  def recurrenceDayOfMonthOpt: Option[Int]

  def durationGrouping: PSDurationGrouping
}

object TACompleteDefinition {
  @pushka
  case class Common(
    name: String,
    roleTypeCodeOpt: Option[String],
    assigneeIdOpt: Option[Long],
    defaultStartTime: java.time.LocalTime,
    defaultEndTimeOpt: Option[java.time.LocalTime],
  )

  case class PigScreener(psDefinitionId: DefinitionId.Live, psDefinition: PSCompleteDefinition, common: Common) extends TACompleteDefinition {

    override def activeFrom: LocalDate = psDefinition.activeFrom
    override def activeToOpt: Option[LocalDate] = psDefinition.activeToOpt

    override def repeatDays: Int = psDefinition.durationOpt.map(duration => duration + psDefinition.daysBetween).getOrElse(0)

    override def commentOpt: Option[String] = psDefinition.commentOpt

    override def showAhead: Int = psDefinition.eventShowAhead

    override def recurrenceWeekDays: Seq[Int] = psDefinition.recurrenceWeekDays

    override def recurrenceDayOfMonthOpt: Option[Int] = psDefinition.recurrenceDayOfMonthOpt

    override def durationGrouping: PSDurationGrouping = psDefinition.durationGrouping

  }

  case class TaskAssistant(
    commentOpt: Option[String],
    activeFrom: java.time.LocalDate,
    activeToOpt: Option[java.time.LocalDate],
    repeatDays: Int,
    animalTypeOpt: Option[AnimalType],
    // locationsUpdated: Option[java.time.Instant], Kedy bol naposledy updatnuty zoznam lokacii
    locationIds: Seq[Long],
    showAhead: Int, // null == 0
    common: Common,
    recurrenceWeekDays: Seq[Int],
    recurrenceDayOfMonthOpt: Option[Int],
    durationGrouping: PSDurationGrouping,
  ) extends TACompleteDefinition {
    import scala.jdk.CollectionConverters._
    private def dateGenerator(today: LocalDate = LocalDate.now()) = {
      new DateGenerator(
        toKotlinLocalDate(activeFrom),
        activeToOpt.map(toKotlinLocalDate).orNull,
        PSDurationGroupingK.fromDBShortName(durationGrouping.dbShortName),
        repeatDays,
        0, // no gaps in TaskAssistant
        showAhead,
        new util.ArrayList(recurrenceWeekDays.map(_.asInstanceOf[Integer]).toList.asJava),
        recurrenceDayOfMonthOpt.getOrElse(-1), // does not matter when not Monthly recurrence
        toKotlinLocalDate(today),
      )
    }
    def generateTaskDates(today: LocalDate = LocalDate.now()): Seq[LocalDate] =
      dateGenerator(today).generatePigScreeners().iterator().asScala.toSeq.map(ps => toJavaLocalDate(ps.getDateFrom))
  }

  keepPushkaPicklersImport
}
