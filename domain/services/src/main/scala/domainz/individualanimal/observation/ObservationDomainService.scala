package domainz.individualanimal.observation

import com.cloudfarms.pigsdomain.state.{ Environment, ServedSow }
import common.ValidatedResult
import domainz.individualanimal.observation.ObservationDomainService.IsEntered
import domainz.individualanimal.sow.SowDomainService
import domainz.individualanimal.{ HopDomainService, IndividualAnimalId }
import domainz.integration.genesus.GenesusDomainService
import scalikejdbc.DBSession
import sjsgrid.shared.form.DynamicValue
import utils.IsSomethingEnteredChecker

import java.sql.Connection
import java.time.Instant
import javax.inject.{ Inject, Singleton }

import domainz.individualanimal.sow.timeline.KotlinxDatetimeOps._

object ObservationDomainService {
  case class IsEntered(hns: Boolean, hnsReason: Boolean, teats: Boolean)
}

/**
  * Observation was originally called backfat but the scope increased and it was renamed to observation.
  */
@Singleton
class ObservationDomainService @Inject() (
  observationRepository: ObservationRepository,
  sowDomainService: SowDomainService,
  hopDomainService: HopDomainService,
  genesusDomainService: GenesusDomainService,
) {

  // TODO Add tests

  private[observation] def validate(individualAnimalId: IndividualAnimalId, actorDate: Instant, isEntered: IsEntered)(implicit
    dbSession: DBSession,
    env: Environment,
  ) = {

    // Animal should also be active but the responsibility to check that is in the HOP domain

    ValidatedResult
      .unit
      .withRequireNotTrue(isEntered.hnsReason && !isEntered.hns, "HNS reason entered but HNS is false")
      .withRequireNotTrue(isEntered.hns && individualAnimalId.indAnimalType.male, "HNS is true but the animal is not a female")
      .withRequireNotTrue(isEntered.teats && individualAnimalId.indAnimalType.male, "Teats data entered but the animal is not a female")
      .withWarningOpt(
        if (!isEntered.hns) None
        else {
          val sowStateOpt = for {
            sowId <- individualAnimalId.sowIdOpt // timeline applies only if animal is a sow
            sow <- sowDomainService.getSowStateAt(sowId, actorDate)
          } yield sow
          import scala.jdk.CollectionConverters._
          sowStateOpt.collect { case served: ServedSow => ObservationIssue.HnsSowServed(actorDate, served.getCycles.asScala.last.getStart) }
        },
      )
  }

  private val isSomethingEnteredChecker = IsSomethingEnteredChecker[ObservationPersistence.UpsertData, qfarm.Backfat] { check =>
    IsEntered(
      hns = check(_.heatNoService, _.heatNoService),
      hnsReason = check(_.heatNoServiceReasonCode, _.heatNoServiceReasonCode),
      teats = check(_.teatsLeft, _.teatsleft)(_.teatsRight, _.teatsright)(_.teatsDefective, _.teatsdefective),
    )
  }

  /**
    * Inserts or updates the observation data depending if there is a backfat record associated to the HOP.
    * Because this is often stored together with HOP, some HOP parameters are passed to this method.
    *
    * @param hopId              HOP id
    * @param individualAnimalId ID of the animal from the HOP.
    * @param actorDate          Actor date from the HOP
    * @param data               Data to be inserted or updated
    */
  // We can create a variant of this method that only needs the hopId and fetches individualAnimalId and actorDate from the DB.
  def upsert(hopId: Long, individualAnimalId: IndividualAnimalId, actorDate: Instant, data: ObservationPersistence.UpsertData)(implicit
    con: Connection,
    dbSession: DBSession,
    env: Environment,
  ): ValidatedResult[ObservationIssue.HnsSowServed, Nothing, Unit] = {

    observationRepository.get(hopId) match {
      // There is already a observation in the database for the HOP
      case Some(persisted) =>
        update(hopId, individualAnimalId, actorDate, persisted, data)

      // There is NO observation in the database for the HOP
      case None =>
        insert(hopId, individualAnimalId, actorDate, data)
    }
  }

  private def dataToWrite(data: ObservationPersistence.UpsertData, hopId: DynamicValue[Long] = DynamicValue.Unavailable) = {
    import io.scalaland.chimney.dsl._

    data
      .into[qfarm.write.Backfat]
      .withFieldRenamed(_.bodyCondition, _.bodycondition)
      .withFieldRenamed(_.feedCurve, _.feedcurve)
      .withFieldRenamed(_.teatsLeft, _.teatsleft)
      .withFieldRenamed(_.teatsRight, _.teatsright)
      .withFieldRenamed(_.teatsDefective, _.teatsdefective)
      .withFieldRenamed(_.bodyLength, _.bodylength)
      .withFieldConst(_.id, hopId)
      .transform
  }

  private def insert(
    hopId: Long,
    individualAnimalId: IndividualAnimalId,
    actorDate: Instant,
    data: ObservationPersistence.UpsertData,
  )(implicit con: Connection, dbSession: DBSession, env: Environment) = {

    validate(
      individualAnimalId = individualAnimalId,
      actorDate = actorDate,
      isEntered = isSomethingEnteredChecker.inInsert(data),
    ).map { _ =>
      val backfatWrite = dataToWrite(data, hopId)
      observationRepository.insertBackfat(backfatWrite)
    }
  }

  private def update(
    hopId: Long,
    individualAnimalId: IndividualAnimalId,
    actorDate: Instant,
    persisted: => qfarm.Backfat,
    data: ObservationPersistence.UpsertData,
  )(implicit con: Connection, dbSession: DBSession, env: Environment) = {

    validate(
      individualAnimalId = individualAnimalId,
      actorDate = actorDate,
      isEntered = isSomethingEnteredChecker.inUpdate(data, persisted),
    ).ignoreRepeatedIssues(validate(
      individualAnimalId = individualAnimalId,
      actorDate = actorDate,
      isEntered = isSomethingEnteredChecker.inPersisted(persisted),
    ))
      .map { _ =>
        val backfatWrite = dataToWrite(data)

        observationRepository.updateBackfat(hopId, backfatWrite)
      }
  }

  // THIS IS CURRENTLY ONLY USED IN TESTS
  // TODO add tests
  def insertWholeObservation(insertData: ObservationPersistence.InsertWholeData)(implicit
    con: Connection,
    dbSession: DBSession,
    env: Environment,
  ) = {
    import insertData._

    for {
      hopId <- hopDomainService.insert(hopData).mapWarnings(ObservationIssue.HopWarning)
      _ <- upsert(hopId, hopData.individualAnimalId, hopData.actorDate, observationData)
      _ <- genesusDataOpt match {
        case Some(genesusData) => genesusDomainService.insertProbe(hopId, genesusData)
        case None              => ValidatedResult.unit
      }
    } yield hopId
  }

  def deleteWholeObservation(hopId: Long)(implicit con: Connection) = {
    ValidatedResult.success(observationRepository.delete(hopId)) *>
      genesusDomainService.delete(hopId) *>
      hopDomainService.delete(hopId)
  }
}
