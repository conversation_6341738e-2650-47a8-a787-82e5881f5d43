import { describe, expect, test } from "@jest/globals";
import "pigbot-core/test/jest.setup";
import { TestData } from "pigbot-core/test/TestData";

import { DefaultTestTimeout, mockTemporal, parseResponseStream } from "pigbot-core/test/jest.setup";

describe("FeedbackService", () => {
	test(
		"compareValidations",
		async () => {
			mockTemporal();

			// Modules must be loaded in the test otherwise they would initialize with incorrect process.env
			const { getEfficiencyReportSummary } = await import("pigbot-core/src/eff-report/EfficiencyReportService");

			const response = getEfficiencyReportSummary(TestData.request, TestData.testUserContext);
			await parseResponseStream(response);
			const module = await import("./PromptValidationService");
			const result = await module.compareValidations({});

			expect(result.brokenValidations).toHaveLength(0);
		},
		DefaultTestTimeout,
	);
});
