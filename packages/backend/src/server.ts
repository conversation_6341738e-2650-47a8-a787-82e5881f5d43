import "pigbot-core/src/Config";

import express from "express";
import cors from "cors";
import * as trpcExpress from "@trpc/server/adapters/express";
import { appRouter, createContext } from "./trpcServer";
import { rateLimit } from "express-rate-limit";
import { RedisStore } from "rate-limit-redis";
import { localRedisClient } from "pigbot-core/src/RedisClient";
import Config from "pigbot-core/src/Config";
import authenticateJWT from "./JWTAuthentication";
import logger from "pigbot-core/src/logger";
import { getEfficiencyReportPDF } from "pigbot-core/src/eff-report/pdf/EfficiencyReportExportPdfService";
import { getPdfData } from "./vfamain/VFAMainService";

const app = express();

const limiter = rateLimit({
	windowMs: 1 * 60 * 1000,
	limit: 5, // Limit each IP to 100 requests per `window`.
	standardHeaders: "draft-7", // draft-6: `RateLimit-*` headers; draft-7: combined `RateLimit` header
	legacyHeaders: false, // Disable the `X-RateLimit-*` headers.
	store: new RedisStore({
		// @ts-expect-error - Known issue: the `call` function is not present in @types/ioredis
		sendCommand: (...args: string[]) => localRedisClient.call(...args),
	}),
});

app.use(cors());

if (Config.NODE_ENV === "production") {
	app.use("/trpc/getEfficiencyReportSummary", limiter);
}

app.use(
	"/trpc",
	authenticateJWT,
	trpcExpress.createExpressMiddleware({
		router: appRouter,
		// This enables us to use POST for query requests which is necessary to avoid the 414 URI Too Long error
		allowMethodOverride: true,
		onError({ error }) {
			// Without this, errors are silently ignored
			logger.error("Error:", error);
		},
		createContext,
	}),
);

app.get("/health", (req, res) => {
	res.status(200).send("OK");
});

// PDF download URL
// It's not authenticated. The ID is a hash that can't be guessed.
app.get("/efficiency-report-pdf/:id", async (req, res) => {
	try {
		const responseId = req.params.id;

		const pdfData = await getEfficiencyReportPDF({
			responseId: responseId,
			hiddenSections: req.query.hiddenSections ? new Set((req.query.hiddenSections as string).split(",")) : new Set(),
		});

		// Set appropriate headers for PDF download
		res.setHeader("Content-Type", "application/pdf");
		res.setHeader(
			"Content-Disposition",
			`attachment; filename="vfa-analysis-${pdfData.farm.replace(/[^a-zA-Z0-9]/g, "-")}${pdfData.reportDate ? `-${pdfData.reportDate}` : ""}.pdf"`,
		);
		res.setHeader("Content-Length", pdfData.pdf.length);

		// Send the PDF data
		res.send(Buffer.from(pdfData.pdf));
	} catch (error) {
		logger.error("Error generating PDF:", error);
		res.status(500).send("Error generating PDF");
	}
});

app.get("/pdf-data/:docName", authenticateJWT, async (req, res) => {
	try {
		const documentName = req.params.docName;
		const pdfContent = await getPdfData(documentName);
		res.setHeader("Content-Type", "application/pdf");
		res.setHeader("Content-Length", pdfContent.length);
		res.send(Buffer.from(pdfContent));
	} catch (error) {
		logger.error(`Error while loading PDF: ${error}`);
		res.status(500).send("Error while loading PDF");
	}
});

const port = Config.BACKEND_PORT;

// Start the server
app.listen(port, () => {
	logger.info(`Server is running on http://localhost:${port}`);
});
