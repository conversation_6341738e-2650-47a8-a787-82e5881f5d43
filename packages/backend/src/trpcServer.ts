import { initTRPC } from "@trpc/server";

import * as trpcExpress from "@trpc/server/adapters/express";
import { AuthenticatedRequest } from "./JWTAuthentication";
import { deleteFeedback, DeleteFeedback, SaveFeedback, saveFeedback } from "./feedback/FeedbackService";
import assert from "assert";
import { guessEnvFromRootId, hasAtLeast } from "pigbot-core/src/UserContext";
import { reloadDixaArticles } from "./supportbot/DixaService";
import { getFallbackPrompt } from "pigbot-core/src/prompt-template/fallback/FallbackPromptTemplates";
import {
	EffReportInternalChunk,
	GetEfficiencyReportSummary,
	getEfficiencyReportSummary,
	getEfficiencyReportSummaryInternal,
} from "pigbot-core/src/eff-report/EfficiencyReportService";
import { TemplateOverrides } from "pigbot-core/src/prompt-template/PromptTemplate";
import { EffReportSummaryResponseChunk } from "pigbot-core/src/eff-report/EffReportSummaryResponseChunk";
import { compareValidations } from "./validation/PromptValidationService";
import { numberOfFiles, supportChatbotService } from "./supportbot/SupportChatbotService";
import { ChatbotMessageParams, SupportChatbotResponseChunk } from "pigbot-core/src/supportbot/SupportChatbotTypes";
import { getLatestAnalysis, GetLatestEfficiencyReportSummaryRequest, getSowFarmLocationsAndPeriodInfo } from "./vfamain/VFAMainService";

export const createContext = (opts: trpcExpress.CreateExpressContextOptions) => {
	const authReq = opts.req as AuthenticatedRequest;
	return {
		userContext: authReq.userContext!,
	};
};
type Context = Awaited<ReturnType<typeof createContext>>;

const t = initTRPC.context<Context>().create();

const privateProcedure = t.procedure;

//This workaround is necessary for errors to be propagated to frontend.
export function captureAsyncGeneratorErrorsAndPreventNetworkTimeout<T>(gen: AsyncGenerator<T>, dummyChunk: T): AsyncGenerator<T> {
	return (async function* () {
		// The 'inFlight' thing prevents network timeout by sending dummyChunk every 30s if nothing is sent.

		// We'll keep track of one "in-flight" call to gen.next()
		// so that if a timeout wins, we don't lose any real item that might eventually arrive.
		let inFlight: Promise<IteratorResult<T>> | null = null;

		try {
			while (true) {
				// If we don't already have a next() in progress, start one
				if (!inFlight) {
					inFlight = gen.next();
				}

				// Race the in-flight gen.next() against a 30s timeout
				const result = await Promise.race([
					inFlight,
					new Promise<IteratorResult<T>>((resolve) =>
						setTimeout(() => {
							// If 30s elapse before gen.next() resolves,
							// resolve with an artificial chunk (not done).
							resolve({ value: dummyChunk, done: false });
						}, 30_000),
					),
				]);

				// If the generator indicated it's done, return.
				if (result.done) {
					return;
				}

				// If we got our dummyChunk, just yield it,
				// but keep waiting for the inFlight real value in future iterations.
				if (result.value === dummyChunk) {
					yield result.value;
					// we do NOT clear inFlight, so the real gen.next() can still eventually resolve
				} else {
					// We got a real chunk! Yield it and reset inFlight
					yield result.value;
					inFlight = null;
				}
			}
		} catch (e) {
			// If gen throws, yield one dummy chunk first (so the client sees something)
			yield dummyChunk;
			throw e;
		}
	})();
}

export const appRouter = t.router({
	saveFeedback: privateProcedure
		.input((value): SaveFeedback => {
			return value as SaveFeedback;
		})
		.mutation(function (opts) {
			assert(hasAtLeast("power-user", opts.ctx.userContext), "Unauthorized");

			return saveFeedback(opts.input, opts.ctx.userContext);
		}),
	deleteFeedback: privateProcedure
		.input((value): DeleteFeedback => {
			return value as DeleteFeedback;
		})
		.mutation(function (opts) {
			assert(hasAtLeast("power-user", opts.ctx.userContext), "Unauthorized");
			return deleteFeedback(opts.input, opts.ctx.userContext);
		}),
	reloadDixaArticles: privateProcedure.mutation(function () {
		return reloadDixaArticles();
	}),
	getFallbackPrompt: privateProcedure
		.input((value): string => {
			return value as string;
		})
		.query(function (opts): string {
			return getFallbackPrompt(opts.input);
		}),
	getEfficiencyReportSummaryInternal: privateProcedure
		.input(
			(
				value,
			): {
				request: GetEfficiencyReportSummary;
				templateOverrides: TemplateOverrides;
				farmId: number;
				rootId: number;
			} => {
				return value as {
					request: GetEfficiencyReportSummary;
					templateOverrides: TemplateOverrides;
					farmId: number;
					rootId: number;
				};
			},
		)
		.query(function (opts) {
			assert(hasAtLeast("backoffice", opts.ctx.userContext), "Unauthorized");

			return captureAsyncGeneratorErrorsAndPreventNetworkTimeout<EffReportInternalChunk>(
				getEfficiencyReportSummaryInternal({
					request: opts.input.request,
					templateOverrides: opts.input.templateOverrides,
					farmHoldingId: {
						farmId: opts.input.farmId,
						rootId: opts.input.rootId,
						env: guessEnvFromRootId(opts.input.rootId), // TODO store env in DB
					},
				}),
				"",
			);
		}),
	getEfficiencyReportSummary2: privateProcedure
		.input((value): GetEfficiencyReportSummary => {
			return value as GetEfficiencyReportSummary;
		})
		.query(function (opts) {
			return captureAsyncGeneratorErrorsAndPreventNetworkTimeout<EffReportSummaryResponseChunk>(
				getEfficiencyReportSummary(opts.input, opts.ctx.userContext),
				"",
			);
		}),
	getLatestEfficiencyReportSummary: privateProcedure
		.input((request): GetLatestEfficiencyReportSummaryRequest => {
			return request as GetLatestEfficiencyReportSummaryRequest;
		})
		.query(async function (opts) {
			assert(hasAtLeast("user", opts.ctx.userContext), "Unauthorized");

			return captureAsyncGeneratorErrorsAndPreventNetworkTimeout<EffReportSummaryResponseChunk>(
				getLatestAnalysis(opts.input, opts.ctx.userContext),
				"",
			);
		}),
	getSowFarmLocationsAndPeriodLength: privateProcedure.query(async function (opts) {
		assert(hasAtLeast("user", opts.ctx.userContext), "Unauthorized");
		// merged getSowFarmLocations and getPeriodLength into single call
		return await getSowFarmLocationsAndPeriodInfo(opts.ctx.userContext);
	}),
	compareValidations: privateProcedure
		.input((value): TemplateOverrides => {
			return value as TemplateOverrides;
		})
		// Claude suggested making this a mutation even though it isn't a mutation.
		// This function is called on demand, and trpc supports this only for mutations.
		.mutation(function (opts) {
			assert(hasAtLeast("backoffice", opts.ctx.userContext), "Unauthorized");

			return compareValidations(opts.input);
		}),
	supportChatbotService: privateProcedure
		.input((value): ChatbotMessageParams => {
			return value as ChatbotMessageParams;
		})
		// Another function on demand
		.mutation(function (opts) {
			return captureAsyncGeneratorErrorsAndPreventNetworkTimeout<SupportChatbotResponseChunk>(
				supportChatbotService(opts.input, opts.ctx.userContext),
				"",
			);
		}),
	numberOfSupportChatbotFiles: privateProcedure.query(function () {
		return numberOfFiles();
	}),
});

export type AppRouter = typeof appRouter;
