/** @jsxImportSource @emotion/react */
import { ApolloProvider } from '@apollo/client';
import GraphQlClient from '../common/GraphQLClient';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { unstable_httpBatchStreamLink } from '@trpc/client';
import type * as ReactTypes from 'react';
import React, { createContext, useContext, useState } from 'react';
import TrpcReact from '@src/common/TrpcReact';
import FrontendConfig from '@src/common/FrontendConfig';
import { UserContext, userContextSchema } from 'pigbot-core/src/UserContext';
import { jwtDecode } from 'jwt-decode';

interface Props {
	jwtToken: string;
	languageCode: string;
	dateFormat: string;
	children: ReactTypes.ReactNode | ReactTypes.ReactNode[];
}

const UserContextContext = createContext<UserContext>(null!);

const LanguageCodeContext = createContext<string>(null!);

const DateFormatContext = createContext<string>(null!);

export function useUserContext() {
	return useContext(UserContextContext);
}

export function useLanguageCode() {
	return useContext(LanguageCodeContext);
}

export function useDateFormat() {
	return useContext(DateFormatContext);
}

function Providers(props: Props) {
	const [graphQlClient] = useState(() => GraphQlClient(props.jwtToken));

	const [queryClient] = useState(
		() =>
			new QueryClient({
				defaultOptions: {
					queries: {
						refetchOnWindowFocus: false,
					},
				},
			}),
	);
	const [trpcClient] = useState(() =>
		TrpcReact.createClient({
			links: [
				unstable_httpBatchStreamLink({
					url: `${FrontendConfig.BACKEND_BASE_URL}/trpc`,
					// This enables us to use POST for query requests which is necessary to avoid the 414 URI Too Long error
					methodOverride: 'POST',
					// You can pass any HTTP headers you wish here
					async headers() {
						return {
							authorization: `Bearer ${props.jwtToken}`,
						};
					},
				}),
			],
		}),
	);

	return (
		<DateFormatContext.Provider value={props.dateFormat}>
			<LanguageCodeContext.Provider value={props.languageCode}>
				<UserContextContext.Provider value={userContextSchema.parse(jwtDecode(props.jwtToken))}>
					<TrpcReact.Provider client={trpcClient} queryClient={queryClient}>
						<QueryClientProvider client={queryClient}>
							<ApolloProvider client={graphQlClient}>{props.children}</ApolloProvider>
						</QueryClientProvider>
					</TrpcReact.Provider>
				</UserContextContext.Provider>
			</LanguageCodeContext.Provider>
		</DateFormatContext.Provider>
	);
}

export default Providers;
