/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React, { useMemo, useState } from 'react';
import 'antd/lib/menu/style/index.less';
import 'antd/lib/tabs/style/index.less';
import Button from 'antd/lib/button';
import { ReportRequestData } from './ReportRequestCards';
import Skeleton from 'antd/lib/skeleton';
import Collapse from 'antd/lib/collapse';
import 'antd/lib/collapse/style/index.less';
import { match, P } from 'ts-pattern';
import EffReportTable from '@src/components/settings/prompt/EffReportTable';
import RadioGroup from 'antd/lib/radio/group';
import RadioButton from 'antd/lib/radio/radioButton';
import 'antd/lib/radio/style/index.less';
import TrpcReact from '@src/common/TrpcReact';
import { Pending } from '@src/components/Pending';
import type { IdentifyIssuesInternalResponse } from 'pigbot-core/src/eff-report/identifyIssues';
import type { EffReportInternalChunk } from 'pigbot-core/src/eff-report/EfficiencyReportService';
import { LoadingError } from '@src/components/common/LoadingError';
import ReactJson from 'react-json-view';
import PigbotMarkdown from '@src/components/common/PigbotMarkdown';
import Empty from 'antd/lib/empty/empty';
import type { RenderedPromptTemplate, TemplateOverrides } from 'pigbot-core/src/prompt-template/PromptTemplate';
import { useDebounce } from 'use-debounce';
import { EffReportSummaryView } from '@src/components/report_assistant/EffReportSummaryView';
import { EffReportSummaryResponseChunk, internalChunk2CustomerChunk } from 'pigbot-core/src/eff-report/EffReportSummaryResponseChunk';
import { observer } from 'mobx-react-lite';
import { extractTextFromStream } from 'pigbot-core/src/utils/extractTextFromStream';
import { FeedbackData } from '@src/components/settings/prompt/ReportOrFeedbackSelector';
import { PersistedResponseData } from 'pigbot-backend/src/feedback/FeedbackService';
import type { GetRelevantDataResponse } from 'pigbot-core/src/relevant-data/RelevantDataService';
import { SimpleChatMessage } from 'pigbot-core/src/eff-report/SimpleChatMessage';
import { AnalyzeCausesInternalResponse } from 'pigbot-core/src/eff-report/analyzeCauses';
import { Panel } from '@src/components/common/Panel';
import { ResponseWithFeedback } from '@src/components/settings/prompt/ResponseWithFeedback';
import { EfficiencyReportData } from 'pigbot-core/src/EfficiencyReportData';
import { useLanguageCode, useUserContext } from '@src/components/Providers';
import { EffReportSummaryViewerState } from '@src/components/report_assistant/EffReportSummaryViewerState';
import { extractDataFromEffReportAnalysisArray } from 'pigbot-core/src/eff-report/ExtractDataFromEffReportAnalysisArray';
import { ReportToolbar } from '@src/components/report_assistant/ReportToolbar';
import { AnalysisOpenedStates } from '@src/components/report_assistant/AnalysisOpenedStates';

export type PromptPreviewProps = {
	selected: FeedbackData | ReportRequestData;
	templateOverrides: TemplateOverrides;
	deselect: () => void;
};

const SmallCollapse = css`
	overflow: auto;

	.ant-collapse-header {
		padding-top: 5px !important;
		padding-bottom: 5px !important;
	}

	.ant-collapse-content-box {
		padding: 8px;
	}
`;

/**
 * Renders a both feedback and report request preview.
 */
export const ReportDataViewer: React.FC<PromptPreviewProps> = observer((props) => {
	const userContext = useUserContext();

	const [analysisState] = useState(() => new AnalysisOpenedStates());

	// Get the type of the selected item and report data
	const { previewType, reportData } = match(props.selected)
		.with({ feedback: P.any }, (feedback) => ({
			previewType: 'feedback' as const,
			reportData: feedback.response.reportData as EfficiencyReportData,
		}))
		.otherwise((promptRequest: ReportRequestData) => ({ previewType: 'reportRequest' as const, reportData: promptRequest.reportData }));

	const [templateOverridesDebounced] = useDebounce(props.templateOverrides, 1000);

	const responseViewerState = useMemo(() => new EffReportSummaryViewerState(), [props.selected.id]);

	// State for switching between feedback and new response view
	const [view, setView] = useState(() => {
		const selected = previewType === 'feedback' ? ('feedback' as const) : ('new-response' as const);
		return {
			selected,
			newResponseEnabled: selected === 'new-response',
		};
	});

	const languageCode = useLanguageCode();

	// Get a new response from backend.
	// Backend is called even if feedback is selected.
	// This streams not only the final response but also data about all intermediate steps
	// that are rendered in different panels.
	const getEfficiencyReportSummaryInternal = TrpcReact.getEfficiencyReportSummaryInternal.useQuery(
		{
			request: {
				report: reportData,
				idx: responseViewerState.completionIdx,
				targetLanguage: languageCode,
			},
			templateOverrides: templateOverridesDebounced,
			farmId: props.selected.farm_id,
			rootId: props.selected.root_id,
		},
		{ retry: false, enabled: view.newResponseEnabled },
	);

	/**
	 * Extracts panel data from response stream array
	 * @param streamArray
	 */
	function extractPanelData(streamArray: EffReportInternalChunk[]) {
		const emptyResponse = {
			reportGrid: reportData,
			feedback: null,
			summaryPrompt: Pending as RenderedPromptTemplate | Pending,
			userSummaryStream: [] as null | EffReportSummaryResponseChunk[],
			issuesIdentification: Pending as IdentifyIssuesInternalResponse | Pending,
			introductionPrompt: Pending as RenderedPromptTemplate | Pending,
			relevantData: Pending as GetRelevantDataResponse | Pending,
			analyzeCauses: Pending as AnalyzeCausesInternalResponse | Pending,
			conflictingGoals: null as string[] | null,
			version: null,
		};

		return streamArray.reduce((acc, chunk: EffReportInternalChunk) => {
			let extracted;
			try {
				extracted = match(chunk)
					.with({ type: 'identifyIssuesResponse' }, (data) => ({
						...acc,
						issuesIdentification: data.identifyIssuesResponse,
					}))
					.with({ type: 'summarizationMetadata' }, (value) => ({
						...acc,
						summaryPrompt: value.prompt,
					}))
					.with({ type: 'usage' }, (value) => ({
						...acc,
						usage: value,
					}))
					.with({ type: 'introduction' }, (value) => ({
						...acc,
						introductionPrompt: value.prompt,
					}))
					.with({ type: 'getRelevantData' }, (value) => ({
						...acc,
						relevantData: value.relevantDataResponse,
					}))
					.with({ type: 'analyzeCausesResponse' }, (value) => ({
						...acc,
						analyzeCauses: value.analyzeCausesResponse,
					}))
					.with({ type: 'conflictingGoals' }, ({ conflictingGoals }) => ({
						...acc,
						conflictingGoals,
					}))
					// These are ignored
					.with(P.string, () => acc)
					.with({ type: 'farmInfo' }, () => acc)
					.with({ type: 'responseId' }, () => acc)
					.exhaustive();
			} catch (e) {
				// If stored data don't correspond to the expected format and extraction of a chunk fails, it is simply ignored.
				// eslint-disable-next-line
				console.error(e);
				return acc;
			}

			// Convert internal to chunk that is passed to a component
			// that renders the response in both prompt management and eff report screen.
			const convertedChunks = internalChunk2CustomerChunk(chunk, userContext.role);

			return {
				...extracted,
				userSummaryStream: convertedChunks ? [...(acc.userSummaryStream ?? []), ...convertedChunks] : acc.userSummaryStream,
			};
		}, emptyResponse);
	}

	// Data for the new response panel
	const newResponsePanelData = useMemo(() => {
		// Go through the stream and extract the data
		return extractPanelData(getEfficiencyReportSummaryInternal.data ?? []);
	}, [getEfficiencyReportSummaryInternal.data]);

	// Final panel data depend on the selected view
	const panelData = match(props.selected)
		.with({ feedback: P.any }, (feedback) =>
			view.selected === 'feedback'
				? match(feedback.response as PersistedResponseData)
						.with({ version: P._ }, (response) => {
							// vfa-v2 feedback stores the same response stream as internal response
							// so we can extract the data from it the same way.
							const extractedData = extractPanelData(response.internalResponseStreamArray);

							return {
								...extractedData,
								feedback: {
									responseMD: extractTextFromStream(response.internalResponseStreamArray),
									feedback: feedback.feedback,
								},
								version: feedback.response.version,
							};
						})
						// v1 data
						.with({ responseText: P.string }, (response1) => ({
							reportGrid: feedback.response.reportData,
							feedback: {
								responseMD: response1.responseText,
								feedback: feedback.feedback,
							},
							userSummaryStream: null,
							summaryPrompt: null,
							issuesIdentification: null,
							introductionPrompt: null,
							relevantData: null,
							analyzeCauses: null,
							version: null,
						}))
						.exhaustive()
				: newResponsePanelData,
		)
		// parameter is defined and typed for compile-time safety
		// eslint-disable-next-line @typescript-eslint/no-unused-vars
		.otherwise((ignored: ReportRequestData) => newResponsePanelData);

	/**
	 * Render a panel that shows an item from the context.
	 * @param header Header of the panel
	 * @param key Key of the item in the context
	 * @param format markdown or plain text
	 */
	function renderItemContextPanel(header: string, key: string, format: 'md' | 'text', context: Record<string, unknown> | string | Pending) {
		const value = match(context)
			.with(Pending, () => Pending)
			.with(P.string, (str) => str)
			.with(P.any, (record) => (record[key] ? (record[key] as string) : null))
			.exhaustive();

		return (
			<Panel key={key} header={header}>
				{value === Pending ? (
					<Skeleton />
				) : value ? (
					match(format)
						.with('md', () => <PigbotMarkdown>{value}</PigbotMarkdown>)
						.with('text', () => (
							<div
								css={css`
									white-space: break-spaces;
								`}
							>
								{value}
							</div>
						))
						.exhaustive()
				) : (
					<Empty />
				)}
			</Panel>
		);
	}

	/**
	 * Renders the context JSON.
	 */
	function renderContextPanel(context?: unknown) {
		return (
			<Panel key='context' header='Context'>
				{context === Pending ? (
					<Skeleton />
				) : (
					<ReactJson src={context ?? {}} displayDataTypes={false} enableClipboard={false} collapseStringsAfterLength={100} />
				)}
			</Panel>
		);
	}

	function renderMessages(messages?: SimpleChatMessage[]) {
		return (
			messages?.map((message, idx) =>
				renderItemContextPanel(
					`${idx}. ${message.role.toUpperCase()}: ${message.content.substring(0, 50)}..`,
					`${idx}`,
					message.role === 'assistant' ? 'md' : 'text',
					message.content,
				),
			) ?? <Empty />
		);
	}

	/**
	 * Renders the metadata of the prompt into several panels.
	 */
	function renderPromptMetadata(renderedPrompt: RenderedPromptTemplate | undefined) {
		return [
			renderItemContextPanel('Prompt', 'prompt', 'text', renderedPrompt?.content),
			renderContextPanel(renderedPrompt?.context),
			renderItemContextPanel('SOPs', 'SOPs', 'md', renderedPrompt?.context),
			renderItemContextPanel('Seges manual', 'segesManual', 'md', renderedPrompt?.context),
			renderItemContextPanel('South West Vets SOPs', 'southWestVetsSOPs', 'md', renderedPrompt?.context),
			renderItemContextPanel('Report text', 'reportText', 'text', renderedPrompt?.context),
		];
	}

	const summaryViewerData = useMemo(
		() => (panelData.userSummaryStream ? extractDataFromEffReportAnalysisArray(panelData.userSummaryStream) : null),
		[panelData.userSummaryStream],
	);

	return (
		<div
			css={css`
				display: flex;
				flex: 1;
				flex-direction: column;
				gap: 10px;
			`}
		>
			<div
				css={css`
					display: flex;
					flex-direction: row;
					justify-content: space-evenly;
				`}
			>
				{/*View type switcher. Visible only for feedback, because for report request there is nothing to switch. */}
				{previewType === 'feedback' && (
					<RadioGroup
						buttonStyle='solid'
						value={view.selected}
						onChange={(e) =>
							setView((v) => ({
								selected: e.target.value,
								newResponseEnabled: v.newResponseEnabled || e.target.value === 'new-response',
							}))
						}
					>
						<RadioButton value='feedback'>Feedback</RadioButton>
						<RadioButton value='new-response'>New response</RadioButton>
					</RadioGroup>
				)}

				<Button type='default' icon='rollback' onClick={() => props.deselect()}>
					Back to selection
				</Button>
			</div>

			{getEfficiencyReportSummaryInternal.error && <LoadingError error={getEfficiencyReportSummaryInternal.error} />}

			{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
			<Collapse css={SmallCollapse} defaultActiveKey={['summary']} accordion={true}>
				{/*Report panel*/}
				<Panel key='reportGrid' header='Report'>
					<EffReportTable effReportResponse={panelData.reportGrid} />
				</Panel>

				{/*Summary panel*/}
				<Panel key='summary' header='Summary (III)'>
					{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
					<Collapse css={SmallCollapse} defaultActiveKey={['response']}>
						{/*Show feedback if available or response*/}
						{panelData.feedback !== null ? (
							<Panel key='response' header='Feedback'>
								<ResponseWithFeedback
									responseMD={panelData.feedback.responseMD}
									feedback={panelData.feedback.feedback}
									analysis={panelData.analyzeCauses?.map((c) => `${c.analysis}---${c.usedData}`) || []}
									version={panelData.version}
									analysisOpenedStates={analysisState}
								/>
							</Panel>
						) : (
							summaryViewerData !== null && (
								<Panel key='response' header='Response'>
									<EffReportSummaryView
										data={summaryViewerData}
										internalToolsVisible
										state={responseViewerState}
										analysisOpenedStates={analysisState}
										handleSOPLinkClick={null}
										handleIssueMouseOver={null}
									/>

									<ReportToolbar
										data={summaryViewerData}
										editFeedback={null}
										viewState={responseViewerState}
										disableButtons={summaryViewerData.responseId == null}
										displayRegenerateButton={true}
									/>
								</Panel>
							)
						)}
						{panelData.summaryPrompt !== null && renderPromptMetadata(panelData.summaryPrompt)}
						{renderItemContextPanel(
							'Raw response',
							'raw',
							'text',
							panelData.feedback
								? panelData.feedback.responseMD
								: panelData.userSummaryStream
									? extractTextFromStream(panelData.userSummaryStream)
									: Pending,
						)}
					</Collapse>
				</Panel>

				{panelData.analyzeCauses !== null && (
					<Panel key='causes-analysis' header='Causes Analysis (II)'>
						{panelData.analyzeCauses !== Pending ? (
							<>
								{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
								<Collapse css={SmallCollapse} defaultActiveKey={['cause']}>
									{panelData.analyzeCauses.map((cause, idx) => (
										<Panel key={idx} header={`${String.fromCharCode(65 + idx)}. ${cause.issue.title.substring(0, 50)}..`}>
											{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
											<Collapse css={SmallCollapse} defaultActiveKey={['cause']}>
												{renderMessages(cause.messages)}
												{renderItemContextPanel('Cause', 'cause', 'md', cause.cause)}
												{renderItemContextPanel('Analysis', 'analysis', 'md', cause.analysis)}
											</Collapse>
										</Panel>
									))}
								</Collapse>
							</>
						) : (
							<Skeleton />
						)}
					</Panel>
				)}

				{/*Issue identification panel. It is not visible for feedback.*/}
				{panelData.issuesIdentification !== null && (
					<Panel key='issue-identification' header='Issue identification (I)'>
						{panelData.issuesIdentification === Pending ? (
							<Skeleton />
						) : (
							<>
								{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
								<Collapse css={SmallCollapse} defaultActiveKey={['response']} accordion={true}>
									{renderMessages(panelData.issuesIdentification.messages)}
									<Panel key='response' header='Response'>
										{/*Response of the issue identification is JSON*/}
										<ReactJson src={panelData.issuesIdentification.response.issues} displayDataTypes={false} enableClipboard={false} />
									</Panel>
								</Collapse>
							</>
						)}
					</Panel>
				)}

				{/*Issue identification panel. It is not visible for feedback.*/}
				{panelData.relevantData !== null && (
					<Panel key='relevant-data' header='Relevant data'>
						{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
						<Collapse css={SmallCollapse} defaultActiveKey={['response']} accordion={true}>
							<Panel key='response' header='Response'>
								{panelData.relevantData === Pending ? (
									<Skeleton />
								) : (
									// Response of the issue identification is JSON
									<ReactJson src={panelData.relevantData} displayDataTypes={false} enableClipboard={false} />
								)}
							</Panel>
						</Collapse>
					</Panel>
				)}

				{/*Introduction panel. It is not visible for feedback*/}
				{panelData.introductionPrompt !== null && (
					<Panel key='introduction' header='Introduction'>
						{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
						<Collapse css={SmallCollapse} defaultActiveKey={['prompt']} accordion={true}>
							{renderItemContextPanel('Prompt', 'prompt', 'text', panelData.introductionPrompt?.content)}
							{renderContextPanel(panelData.introductionPrompt?.context)}
						</Collapse>
					</Panel>
				)}
			</Collapse>
		</div>
	);
});
