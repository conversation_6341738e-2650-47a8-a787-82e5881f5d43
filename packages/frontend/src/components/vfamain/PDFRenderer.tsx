/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import Button from 'antd/lib/button';
import Spin from 'antd/lib/spin';
import React, { useEffect, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';
import { makeAutoObservable } from 'mobx';
import { observer } from 'mobx-react-lite';
import { LoadingError } from '../common/LoadingError';
import FrontendConfig from '@src/common/FrontendConfig';
import logger from 'pigbot-core/src/logger';

pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`;

export class PDFRendererState {
	pdfUrl: string | null = null;
	pdfError: string | null = null;

	constructor(
		public documentName: string,
		public pageNumber: number,
	) {
		makeAutoObservable(this);
	}

	openLink(documentName: string, pageNumber: number) {
		if (documentName != this.documentName) {
			this.pdfUrl = null;
			this.pdfError = null;
		}
		this.documentName = documentName;
		this.pageNumber = pageNumber;
	}

	nextPage() {
		this.pageNumber++;
	}

	previousPage() {
		this.pageNumber--;
	}

	setPdfUrl(pdfUrl: string) {
		this.pdfUrl = pdfUrl;
		this.pdfError = null;
	}

	setPdfError(error: string) {
		this.pdfUrl = null;
		this.pdfError = error;
	}
}

interface PDFRendererProps {
	jwtToken: string;
	state: PDFRendererState;
	onClose: () => void;
}

export const PDFRenderer: React.FC<PDFRendererProps> = observer((props) => {
	useEffect(() => {
		const fetchPdf = async () => {
			try {
				// 1. Fetch the binary PDF from the backend
				const response = await fetch(`${FrontendConfig.BACKEND_BASE_URL}/pdf-data/${props.state.documentName}`, {
					method: 'GET',
					headers: {
						Authorization: `Bearer ${props.jwtToken}`,
					},
				});
				if (!response.ok) {
					throw new Error(`Failed to fetch PDF: ${response.statusText}`);
				}

				// 2. Convert it to a Blob (binary large object)
				const blob = await response.blob();

				// 3. Create a temporary object URL from the Blob
				const url = URL.createObjectURL(blob);

				// 4. Store that URL in state for use in <Document file={pdfUrl} />
				props.state.setPdfUrl(url);
			} catch (error) {
				logger.error(error);
				props.state.setPdfError('Failed to fetch PDF');
			}
		};

		// Trigger the PDF load
		fetchPdf();

		// Cleanup: revoke object URL when document changes or component unmounts
		return () => {
			if (props.state.pdfUrl) URL.revokeObjectURL(props.state.pdfUrl);
		};
	}, [props.state.documentName]);

	// TODO numPages should be cleared when documentName changes - the best way to do it is to move it to the State
	const [numPages, setNumPages] = useState<number | null>(null);

	const loadingSpin = (
		<div
			css={css`
				display: flex;
				flex: 1;
				justify-content: center;
				align-items: center;
			`}
		>
			<Spin />
		</div>
	);

	const errorMessage = (error: string) => (
		<div
			css={css`
				display: flex;
				flex: 1;
				justify-content: center;
				align-items: center;
			`}
		>
			<LoadingError error={error} />
		</div>
	);

	return (
		<>
			{!props.state.pdfUrl && loadingSpin}

			{props.state.pdfError && errorMessage(props.state.pdfError)}

			{props.state.pdfUrl && (
				<div
					css={css`
						display: flex;
						flex: 1;
						flex-direction: row;
						overflow: hidden;
						align-items: stretch;
						justify-content: center;
						padding: 0 10px;
					`}
				>
					{props.state.pdfUrl && (
						<div
							css={css`
								display: flex;
								align-items: center;
							`}
						>
							<Button disabled={props.state.pageNumber <= 1} icon='left' onClick={() => props.state.previousPage()} />
						</div>
					)}

					<div
						css={css`
							display: flex;
							flex: 1;
							align-items: center;
							justify-content: center;
							overflow: auto;
						`}
					>
						<Document
							file={props.state.pdfUrl}
							loading={loadingSpin}
							onLoadSuccess={(doc) => setNumPages(doc.numPages)}
							onLoadError={(error) => errorMessage(error.message)}
						>
							<Page pageNumber={props.state.pageNumber} />
						</Document>
					</div>

					{props.state.pdfUrl && (
						<div
							css={css`
								display: flex;
								position: relative;
							`}
						>
							<Button
								css={css`
									position: absolute;
									top: 10px;
									z-index: 1;
								`}
								icon='close'
								type='primary'
								onClick={() => props.onClose()}
							/>
							<div
								css={css`
									display: flex;
									flex: 1;
									align-items: center;
								`}
							>
								<Button disabled={!!numPages && props.state.pageNumber >= numPages} icon='right' onClick={() => props.state.nextPage()} />
							</div>
						</div>
					)}
				</div>
			)}
		</>
	);
});
