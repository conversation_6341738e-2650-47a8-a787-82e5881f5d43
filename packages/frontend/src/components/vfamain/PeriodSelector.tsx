/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import Alert from 'antd/lib/alert';
import Button from 'antd/lib/button';
import DatePicker from 'antd/lib/date-picker';
import Select from 'antd/lib/select';
import 'antd/lib/date-picker/style/index.less';
import 'antd/lib/select/style/index.less';
import React, { useState } from 'react';
import { PeriodType, isDatePeriod, defaultPeriodsDynamic, periodLengthString } from 'pigbot-core/src/vfamain/Periods';
import { getPeriodInterval } from './PeriodInterval';
import { useDateFormat } from '@src/components/Providers';

const { MonthPicker } = DatePicker;
const { Option } = Select;

interface PeriodSelectorProps {
	periodLength: number;
	periodType: PeriodType;
	periodSinceDate: moment.Moment;
	periodSinceMonth: moment.Moment;
	onPeriodTypeChange: (periodType: PeriodType) => void;
	onPeriodSinceChange: (periodSince: moment.Moment) => void;
}

export const PeriodSelector: React.FC<PeriodSelectorProps> = (props) => {
	const [changeButtonVisible, setChangeButtonVisible] = useState(true);

	const dateFormat = useDateFormat();

	const periodInterval = getPeriodInterval(
		props.periodType,
		isDatePeriod(props.periodType) ? props.periodSinceDate : props.periodSinceMonth,
		props.periodLength,
	);

	const periodInfo = (
		<div
			css={css`
				margin: 10px 30px;
			`}
		>
			<Alert
				type='info'
				message={
					<>
						{`Virtual farm assistant will analyze farm data since ${periodInterval.from.format(dateFormat)} until ${periodInterval.to.format(dateFormat)} in ${periodLengthString(props.periodLength)} periods. `}
						<Button onClick={() => setChangeButtonVisible(false)} type='dashed' size='small'>
							Change
						</Button>
					</>
				}
			/>
		</div>
	);
	const periodSelect = (
		<div
			css={css`
				display: flex;
				gap: 10px;
				align-items: center;
				justify-content: center;
			`}
		>
			<div
				css={css`
					display: flex;
					flex-direction: column;
					width: 200px;
				`}
			>
				<label>Periods</label>
				{/* @ts-expect-error antd 3x reports incorrect props with React 18 */}
				<Select
					defaultValue={props.periodType}
					onChange={(value) => {
						if (value !== null) {
							props.onPeriodTypeChange(value);
						}
					}}
				>
					{defaultPeriodsDynamic(props.periodLength).map((period) => (
						<Option key={period.key} value={period.key}>
							{period.displayValue}
						</Option>
					))}
				</Select>
			</div>

			<div
				css={css`
					display: flex;
					flex-direction: column;
				`}
			>
				{isDatePeriod(props.periodType) ? (
					<>
						<label>Since</label>
						<DatePicker
							value={props.periodSinceDate}
							onChange={(value) => {
								if (value !== null) {
									props.onPeriodSinceChange(value);
								}
							}}
						/>
					</>
				) : (
					<>
						<label>Since (Month)</label>
						<MonthPicker
							value={props.periodSinceMonth}
							onChange={(value) => {
								if (value !== null) {
									props.onPeriodSinceChange(value);
								}
							}}
						/>
					</>
				)}
			</div>
		</div>
	);

	return changeButtonVisible ? periodInfo : periodSelect;
};
