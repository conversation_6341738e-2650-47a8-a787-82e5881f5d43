import Providers from '@src/components/Providers';
import { SupportChatbot } from '@src/components/supportbot/SupportChatbot';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { DevDateFormat, DevLanguageCode } from '../../../dev/Application';

interface Props {
	jwtToken: string;
	cfPath: string;
	helpUrl?: string;
}

export const ReactComponent = (props: Props) => {
	return (
		<Providers jwtToken={props.jwtToken} languageCode={DevLanguageCode} dateFormat={DevDateFormat}>
			<SupportChatbot isChatbotReady={true} cfPath={props.cfPath} helpUrl={props.helpUrl} />
		</Providers>
	);
};

export function showSupportChatbot(targetElement: HTMLElement, props: Props) {
	createRoot(targetElement).render(<ReactComponent {...props} />);
}
