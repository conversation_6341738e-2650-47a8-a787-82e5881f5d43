import React, { useState } from 'react';
import { hash } from 'ohash';

/**
 * Cuts the text and adds "Show more" button for text that is too long
 * @param text Text to show.
 * @param maxLength Maximum length of the text before it is cut.
 */
export const ExpandableText: React.FC<{ text: string; maxLength: number }> = ({ text, maxLength }) => {
	const [isExpanded, setIsExpanded] = useState(false);

	if (text.length <= maxLength) {
		return <>{text}</>;
	}

	return (
		<>
			{(isExpanded ? text : text.substring(0, maxLength) + '...') + ' '}
			<a
				href={`show-more-${hash(text)}`}
				onClick={(e) => {
					e.preventDefault();
					setIsExpanded(!isExpanded);
				}}
			>
				{isExpanded ? ' Show less' : ' Show more'}
			</a>
		</>
	);
};
