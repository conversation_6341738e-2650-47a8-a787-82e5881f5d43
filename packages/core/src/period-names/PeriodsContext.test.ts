import { getPeriodNamings } from "./PeriodNamings";

describe("getPeriodNamingsPrompt", () => {
	test("should return prompt with period names", async () => {
		const { getPeriodNamingsPrompt } = await import("./PeriodsContext");

		const periods = [
			{ from: "2024-01-01", to: "2024-01-31" },
			{ from: "2024-02-01", to: "2024-02-29" },
		];

		const periodNamings = getPeriodNamings(periods, "en", "standard");
		const prompt = getPeriodNamingsPrompt(periodNamings);
		expect(prompt).toMatchSnapshot();
	});
});
