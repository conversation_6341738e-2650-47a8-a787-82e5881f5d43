import { EfficiencyReportData, Kpi, Section } from "../EfficiencyReportData";
import breedingKPIs from "../utils/breedingKPIs.json";
import Handlebars from "handlebars";
import fs from "fs";
import path from "path";
import { renderTemplateFile } from "../utils/renderTemplateFile";
import { registerUniversalHelpers } from "../utils/handlebarsUtils";
import { PeriodsContext } from "../period-names/PeriodsContext";

type KPIData = {
	Code: string;
	Description: string;
};

// KPI codes are normally not needed and only confuse the LLM, so they are hidden by default
export function convertEffReportToText(data: EfficiencyReportData, periodsContext: PeriodsContext, showKpiCodes: boolean = false) {
	const additionalKPIData: KPIData[] = breedingKPIs;
	const descriptionMap = Object.fromEntries(additionalKPIData.map((d) => [d.Code, d.Description]));

	const kpiTemplate = fs.readFileSync(path.resolve(__dirname, "./kpiToText.hbs"), "utf-8");
	Handlebars.registerPartial("kpiText", kpiTemplate);

	registerUniversalHelpers();

	const newSections: Section[] = data.sections.map((section) => {
		const newKpis: Kpi[] = section.kpis.map((kpi) => {
			return { ...kpi, description: descriptionMap[kpi.code] }; //adding descriptions to kpis
		});
		return { ...section, kpis: newKpis };
	});
	const templateData = {
		...data,
		...periodsContext,
		periods: data.periods.slice(0, -1),
		sections: newSections,
		showKpiCodes,
	};
	return renderTemplateFile(path.resolve(__dirname, "./efficiencyReport.hbs"), templateData);
}

export function kpiToText(kpis: Kpi[], periodCodes: { [key: number]: string }, showKpiCodes: boolean = false) {
	return renderTemplateFile(path.resolve(__dirname, "./kpiToText.hbs"), { kpis, periodCodes, showKpiCodes });
}
