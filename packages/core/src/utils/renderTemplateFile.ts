import * as fs from "fs";
import Handlebars from "handlebars";
import { PigbotHandlebarsOptions, registerUniversalHelpers } from "./handlebarsUtils";

interface TemplateCache {
	[key: string]: Handlebars.TemplateDelegate;
}

const templateCache: TemplateCache = {};

// Make sure to register all universal handlebars helpers before using them
registerUniversalHelpers();

/**
 * Render template from a file
 */
export function renderTemplateFile(hbsFile: string, context: unknown = {}): string {
	// Don't cache templates in development
	if (!templateCache[hbsFile] || process.env.NODE_ENV === "development") {
		const templateContent = fs.readFileSync(hbsFile, "utf-8");
		templateCache[hbsFile] = Handlebars.compile(templateContent, PigbotHandlebarsOptions);
	}

	const template = templateCache[hbsFile];
	try {
		return template(context);
	} catch (e) {
		throw Object.assign(new Error(`Failed to render template ${hbsFile} with context ${JSON.stringify(context, null, 2)}`), {
			cause: e,
		});
	}
}
