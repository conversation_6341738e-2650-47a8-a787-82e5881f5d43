import "../../../test/jest.setup";
import { TestData } from "../../../test/TestData";
import { match } from "ts-pattern";
import { DefaultTestTimeout, mockTemporal } from "../../../test/jest.setup";
import { expect } from "@jest/globals";

test(
	"getEfficiencyReportPDF",
	async () => {
		mockTemporal();
		const efficiencyReportService = await import(".././EfficiencyReportService");
		const ereps = await import("./EfficiencyReportExportPdfService");

		const stream = efficiencyReportService.getEfficiencyReportSummaryInternal({
			request: TestData.request,
			templateOverrides: {},
			farmHoldingId: TestData.testUserContext,
		});

		// PDF is downloaded using the responseId
		let responseId: string | undefined;
		for await (const chunk of stream) {
			match(chunk)
				.with({ type: "responseId" }, (v) => (responseId = v.responseId))
				.otherwise(() => {});
		}

		const fullResponse = await ereps.getEfficiencyReportPDF({ responseId: responseId!, hiddenSections: new Set() });

		expect(fullResponse.pdf.length).toBeGreaterThan(1000000);

		const customizedShorterResponse = await ereps.getEfficiencyReportPDF({ responseId: responseId!, hiddenSections: new Set(["issue-0"]) });

		expect(customizedShorterResponse.pdf.length).toBeGreaterThan(1000000);

		// PDF with hidden sections should be smaller than the full response
		expect(customizedShorterResponse.pdf.length).toBeLessThan(fullResponse.pdf.length);
	},
	DefaultTestTimeout,
);
