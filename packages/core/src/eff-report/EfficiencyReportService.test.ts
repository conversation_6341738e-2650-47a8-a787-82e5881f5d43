import "../../test/jest.setup";
import { match } from "ts-pattern";
import { TestData } from "../../test/TestData";
import type { EffReportInternalChunk } from "./EfficiencyReportService";
import { DefaultTestTimeout, mockTemporal } from "../../test/jest.setup";

test(
	"getEfficiencyReportSummaryInternal",
	async () => {
		const startOrAwaitWorkflow = mockTemporal();

		const erp = await import("./EfficiencyReportService");

		const response = erp.getEfficiencyReportSummaryInternal({
			request: TestData.request,
			templateOverrides: {},
			farmHoldingId: TestData.testUserContext,
		});

		const result: EffReportInternalChunk[] = [];
		for await (const chunk of response) {
			result.push(chunk);
		}

		expect(startOrAwaitWorkflow).toHaveBeenCalled();

		expect(
			result.find((v) =>
				match(v)
					.with({ type: "getRelevantData" }, () => true)
					.otherwise(() => false),
			),
		).toBeDefined();

		expect(
			result.find((v) =>
				match(v)
					.with({ type: "identifyIssuesResponse" }, () => true)
					.otherwise(() => false),
			),
		).toBeDefined();

		expect(result.filter((v) => typeof v === "string").length).toBeGreaterThan(0);
	},
	DefaultTestTimeout,
);
