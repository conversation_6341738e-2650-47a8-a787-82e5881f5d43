import { convertEffReportToText } from "../data-to-text/DataToText";
import { IdentifyIssuesResponse, IdentifyIssuesResponseSchema } from "./IdentifyIssuesResponseSchema";
import { EfficiencyReportData } from "../EfficiencyReportData";
import { enhanceSchemaWithDescriptions, RenderedPromptTemplate, renderPromptTemplate } from "../prompt-template/PromptTemplate";
import { getSources } from "../source-docs/SourceDoc";
import { HoldingId, SystemHoldingId } from "../HoldingId";
import { PromptCustomization } from "../PromptCustomization";
import { EffReportPrompts } from "./EffReportPrompts";
import { SimpleChatMessage } from "./SimpleChatMessage";
import { getLanguageLevelsContext } from "../language-level/getLanguageLevelsContext";
import { emptyUsage, structuredCompletion, sumUsages, textCompletion, Usage } from "../llm/LlmCommon";
import { PeriodsContext } from "../period-names/PeriodsContext";

export const ISSUE_IDENTIFICATION_STEP = "issue-identification-step";

export type IdentifyIssuesInternalResponse = {
	messages: SimpleChatMessage[];
	response: IdentifyIssuesResponse;
	usage: Usage;
};

/**
 * Identify issues and find problematic KPIs in an eff-report
 */
export async function identifyIssues({
	report,
	holdingId,
	introductionResponse,
	promptCustomization,
	periodsContext,
}: {
	report: EfficiencyReportData;
	holdingId: HoldingId;
	introductionResponse: RenderedPromptTemplate;
	promptCustomization: PromptCustomization;
	periodsContext: PeriodsContext;
}): Promise<IdentifyIssuesInternalResponse> {
	const sources = await getSources({ userLanguage: report.language, holdingId, relevantFor: [ISSUE_IDENTIFICATION_STEP] });

	// Load optional holding specific instructions
	const holdingInstructions = (
		await renderPromptTemplate({
			promptId: {
				promptType: EffReportPrompts.issueIdentification_holdingInstructions,
				holdingId,
				templateOverrides: promptCustomization.templateOverrides,
			},
			optional: true,
		})
	)?.template.template;

	const languageLevelsContext = await getLanguageLevelsContext(promptCustomization.templateOverrides);

	const responseSchema = await enhanceSchemaWithDescriptions(
		IdentifyIssuesResponseSchema,
		EffReportPrompts.issueIdentification_2_top_issues,
		promptCustomization.templateOverrides,
		SystemHoldingId,
		languageLevelsContext,
	);

	// Context is shared by all prompts
	const context = {
		...introductionResponse.context,
		// KPI codes are needed here, because they are expected in the response
		reportText: convertEffReportToText(report, periodsContext, true),
		...sources,
		holdingInstructions,
		IdentifiedIssueDescription: responseSchema.shape.issues.element.shape.description.description,
		...periodsContext,
		...languageLevelsContext,
	};

	const messages: SimpleChatMessage[] = [
		{
			role: "system",
			content: introductionResponse.content,
		},
		{
			role: "user",
			content: (
				await renderPromptTemplate({
					promptId: {
						promptType: EffReportPrompts.issueIdentification_1_issue_analysis,
						holdingId: SystemHoldingId,
						templateOverrides: promptCustomization.templateOverrides,
					},
					context,
				})
			).content,
		},
	];

	let usageSummary: Usage = emptyUsage({ activity: "eff-report", action: "identify-issues" });

	const { response: identification, usage: usage1 } = await textCompletion({
		messages,
		cacheIdx: promptCustomization.cacheIdx,
		llm: "gemini-pro",
		metricData: usageSummary.metricData,
	});

	usageSummary = usage1 ? sumUsages(usageSummary, usage1) : usageSummary;

	messages.push(
		{
			role: "assistant",
			content: identification,
		},
		{
			role: "user",
			content: (
				await renderPromptTemplate({
					promptId: {
						promptType: EffReportPrompts.issueIdentification_2_top_issues,
						holdingId: SystemHoldingId,
						templateOverrides: promptCustomization.templateOverrides,
					},
					context,
				})
			).content,
		},
	);

	const { response, usage: usage2 } = await structuredCompletion({
		responseSchema,
		messages: messages,
		metricData: usageSummary.metricData,
		cacheIdx: promptCustomization.cacheIdx,
	});

	usageSummary = usage2 ? sumUsages(usageSummary, usage2) : usageSummary;

	return {
		messages,
		response,
		usage: usageSummary,
	};
}
