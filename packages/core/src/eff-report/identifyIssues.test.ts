import "pigbot-core/test/jest.setup";
import { efficiencyReportData } from "../../test/EfficiencyReportData";
import { TestData } from "../../test/TestData";
import { expect } from "@jest/globals";

import { DefaultTestTimeout } from "../../test/jest.setup";

test(
	"identifyIssues",
	async () => {
		const module = await import("./identifyIssues");
		const { getPeriodsContext } = await import("../period-names/PeriodsContext");

		const SourceDoc = await import("../source-docs/SourceDoc");
		jest.spyOn(SourceDoc, "getSources").mockReturnValue(
			Promise.resolve({
				segesManual: "Seges Ipsum",
				SOPs: "SOPs Ipsum",
				southWestVetsSOPs: "SWV SOPs Ipsum",
			}),
		);

		// Most we can test is that this doesn't throw an error
		const response = await module.identifyIssues({
			report: efficiencyReportData,
			holdingId: TestData.testUserContext,
			introductionResponse: {
				context: {
					country: "US",
					language: "en",
				},
				content: "You are a virtual pig farm assistant. The farm is located in USA. Respond in following language: en.",
				template: {
					template: "IGNORED",
					id: "IGNORED",
				},
			},
			promptCustomization: {
				templateOverrides: {},
				cacheIdx: 0,
			},
			periodsContext: getPeriodsContext(efficiencyReportData.periods.slice(0, -1), "en", "standard"),
		});

		const inputContent = response.messages.map((m) => m.content).join("\n");
		expect(inputContent).toContain("Seges Ipsum");
		expect(inputContent).toContain("SOPs Ipsum");
		expect(inputContent).toContain("SWV SOPs Ipsum");
	},
	DefaultTestTimeout,
);
