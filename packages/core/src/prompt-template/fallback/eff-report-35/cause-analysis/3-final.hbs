Rewrite the analysis using the same instructions but incorporate the feedback from the review.
Respond in the following language: {{language}}.
The response should use terminology understandable to pig farmer from {{country}}.

Split your answer into the following sections:
- Analysis - Detailed analysis. Structure and format this section for high readability. Incorporate graphs and tables directly alongside the relevant text to increase clarity. Instead of writing values of data points in text, use graphs. {{WriteInExpertLanguageLevel}}
- Relevant data - Show input data that were crucial for analysis of the issue in table format. It's ok to use multiple tables. Include goal for the latest period and then 4 periods starting with the latest one if available for the type of data.
- Identified cause - Write a summary of the analysis with a focus on identified cause as a single unstructured paragraph of text. If the exact cause can't be confirmed by the data, suggest what needs to be done to find the actual cause, such as having a veterinary visit the farm. Highlight important information in bold to increase readability. Don't suggest actions to resolve the issue. Keep this section short. {{WriteInStandardLanguageLevel}}
- One sentence summary - Write a one sentence summary of the identified cause section. Write this section without any additional comments. Just a single sentence without highlighted text. This sentence will be added to a document after a sentence that summarizes the issue. Make sure the sentences will flow nicely together and there is no unnecessary repetition. Don't include the issue summary here. Issue summary: {{issue.summary}}

Separate the sections by horizontal lines. Start each section with a level 1 heading. Don't add any other comments before or after these four sections.

Refer to KPIs using their names, not codes.
If you mention SOP, {{SOPLinkFormatPrompt}}

## Graph Generation Instructions

To generate a graph, use a markdown code block with the specific `graph_type` and a JSON object matching the schema for that graph.

**Format:**
```graph_type
{
  "key": "value",
  // ... more data according to the schema
}
```

---
### Available Graph Types

#### 🥧 Pie Chart
* **Purpose**: Use for showing proportions or percentage breakdowns of a whole.
* **`graph_type`**: `piechart`
* **Schema**:
{{pieChartSchema}}

#### 📈 Multi-Line Trend Graph
* **Purpose**: Use show data series or to compare multiple data series over at least four time periods. Optional goal lines can be included.
* **`graph_type`**: `vfastackedlinegraph`
* **Schema**:
{{stackedLineGraphSchema}}

####📊 Stacked Bar Chart
* **Purpose**: Use to show how a total amount is divided into parts across different categories or time periods.
* **`graph_type`**: `stackedbarchart`
* **Schema**:
{{stackedBarChartSchema}}

---

### ⚠️ Important Rules for Multi-Series Graphs (`vfastackedlinegraph`)

Follow these rules **before** generating a single graph with multiple data series:

1.  **Check for Scale Disparity**: Compare the approximate maximum values of all data series intended for the same graph.
2.  **Decision Rule**: **If one series' maximum value is roughly 3 times (or more) greater than another series' maximum value, you MUST generate separate graphs.**
3.  **Implementation**:
    * Create one graph for the series with larger values.
    * Create a *separate* graph for the series with smaller values.
    * Ensure each graph uses an appropriate Y-axis scale for the data it displays.
4.  **Reason**: Displaying series with vastly different scales on one graph makes it impossible to see variations in the smaller-valued series. Separate graphs ensure all data is clearly visible.
5.  **Check for Different Units**: **If data series intended for the same graph use different units (e.g., 'USD' vs. '%', 'kg' vs. 'count'), they MUST always be plotted on separate graphs.** This applies even if their scales are similar.