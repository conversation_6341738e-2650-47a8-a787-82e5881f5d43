{"name": "pigbot-core", "version": "1.0.0", "scripts": {"build": "tsc", "test": "jest --force<PERSON>xit", "getreport": "node-dev src/dev/runGetReport.ts"}, "dependencies": {"@google/genai": "^0.8.0", "@temporalio/client": "^1.11.7", "@temporalio/common": "^1.11.7", "@temporalio/proto": "^1.11.7", "@temporalio/worker": "^1.11.7", "@temporalio/workflow": "^1.11.7", "@tirke/node-cache-manager-ioredis": "^3.6.0", "cache-manager": "^5.7.6", "convict": "^6.2.4", "convict-format-with-luxon": "^1.0.1", "dotenv": "^16.4.5", "echarts": "^5.6.0", "fp-ts": "^2.16.9", "fs-extra": "^11.2.0", "gemini-zod": "^0.1.2", "handlebars": "^4.7.8", "image-size": "^1.1.1", "ioredis": "^5.4.1", "jsonrepair": "^3.12.0", "luxon": "^3.5.0", "marked": "^15.0.7", "mime-types": "^2.1.35", "ms": "^2.1.3", "ohash": "^1.1.6", "openai": "^4.85.1", "postgres": "^3.4.4", "puppeteer": "^24.2.0", "rate-limiter-flexible": "^5.0.3", "react-markdown": "^9.0.1", "rxjs": "^7.8.1", "tiktoken": "^1.0.16", "ts-pattern": "^5.4.0", "winston": "^3.17.0", "winston-transport-browserconsole": "^1.0.5", "zod": "^3.23.8"}, "devDependencies": {"@jest/globals": "^29.7.0", "@testcontainers/postgresql": "^10.13.1", "@testcontainers/redis": "^10.13.1", "@types/convict": "^6.1.6", "@types/fs-extra": "^11.0.4", "@types/luxon": "^3.4.2", "@types/mime-types": "^2.1.0", "@types/ms": "^0.7.34", "@types/node": "^20.16.7", "jest": "^29.7.0", "node-dev": "^8.0.0", "testcontainers": "^10.13.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2"}}