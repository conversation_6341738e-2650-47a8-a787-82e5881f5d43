package com.cloudfarms.externalapi.farm.deltas

private[deltas] case class TableFetch(name: String, sqlDeleted: String, sqlUpserted: String)

private[deltas] object TableFetch {

  // Important note for further development of this export data model.
  // This export data model is used by Cloudfarms clients to fetch data changes from the Cloudfarms database.
  // The evolutions of the internal Cloudfarms data model can impact this export data model, but should minimize
  // the impact on the clients. That is why the export data model is separated from the internal data model and
  // the internal data model is not exposed to the clients.
  //
  // When changing this export data model, the clients should be informed about the changes.
  // Following changes can be made to the export data model without feedback from the clients:
  //
  // - Adding new tables (ideally at the end of the list).
  // - Adding new columns to existing tables. They have to be added at the end of the column list.
  // - Fixing bugs in the export queries that do not change the result set format.
  // - Changing the types of the columns in the result set, if the new type is compatible with the old type.
  //   (e.g. changing the lenght of a string to a shorter length)
  //
  // When a column in the internal data model is removed, the corresponding column in the export data model should be
  // marked as deprecated and the customers should be informed about the change and until the removal in the export data
  // model is approved by the customer, the column should be kept in the export data model and its value should be null.
  //
  // All other changes can break the clients, so the customers have to be informed about the changes, and they have to
  // approve them.

  def holdingTables(rootId: Long): List[TableFetch] = List(
    TableFetch(
      name = "death_type",
      sqlDeleted = s"select $rootId as root_id, code FROM deathtype_all_deleted WHERE update_date > ?",
      sqlUpserted = s"select $rootId as root_id, code, description, externalcode, domestic_slaughter, domestic_disposal, stolen from deathtype_all where update_date > ? order by code",
    ),
    TableFetch(
      name = "illness_type",
      sqlDeleted = s"select $rootId as root_id, code FROM illnesstype_all_deleted WHERE update_date > ?",
      sqlUpserted = s"select $rootId as root_id, code, description from illnesstype_all where update_date > ? order by code",
    ),
    TableFetch(
      name = "account",
      sqlDeleted = s"select 0 as id where false and null > ?", // Accounts cannot be deleted
      sqlUpserted = s"select distinct a.id, a.email :: varchar(50), a.abbr, a.name from account a join persona p on a.id = p.account_id join organization o on o.id = p.organization_id where o.root_id = $rootId and a.update_date > ? order by a.id",
    ),
    TableFetch(
      name = "feed_type",
      sqlDeleted = s"select $rootId as root_id, feed_name FROM feed_type_deleted WHERE update_date > ?",
      sqlUpserted = s"select $rootId as root_id, feed_name, valid_from, valid_to, min_weight, max_weight, piglet, weaner, fattener, sow, gilt, boar, piglet_mix_ratio, weaner_mix_ratio, finisher_mix_ratio, sow_mix_ratio, gilt_mix_ratio, boar_mix_ratio from feed_type where update_date > ? order by feed_name",
    ),
    TableFetch(
      name = "body_condition_score",
      sqlDeleted = s"select $rootId as root_id, code FROM body_condition_score_all_deleted WHERE update_date > ?",
      sqlUpserted =
        s"select $rootId as root_id, code, description, score, color from body_condition_score_all where update_date > ? order by code",
    ),
  )

  def farmTables(farmId: Long): List[TableFetch] = List(
    TableFetch(
      name = "sow",
      sqlDeleted = s"select $farmId as farm_id, id FROM sow_deleted WHERE update_date > ?",
      sqlUpserted = s"""select $farmId as farm_id
                      |     , id
                      |     , animalid
                      |     , sownumber
                      |     , birthdate
                      |     , breed
                      |     , farmnumber
                      |     , eartagnumber
                      |     , smallnumber
                      |     , comment
                      |     , pig_centralrn
                      |     , tobekilled to_be_killed
                      |     , tobecured  to_be_cured
                      |     , hasissue   has_issue
                      |     , badnurse   bad_nurse
                      |     , flag1
                      |     , flag2
                      |     , flag3
                      |     , rfidlf
                      |     , rfiduhf
                      |     , nationalid national_id
                      |     , nationalid_marking_date
                      |     , flex1
                      |     , flex2
                      |     , flex3
                      |     , flex4
                      |     , flex5
                      |     , location_id
                      |     , created_location_id
                      |     , case
                      |         when state = 0 then 'onFarm'
                      |         when state = 1 then 'alive'
                      |         when state = 2 then 'empty'
                      |         when state = 3 then 'served'
                      |         when state = 4 then 'farrowing'
                      |         when state = 5 then 'lactating'
                      |         when state = 6 then 'nursery'
                      |         when state = 7 then 'dead'
                      |         when state = 8 then 'inactive'
                      |       end::varchar(15) as state
                      |from sow
                      |where update_date > ?
                      |order by id  """.stripMargin,
    ),
    TableFetch(
      name = "location",
      sqlDeleted = s"select $farmId as farm_id, id FROM location_deleted WHERE update_date > ?",
      sqlUpserted = {
        s"""select $farmId as farm_id, id, inhabitanttype_code, locationtype_id, locationnumber, name, aream2, capacity, comment, parent_id, validfrom, validto, forbidserving, forbidfarrowing, forbidweaning, growthrate_id, country_id, street1, street2, postalnumber, city, region, farm_centralrn, pig_centralrn, turnarounddays, commonexitweight, fornamed, fortest, commonentryweight, externalname, feedsystemname, danishcrown_firmakode, progeny_type_code, addressname, smallpigs, batch_target_date, cost_type_code, sikava_holding_place
          |from location
          |where update_date > ?
          |order by id  """.stripMargin
      },
    ),
    TableFetch(
      name = "batch_location",
      sqlDeleted = s"select $farmId as farm_id, id FROM batch_location_deleted WHERE update_date > ?",
      sqlUpserted =
        s"select $farmId as farm_id, id, batch_id, location_id, entry_date from batch_location where update_date > ? order by id",
    ),
    TableFetch(
      name = "cycle",
      sqlDeleted = s"select $farmId as farm_id, id FROM serving_deleted WHERE update_date > ?",
      sqlUpserted =
        s"select $farmId as farm_id, id, sow_id, litter, actor_date cycle_start_date, farrow_location_id from serving where update_date > ? order by id",
    ),
    TableFetch(
      name = "mating",
      sqlDeleted = s"select $farmId as farm_id, id FROM servingevent_deleted WHERE update_date > ?",
      sqlUpserted = {
        s"""select $farmId as farm_id, id, serving_id as cycle_id, semenbatch_id, actor_date as mating_date, location_id, actor_id as inseminator_id, comment
          |from servingevent
          |where update_date > ? order by id""".stripMargin
      },
    ),
    TableFetch(
      name = "farrowing",
      sqlDeleted = s"select $farmId as farm_id, id FROM farrowevent_deleted WHERE update_date > ?",
      sqlUpserted = {
        s"""select $farmId as farm_id, id, serving_id as cycle_id, actor_date farrowing_date, comment, liveborn, stillborn, weakborn, mummificated as mummified,
          |femalepiglets as females_for_breeding, state = 2 as finished
          |from farrowevent
          |where update_date > ? order by id""".stripMargin
      },
    ),
    TableFetch(
      name = "weaning",
      sqlDeleted = s"select $farmId as farm_id, id FROM weaned_deleted WHERE update_date > ?",
      sqlUpserted = {
        s"""select $farmId as farm_id, id, serving_id as cycle_id, actor_date weaning_date, nursery, amount, weight, age, location_id as weaning_location_id, sow_location_id, comment
          |from weaned
          |where update_date > ? order by id""".stripMargin
      },
    ),
    TableFetch(
      name = "pregnancy_scan",
      sqlDeleted = s"select $farmId as farm_id, id FROM sowscan_deleted WHERE update_date > ?",
      sqlUpserted = {
        s"""select $farmId as farm_id, id, serving_id as cycle_id, actor_date pregnancy_scan_date, location_id, pregnant, illnesstype_code, comments
          |from sowscan
          |where update_date > ? order by id""".stripMargin
      },
    ),
    TableFetch(
      name = "semenbatch",
      sqlDeleted = s"select $farmId as farm_id, id FROM semenbatch_deleted WHERE update_date > ?",
      sqlUpserted = {
        s"""select $farmId as farm_id, id, semenbatchnumber, actor_date usable_from, usable_until, comment, breed, animalid, external_name
          |from semenbatch
          |where update_date > ? order by id""".stripMargin
      },
    ),
    TableFetch(
      name = "semen",
      sqlDeleted = s"select $farmId as farm_id, id FROM semen_deleted WHERE update_date > ?",
      sqlUpserted = {
        s"""select $farmId as farm_id, id, semenbatch_id, actor_date semen_date, boar_id, animalid, breed, comment,
          |colorright, smellright, spermmoving, motilitypercent, motilitypercent1, motilitypercent2, motilitypercent3
          |from semen
          |where update_date > ? order by id""".stripMargin
      },
    ),
    TableFetch(
      name = "young_animal",
      sqlDeleted = s"select $farmId as farm_id, id FROM gilt_deleted WHERE update_date > ?",
      sqlUpserted = s"""
                      |select $farmId as farm_id, id, animalid, sownumber animalnumber, birthdate, breed, farmnumber, eartagnumber,
                      |  smallnumber, comment, pig_centralrn, quarantine,
                      |  (case when sex = 'female' then 'F' when sex = 'male' then 'M' else null end) as sex,
                      |  active, sow_id, boar_id, rfidlf, rfiduhf, heatdate, secondheat,
                      |  testeduntil, testresult, externalname, teststart, teststartweight, observation, forslaughter,
                      |  testteam_id, flex1, flex2, flex3, flex4, flex5, castrated,
                      |  location_id, created_location_id, giltdate as created_on,
                      |  serving_id as born_in_cycle_id
                      |from gilt
                      |where update_date > ?
                      |order by id""".stripMargin,
    ),
    TableFetch(
      name = "breed",
      sqlDeleted = s"select $farmId as farm_id, animalid FROM breed_deleted WHERE update_date > ?",
      sqlUpserted = s"""select $farmId as farm_id,
                      |  animalid,
                      |  farmnumber,
                      |  birthdate,
                      |  breed,
                      |  (case when sex = 'female'
                      |    then 'F'
                      |   when sex = 'male'
                      |     then 'M'
                      |   else null end) :: char(1) sex,
                      |  teats,
                      |  teatsleft                  teats_left,
                      |  teatsright                 teats_right,
                      |  index    as                breeding_index,
                      |  damid                      dam_id,
                      |  damnumber                  dam_number,
                      |  dambreed                   dam_breed,
                      |  dam_line,
                      |  sireid                     sire_id,
                      |  sirenumber                 sire_number,
                      |  sirebreed                  sire_breed,
                      |  sire_line,
                      |  male_index,
                      |  female_index,
                      |  daily_gain_small,
                      |  daily_gain_large,
                      |  feed_units,
                      |  lean_meat_percentage,
                      |  litter_size,
                      |  sustainability,
                      |  slaughter_loss,
                      |  strength,
                      |  index_type,
                      |  entrydate                  entry_date,
                      |  exitdate                   exit_date,
                      |  exittype                   exit_type,
                      |  exitcode                   exit_code,
                      |  last_index_update_date,
                      |  last_index_download_date :: timestamp(0) with time zone,
                      |  f4date                     f4_date,
                      |  f4state                    f4_state,
                      |  usecode,
                      |  comment
                      |from breed where update_date > ?
                      |order by animalid""".stripMargin,
    ),
    TableFetch(
      name = "boar",
      sqlDeleted = s"select $farmId as farm_id, id FROM boar_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""
          |select $farmId as farm_id, id, animalid, boarnumber, birthdate, breed, farmnumber, eartagnumber, smallnumber, comment, pig_centralrn, rfidlf, rfiduhf, nationalid national_id, nationalid_marking_date, flex1, flex2, flex3, flex4, flex5,
          |location_id, created_location_id, boardate as created_on
          |from boar where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "externalboar",
      sqlDeleted = s"select $farmId as farm_id, id FROM externalboar_deleted WHERE update_date > ? order by id",
      sqlUpserted = s"select $farmId as farm_id, id, animalid, nationalid, boarnumber from externalboar where update_date > ? order by id",
    ),
    TableFetch(
      name = "business",
      sqlDeleted = s"select $farmId as farm_id, id FROM business_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""select
          |$farmId as farm_id,
          |id,
          |country_id,
          |code company_code,
          |companyname company_name,
          |street1,
          |street2,
          |postalnumber,
          |city,
          |region,
          |type,
          |hq,
          |active,
          |linkedinurl,
          |website,
          |comment,
          |pig_centralrn,
          |farm_centralrn,
          |danavl_dont_send_exit,
          |category
          |from business
          |where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "buying",
      sqlDeleted = s"select $farmId as farm_id, id FROM transferin_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""select $farmId as farm_id,
          |id,
          |business_id,
          |transfername,
          |transferweightempty,
          |transferweightfull,
          |comment,
          |actor_id,
          |actor_date as purchase_date,
          |traces_certificate,
          |health_certificate,
          |lorry_country_id,
          |lorry_platenumber,
          |trailer_country_id,
          |trailer_platenumber
          |from transferin
          |where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "buying_group",
      sqlDeleted = s"select $farmId as farm_id, id FROM pigqualityin_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""select $farmId as farm_id, id,
          |transferin_id buying_id,
          |pigqualitytype_code,
          |pigweight,
          |pigamount,
          |pigliveweight,
          |price,
          |leanpercent,
          |comment,
          |deadweight,
          |location_id,
          |pig_centralrn,
          |age
          |from pigqualityin where update_date < ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "buying_individual",
      sqlDeleted = s"select $farmId as farm_id, id FROM transferindividualin_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""
          |select $farmId as farm_id, id, transferin_id buying_id, sow_id, boar_id, gilt_id young_animal_id, pigweight, pigliveweight,
          |price, leanpercent, comment, dead, location_id
          |from transferindividualin where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "selling",
      sqlDeleted = s"select $farmId as farm_id, id FROM transferout_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""select $farmId as farm_id,
          |id,
          |business_id,
          |destination_id,
          |not(livesell) for_slaughter,
          |transfername as name,
          |comment,
          |actor_date selling_date,
          |shipping_date planned_selling_date,
          |traces_certificate,
          |health_certificate,
          |lorry_country_id,
          |lorry_platenumber lorry_plate_number,
          |trailer_country_id,
          |trailer_platenumber trailer_plate_number,
          |driver,
          |lorry_brand,
          |shippingcomment shipping_comment,
          |transporter_id,
          |receiver_id
          |from transferout where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "local_transfer",
      sqlDeleted = s"select $farmId as farm_id, id FROM transferloc_deleted WHERE update_date > ? order by id",
      sqlUpserted =
        s"select $farmId as farm_id, id, comment, actor_date as transfer_date from transferloc where update_date > ? order by id",
    ),
    TableFetch(
      name = "local_transfer_group",
      sqlDeleted = s"select $farmId as farm_id, id FROM pigqualityloc_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""
          |select  $farmId as farm_id, id, transferloc_id local_transfer_id, pigqualitytype_code, pigweight, pigamount,
          |pigliveweight, price, leanpercent, comment, deadamount, deadweight, fromlocation_id from_location_id,
          |tolocation_id to_location_id,
          |pig_centralrn, age
          |from pigqualityloc where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "local_transfer_individual",
      sqlDeleted = s"select $farmId as farm_id, id FROM hop_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""select $farmId as farm_id, h.id
          |, h.sow_id
          |, h.boar_id
          |, h.gilt_id as young_animal_id
          |, h.actor_date as transfer_date
          |, h.from_location_id
          |, h.to_location_id
          |, h.weight
          |, h.illnesstype_code
          |, b.comment
          |, b.layer1
          |, b.layer2
          |, b.layer3
          |, b.bodycondition
          |, b.feedcurve
          |, b.strength1
          |, b.strength2
          |, b.strength3
          |, b.strength4
          |, b.backfat1
          |, b.backfat2
          |, b.backfat3
          |, b.backfat4
          |, b.teatsleft
          |, b.teatsright
          |, b.teatsdefective
          |, b.bodylength
          |, b.layer4
          |, b.heat_no_service
          |, b.heat_no_service_reason_code
          |, b.body_condition_score_code
          |from hop h left join backfat b using (id) where h.update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "selling_group",
      sqlDeleted = s"select $farmId as farm_id, id FROM pigqualityout_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""select $farmId as farm_id,
          |id,
          |transferout_id selling_id,
          |pigqualitytype_code,
          |pigamount,
          |pigweight,
          |pigliveweight,
          |price,
          |leanpercent,
          |comment,
          |deadamount,
          |deadweight,
          |location_id,
          |pig_centralrn,
          |transfergroup,
          |slaughterindex,
          |age
          |from pigqualityout where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "selling_individual",
      sqlDeleted = s"select $farmId as farm_id, id FROM transferindividualout_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""select $farmId as farm_id,
          |id,
          |transferout_id selling_id,
          |sow_id,
          |boar_id,
          |gilt_id as young_animal_id,
          |pigqualitytype_code,
          |pigweight,
          |pigliveweight,
          |price,
          |leanpercent,
          |comment,
          |dead,
          |location_id,
          |illnesstype_code,
          |transfergroup,
          |slaughterindex,
          |selection_date
          |from transferindividualout where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "death",
      sqlDeleted = s"select $farmId as farm_id, id FROM dead_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""select $farmId as farm_id, id, sow_id, boar_id, serving_id as cycle_id, gilt_id as young_animal_id, illnesstype_code, deathtype_code,
          |comment, actor_date as death_date, location_id, weight, pig_centralrn, amount, age, price, piglet_state
          |from dead where update_date > ? order by id""".stripMargin
      },
    ),
    TableFetch(
      name = "feed_consumption",
      sqlDeleted = s"select $farmId as farm_id, id FROM feedconsumption_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""select $farmId as farm_id, id,
          |location_id,
          |consumption,
          |feedunit,
          |measuredate,
          |price,
          |rawprotein_percent,
          |g_calcium calcium,
          |g_phosfor_kg phosphorus,
          |g_kalium_kg kalium,
          |comment,
          |actor_date as feed_date,
          |inhabitanttype_code,
          |fromdate,
          |drymatter,
          |feedname,
          |case when type = 0 then 'consumption'
          |     when type = 1 then 'delivery'
          |     when type = 2 then 'inventory'
          |end as record_category
          |from feedconsumption where update_date > ? order by id""".stripMargin
      },
    ),
    TableFetch(
      name = "stocktaking_day",
      sqlDeleted = s"select $farmId as farm_id, id FROM stocktakingday_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""select $farmId as farm_id, id, effectivedate as stocktaking_date
          |from stocktakingday where update_date > ? order by id""".stripMargin
      },
    ),
    TableFetch(
      name = "stocktaking",
      sqlDeleted = s"select $farmId as farm_id, id FROM stocktaking_deleted WHERE update_date > ? order by id",
      sqlUpserted = {
        s"""select $farmId as farm_id, id, stocktakingday_id as stocktaking_day_id, location_id, inhabitanttype_code, amount, weight
          |from stocktaking where update_date > ? order by id""".stripMargin
      },
    ),
    TableFetch(
      name = "empty_location",
      sqlDeleted = s"select $farmId as farm_id, id FROM empty_location_deleted WHERE update_date > ? order by id",
      sqlUpserted = s"select $farmId as farm_id, id, actor_date stocktaking_date, location_id, comment from empty_location where update_date > ? order by id",
    ),
    TableFetch(
      name = "growth_rate",
      sqlDeleted = s"select $farmId as farm_id, id from growthrate_deleted where update_date > ? order by id",
      sqlUpserted = s"select $farmId as farm_id, id, name, description, weightlimit from growthrate where update_date > ? order by id",
    ),
    TableFetch(
      name = "growth_rate_curve",
      sqlDeleted = s"select $farmId as farm_id, id from growthratedata_deleted where update_date > ? order by id",
      sqlUpserted = s"select $farmId as farm_id, id, growthrate_id growth_rate_id, agefrom age_from, ageto age_to, weightfrom weight_from, weightto weight_to from growthratedata where update_date > ? order by id",
    ),
    TableFetch(
      name = "fostering",
      sqlDeleted = s"select $farmId as farm_id, id from fostering_deleted where update_date > ? order by id",
      sqlUpserted = {
        s"""select $farmId as farm_id, id, actor_date as foster_date, amount, from_serving_id as from_cycle_id
          |, to_serving_id as to_cycle_id, from_location_id, to_location_id
          |, gilt_id as young_animal_id, comment
          |from fostering where update_date > ? order by id""".stripMargin
      },
    ),
    TableFetch(
      name = "suckling_transfer",
      sqlDeleted = s"select $farmId as farm_id, id from pigltrans_deleted where update_date > ? order by id",
      sqlUpserted =
        s"select $farmId as farm_id, id, actor_date as transfer_date, amount, from_location_id, to_location_id from pigltrans where update_date > ? order by id",
    ),
    TableFetch(
      name = "issue",
      sqlDeleted = s"select $farmId as farm_id, id from issue_deleted where update_date > ? order by id",
      sqlUpserted = {
        s"""
          |select $farmId as farm_id, id, subject, status, reporter_id, assignee_id, lastuser_id, report_date, lastevent_date,
          | issuetype_code, location_id, severity, issue_reason_type_code, deadline, priv is_private
          |from issue where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "issue_event",
      sqlDeleted = s"select $farmId as farm_id, id from issueevent_deleted where update_date > ? order by id",
      sqlUpserted = {
        s"""
          |select $farmId as farm_id, id, issue_id, picture_id, subject, description, status, actor_date event_date, actor_id, assignee_id,
          |issuetype_code, location_id, severity, issue_reason_type_code, deadline, priv is_private
          |from issueevent where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "medicine_usage",
      sqlDeleted = s"select $farmId as farm_id, id from medicine_usage_deleted where update_date > ? order by id",
      sqlUpserted = {
        s"""
          |select  $farmId as farm_id, id
          |, actor_date as usage_date
          |, actor_id
          |, comment
          |, sow_id
          |, boar_id
          |, serving_id as cycle_id
          |, gilt_id as young_animal_id
          |, location_id
          |, prescription_id
          |, illnesstype_code
          |, amount_used
          |, animals_count
          |, weight
          |, inhabitanttype_code
          |, first_usage_id
          |, cure_id
          |, repeat_no
          |, treatment_group
          |, multiprescription_id
          |, needle
          |from medicine_usage where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "cure",
      sqlDeleted = s"select $farmId as farm_id, id from cure_deleted where update_date > ? order by id",
      sqlUpserted = {
        s"""
          |select  $farmId as farm_id, id, prescription_id, illnesstype_code,inhabitanttype_code, dosage, target, target_unit
          |, repetition, repetition_delay, comment
          |from cure where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "prescription",
      sqlDeleted = s"select $farmId as farm_id, id from prescription_deleted where update_date > ? order by id",
      sqlUpserted = {
        s"""
          |select  $farmId as farm_id, id,
          |name,
          |actor_date as prescription_date,
          |comment,
          |expiration_date,
          |packages_count,
          |package_size,
          |unit,
          |medapplication_code,
          |medicine_id,
          |withdrawal_days,
          |add_sow,
          |add_weaner,
          |add_fattener,
          |package_price,
          |drug_provider,
          |drug_provider_extra
          |from prescription where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "medicine",
      sqlDeleted = s"select $farmId as farm_id, id from farmmedicine_deleted where update_date > ? order by id",
      sqlUpserted = {
        s"""
          |select $farmId as farm_id, id,
          |country_id,
          |name,
          |company,
          |units,
          |unit,
          |packages,
          |package,
          |expiration_date,
          |add_sow,
          |add_weaner,
          |add_fattener,
          |externalid,
          |category,
          |nplpackid,
          |nplid,
          |case
          |    when medicine_category = 0 then 'antibiotic'
          |    when medicine_category = 1 then 'vaccine'
          |    when medicine_category = 2 then 'vitamin'
          |    when medicine_category = 3 then 'disinfection'
          |    when medicine_category = 4 then 'antiparasitic'
          |    when medicine_category = 5 then 'hormones'
          |    when medicine_category = 6 then 'antiphlogistics'
          |    when medicine_category = 7 then 'anesthetics'
          |    when medicine_category = 8 then 'antimicrobials'
          |    when medicine_category = 9 then 'antispasmodics'
          |end as medicine_type
          |from farmmedicine where update_date > ? order by id
          |""".stripMargin
      },
    ),
    TableFetch(
      name = "cost",
      sqlDeleted = s"select $farmId as farm_id, id from cost_deleted where update_date > ? order by id",
      sqlUpserted = {
        s"""
          |select $farmId as farm_id, id,
          |location_id,
          |name,
          |amount,
          |cost_unit,
          |cost_date,
          |comment,
          |actor_id,
          |actor_date accounting_date,
          |inhabitanttype_code,
          |costtype_code
          |from cost where update_date > ? order by id
          |""".stripMargin
      },
    ),
  )
}
