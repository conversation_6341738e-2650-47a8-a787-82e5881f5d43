package reactapp.shared.batches.detail

import reactapp.shared.batches.BatchesPageApi.FeedConsumptionId
import sjsgrid.shared.grid.dto.{ RowSaveDTO, ValidRow }

trait FeedConsumptionGridApi {
  def getLastFeeds(): Map[String, FeedConsumptionGrid.Row]

  def list(batchId: Long): Seq[(Long, FeedConsumptionGrid.Row)]

  def save(batchId: Long, feedConsumptions: Seq[RowSaveDTO[FeedConsumptionGrid, Option[Long]]]): Map[Int, FeedConsumptionId]

  def delete(feedConsumptionId: Long): Unit
}
