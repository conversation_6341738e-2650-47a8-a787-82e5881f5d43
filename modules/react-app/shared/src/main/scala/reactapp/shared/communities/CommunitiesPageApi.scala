package reactapp.shared.communities

import monocle.macros.Lenses
import org.scalactic.{ Every, Or }
import pushka.annotation.pushka
import reactapp.shared.communities.CommunitySavingAD._
import sjsgrid.shared.grid.column.ColumnPositions
import sjsgrid.shared.grid.dto._

trait CommunitiesPageApi {

  def count(filtering: GridFilters[CommunityGrid]): Int

  def filteredAndSortedCommunitiesWithPaging(params: DataGetterParams[CommunityGrid]): List[(CommunityServerAD, RowModel[CommunityGrid])]

  def filteredAndSortedCommunities(params: DataGetterParams[CommunityGrid]): List[(CommunityServerAD, RowModel[CommunityGrid])]

  def save(rowsToSave: Seq[RowSaveDTO[CommunityGrid, CommunitySavingAD]]): Map[Int, Or[Long, Every[ServerProblem]]]

  def kpiDefCodeNameMap(): Map[String, RowModel[CommunityKpiDefsGrid]]

  def deleteInvitee(id: Long, groupId: Long): Option[String]

  def communityThatWasNotAccepted(accountId: Long): Long

}

@pushka
case class CommunityServerAD(
  communityIdOpt: Option[Long],
  communityAdminIdOpt: Option[Long],
  kpis: Map[KpiCode, RowModel[CommunityKpiDefsGrid]],
  invitees: Seq[(CommunityInviteeAD, RowModel[CommunityInviteesGrid])],
  members: Seq[(CommunityMemberAD, RowModel[CommunityMembersGrid])],
)

@pushka
case class CommunityInviteeAD(
  communityInviteeIdOpt: Option[Long],
  accountIdOpt: Option[Long],
  groupIdOpt: Option[Long],
)

object CommunityInviteeAD {
  val empty: CommunityInviteeAD = CommunityInviteeAD(None, None, None)
}

@pushka
case class CommunityMemberAD(communityOrganizationIdOpt: Option[Long], orgId: Long, orgName: String)

object CommunityMemberAD {
  def empty(currentOrgId: Long, currentOrgName: String): CommunityMemberAD = CommunityMemberAD(None, currentOrgId, currentOrgName)
}

@pushka
case class CommunitySavingAD(
  communityIdOpt: Option[Long],
  communityAdminIdOpt: Option[Long],
  kpis: Seq[RowSaveDTO[CommunityKpiDefsGrid, KpiCode]],
  invitees: Seq[RowSaveDTO[CommunityInviteesGrid, CommunityInviteeAD]],
  members: Seq[RowSaveDTO[CommunityMembersGrid, CommunityMemberAD]],
)
object CommunitySavingAD {
  type KpiCode = String
  type CommunityId = Long
  type AccountId = Long
}

@Lenses
final case class BenchmarkCommunity(id: Long, name: String, members: Seq[CommunityMember], kpiCodes: Seq[String]) {
  lazy val memberIds: Set[Long] = members.map(_.id).toSet
}

final case class CommunityMember(id: Long, name: String, inHitlist: Boolean, tableView: Boolean)

object CommunitiesExt {

  def allActiveCommunitiesParams: DataGetterParams[CommunityGrid] = {
    import com.softwaremill.tagging._
    DataGetterParams(
      rowsToGet = RowsToGet.AllRows,
      filteringAndSorting = FilteringAndSorting[CommunityGrid](
        sortings = Seq(
          GridSorting[CommunityGrid](CommunityGrid.CommunityName),
        ),
        filters = GridFilters[CommunityGrid](Map(
          CommunityGrid.TermsAndConditions -> true,
          CommunityGrid.IsActive -> true,
        )),
      ),
      columnsHiddenByUser = Set.empty[CommunityGrid].taggedWith[ColumnPositions.HiddenColumns],
    )
  }

  implicit class BenchmarkCommunityExt(g: (CommunityServerAD, RowModel[CommunityGrid])) {
    lazy val id: Long = g._1.communityIdOpt.getOrElse {
      println(s"WARN communityIdOpt in CommunityBenchmarkOrgContext is none")
      -1L
    }

    lazy val name: String = g._2.map.get(CommunityGrid.CommunityName).map(_.asInstanceOf[String]).getOrElse("")

    lazy val members: Seq[CommunityMember] = g._1.members.flatMap {
      case (commMemberAd, commMember) =>
        for {
          inHitList <- commMember.getValue(CommunityMembersGrid.InHitList)
          tableView <- commMember.getValue(CommunityMembersGrid.TableView)
        } yield CommunityMember(commMemberAd.orgId, commMemberAd.orgName, inHitList, tableView)
    }

    lazy val kpiCodeOptions: IndexedSeq[KpiCode] = g._1.kpis
      .filter(_._2.getValue(CommunityKpiDefsGrid.IsSelected).contains(true))
      .keys
      .toIndexedSeq
  }

}
