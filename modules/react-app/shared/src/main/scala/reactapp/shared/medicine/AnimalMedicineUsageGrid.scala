package reactapp.shared.medicine

import reactapp.shared.auth.{ Access, Authorization }
import reactapp.shared.common.grid.CfDependency.WeightUnit
import reactapp.shared.common.grid.{ CfColumnType, CfDependency, GridColumn, GridDefinition }
import reactapp.shared.grid.Field
import reactapp.shared.medicine
import sjsgrid.shared.common.{ Dependencies, RemoteDependency, SharedContext }
import sjsgrid.shared.form.{ FormDefinition, ModelValues }
import sjsgrid.shared.grid.column.ColumnType

import scala.collection.immutable

// Field.AlwaysFetch because it's InMemory grid
sealed trait AnimalMedicineUsageGrid extends GridColumn with Field.FromDb with Field.AlwaysFetch

object AnimalMedicineUsageGrid extends GridDefinition.Editable.MissingValidation[AnimalMedicineUsageGrid] {
  override lazy val labelKey = "js.label.medicineusage"

  override def hasWriteAccess(implicit deps: Dependencies): Boolean = Access.MedicineUsage.write.isAuthorized

  trait MedicineUsageTable extends AnimalMedicineUsageGrid {
    override lazy val sqlSelectOpt: Option[String] = Some(s"mu.$underscoreName")
  }

  trait FarmMedicineTable extends AnimalMedicineUsageGrid {
    override lazy val sqlSelectOpt: Option[String] = Some(s"fm.$underscoreName")

    override def remoteDependencies: Set[RemoteDependency[Any]] = Set(CfDependency.MedicinesWithAdditionalData)
  }

  trait PrescriptionTable extends AnimalMedicineUsageGrid {
    override lazy val sqlSelectOpt: Option[String] = Some(s"pres.$underscoreName")
  }

  trait LocationTable extends AnimalMedicineUsageGrid {
    override lazy val sqlSelectOpt: Option[String] = Some(s"loc.$underscoreName")

    override def remoteDependencies: Set[RemoteDependency[Any]] = Set(CfDependency.Locations)
  }

  case object ActorDate extends MedicineUsageTable with ColumnType.LocalDate

  case object Retreatment extends MedicineUsageTable with ColumnType.Int {
    override lazy val sqlSelectOpt: Option[String] = Some("mu.repeat_no")
  }

  case object Medicine extends FarmMedicineTable with CfColumnType.MedicineName {
    override lazy val sqlSelectOpt: Option[String] = Some("fm.id")
  }

  case object Prescription extends PrescriptionTable with CfColumnType.PrescriptionName {
    override lazy val sqlSelectOpt: Option[String] = Some("pres.id")
  }

  case object Category extends FarmMedicineTable with ColumnType.Text {
    override lazy val sqlSelectOpt: Option[String] = Some("fm.category")
  }

  case object Location extends LocationTable with CfColumnType.Location {
    override lazy val sqlSelectOpt: Option[String] = Some("mu.location_id")
  }

  case object AnimalWeight extends MedicineUsageTable with CfColumnType.WeightKg {
    override lazy val sqlSelectOpt: Option[String] = Some("mu.weight")
    override def label(context: SharedContext): String =
      context.localize("js.label.weight.uw", context.localize("js.label.weight.unit." + context.dependency(WeightUnit).unitCode))
  }

  case object Illnesstype extends MedicineUsageTable with CfColumnType.IllnessType {
    override lazy val sqlSelectOpt: Option[String] = Some(s"mu.illnesstype_code")
  }

  case object AmountUsed extends MedicineUsageTable with ColumnType.BigDecimal

  case object Unit extends FarmMedicineTable with ColumnType.Text {
    override lazy val sqlSelectOpt: Option[String] = Some("fm.unit")
    override lazy val width = 30
  }

  case object Who extends MedicineUsageTable with CfColumnType.ColleagueName with Field.Editable {
    override lazy val sqlSelectOpt: Option[String] = Some("mu.actor_id")
  }

  case object Company extends FarmMedicineTable with ColumnType.Text {
    override lazy val sqlSelectOpt: Option[String] = Some("fm.company")
  }

  override def values: immutable.IndexedSeq[AnimalMedicineUsageGrid] = findValues

}
