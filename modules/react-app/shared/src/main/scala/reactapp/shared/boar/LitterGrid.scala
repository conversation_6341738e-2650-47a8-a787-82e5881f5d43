package reactapp.shared.boar

import reactapp.shared.common.grid.{ GridColumn, GridDefinition }
import reactapp.shared.grid.Field
import sjsgrid.shared.grid.column.ColumnType

import scala.collection.immutable

/**
 * Created by <PERSON>
 * Date: 24. 7. 2020
 * Time: 11:12
 */

// Field.AlwaysFetch because it's InMemory grid
sealed trait LitterGrid extends GridColumn with Field.FromDb with Field.AlwaysFetch {
  val TableName = "s"
  override lazy val sqlSelectOpt: Option[String] = Some(s"$TableName.$columnName")
  lazy val columnName: String = underscoreName
}

object LitterGrid extends GridDefinition[LitterGrid] {

  case object ActorDate extends LitterGrid with Field.FromDb with ColumnType.LocalDate
  case object <PERSON>born extends LitterGrid with Field.FromDb with ColumnType.Int
  case object Stillborn extends LitterGrid with Field.FromDb with ColumnType.Int
  case object <PERSON><PERSON>born extends LitterGrid with Field.FromDb with ColumnType.Int
  case object Mummificated extends LitterGrid with Field.FromDb with ColumnType.Int
  // case object Femalepiglets extends LitterGrid with Field.FromDb with ColumnType.Int

  override lazy val values: immutable.IndexedSeq[LitterGrid] = findValues
}
