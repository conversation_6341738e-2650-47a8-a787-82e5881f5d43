package reactapp.shared.integration.bonarea

import pushka.annotation.pushka
import sjsgrid.shared.common.PushkaPicklers.keepPushkaPicklersImport
import sjsgrid.shared.grid.dto.{ DataGetterParams, FilteringAndSorting, RowModel }

@pushka
case class BonAreaImportMetadata(
  status: String,
  message: Option[String],
  total: Int,
  inserted: Int,
  updated: Int,
  missingFarm: Int,
  missingBatch: Int,
  annotatedHash: Option[String] = None,
)

trait BonAreaImportApi {
  def list(params: DataGetterParams[BonAreaImportGrid]): Seq[(Long, RowModel[BonAreaImportGrid], Option[BonAreaImportMetadata])]

  def count(filteringAndSorting: FilteringAndSorting[BonAreaImportGrid]): Int

  keepPushkaPicklersImport

}
