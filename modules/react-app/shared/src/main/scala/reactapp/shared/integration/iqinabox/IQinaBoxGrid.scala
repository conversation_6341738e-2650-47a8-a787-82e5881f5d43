package reactapp.shared.integration.iqinabox

import reactapp.shared.common.grid.{ GridColumn, GridDefinition }
import reactapp.shared.grid.Field
import sjsgrid.shared.grid.column.ColumnType

import scala.collection.immutable

sealed trait IQinaBoxGrid extends GridColumn

object IQinaBoxGrid extends GridDefinition[IQinaBoxGrid] {

  override def values: immutable.IndexedSeq[IQinaBoxGrid] = findValues

  private val WideWidth = 160
  private val Precise = 3

  sealed trait WithCustomColumnName extends GridColumn with Field.FromDb {
    val TableName = "b"
    override lazy val sqlSelectOpt: Option[String] = Some(s"$TableName.$columnName")
    lazy val columnName: String = underscoreName
  }

  trait FromLogsTable extends WithCustomColumnName {
    override val TableName = "iqinabox_logs"
  }

  trait FromDivergenceTable extends WithCustomColumnName {
    override val TableName = "iqinabox_divergence"
  }

  // CVR
  case object CentralEnterpriseRegisterId extends IQinaBoxGrid with Field.FromDb with ColumnType.LongCol with Field.AlwaysFetch
      with FromLogsTable {
    override lazy val width: Int = WideWidth

  }

  // CHR
  case object CentralLivestockRegisterId extends IQinaBoxGrid with Field.FromDb with ColumnType.LongCol with Field.AlwaysFetch
      with FromLogsTable {
    override lazy val width: Int = WideWidth
  }

  case object IqinaBoxLocation extends IQinaBoxGrid with Field.FromDb with ColumnType.LongCol with Field.AlwaysFetch with FromLogsTable {
    override lazy val columnName: String = "iqinabox_location_id"
  }

  case object WeaningDate extends IQinaBoxGrid with Field.FromDb with ColumnType.LocalDate with Field.AlwaysFetch with FromLogsTable {
    override lazy val columnName: String = "insertion_date"
  }

  case object NumberOfPigs extends IQinaBoxGrid with Field.FromDb with ColumnType.BigDecimal with FromLogsTable with Field.AlwaysFetch

  case object AverageWeight extends IQinaBoxGrid with Field.FromDb with ColumnType.BigDecimal with FromLogsTable with Field.AlwaysFetch {
    override def decimalPlacesToRender: Int = Precise
  }

  case object WeightSpread extends IQinaBoxGrid with Field.FromDb with ColumnType.BigDecimal with FromLogsTable with Field.AlwaysFetch {
    override def decimalPlacesToRender: Int = Precise
  }

  case object NumberOfPigsDivergence extends IQinaBoxGrid with Field.FromDb with ColumnType.BigDecimal with FromDivergenceTable {
    override lazy val columnName: String = "number_of_pigs"
  }

  case object AverageWeightDivergence extends IQinaBoxGrid with Field.FromDb with ColumnType.BigDecimal with FromDivergenceTable {
    override lazy val columnName: String = "average_weight"

    override def decimalPlacesToRender: Int = Precise
  }

  case object WeightSpreadDivergence extends IQinaBoxGrid with Field.FromDb with ColumnType.BigDecimal with FromDivergenceTable {
    override lazy val columnName: String = "weight_spread"

    override def decimalPlacesToRender: Int = Precise
  }

  case object IntegrationStatus extends IQinaBoxGrid with Field.FromDb with ColumnType.Text with Field.AlwaysFetch {
    override lazy val sqlSelectOpt: Option[String] = Some("integration_status")
  }

}
