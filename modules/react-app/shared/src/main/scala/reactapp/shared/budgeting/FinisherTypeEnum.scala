package reactapp.shared.budgeting

import scala.collection.immutable.IndexedSeq

/**
  * Created by <PERSON>
  * Date: 10.4.2018
  * Time: 13:29
  */

sealed trait FinisherTypeEnum extends enumeratum.EnumEntry {
  def labelKey = s"js.label.finishertype.${entryName.toLowerCase}"
}

object FinisherTypeEnum extends enumeratum.Enum[FinisherTypeEnum] {

  case object BelowBoundary extends FinisherTypeEnum
  case object AboveBoundary extends FinisherTypeEnum
  case object NotDefined extends FinisherTypeEnum

  def values: IndexedSeq[FinisherTypeEnum] = findValues
}
