package reactapp.shared.integration.iqinabox.model

import cats.syntax.all._
import pushka.annotation.{ key, pushka }

@pushka
case class IQinaBoxLocation(id: DanishCrownId, name: String)

@pushka
case class IQinaBoxRootLocation(registerIds: RegisterIds, iqinaboxLocations: IQinaBoxLocations) {
  def sublocations: List[IQinaBoxLocation] = iqinaboxLocations.locations

  def hasLocationWithName(weaningLocation: WeaningLocation): Boolean =
    iqinaboxLocations.hasLocationWithName(weaningLocation)

}

@pushka
case class IQinaBoxLocations(locations: List[IQinaBoxLocation]) {

  def hasLocationWithName(weaningLocation: WeaningLocation): Boolean = {
    locations.exists { x =>
      weaningLocation.iqinaboxName.contains(x.name)
    }
  }

}
