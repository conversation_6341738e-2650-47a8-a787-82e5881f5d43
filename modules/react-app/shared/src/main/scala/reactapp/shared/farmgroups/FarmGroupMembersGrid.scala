package reactapp.shared.farmgroups

import reactapp.shared.common.grid.{ CfColumnType, CfDependency, GridColumn, GridDefinition }
import reactapp.shared.grid.Field
import sjsgrid.shared.common.{ Dependencies, RemoteDependency }
import sjsgrid.shared.form.types.LocalDateFieldType.{ LocalDateConf, TargetLocalDate }
import sjsgrid.shared.grid.column.ColumnType

import scala.collection.immutable

sealed trait FarmGroupMembersGrid extends GridColumn with Field.FromDb

object FarmGroupMembersGrid extends GridDefinition.Editable.NoValidation[FarmGroupMembersGrid] {

  case object FarmName extends FarmGroupMembersGrid with CfColumnType.RootHoldingFarmName with Field.Editable with Field.Mandatory {
    override lazy val sqlSelectOpt: Option[String] = Some("o.id")
  }

  case object HoldingName extends FarmGroupMembersGrid with ColumnType.Text {
    override lazy val labelKey: String = "js.label.holding"
    override lazy val sqlSelectOpt: Option[String] = Some("h.name")
  }

  case object ValidFrom extends FarmGroupMembersGrid with ColumnType.LocalDate with Field.Editable {
    override lazy val labelKey: String = "js.label.farmgroup.member.validfrom"
    override lazy val sqlSelectOpt: Option[String] = Some("fgm.valid_from")
    override lazy val defaultConf: LocalDateConf = LocalDateConf(futureWarningOpt = None)
  }

  case object ValidTo extends FarmGroupMembersGrid with ColumnType.LocalDate with Field.Editable {
    override lazy val labelKey: String = "js.label.farmgroup.member.validto"
    override lazy val sqlSelectOpt: Option[String] = Some("fgm.valid_to")
    override lazy val defaultConf: LocalDateConf = LocalDateConf(
      validFrom = TargetLocalDate.Column(ValidFrom, inclusive = false),
      futureWarningOpt = None,
    )
  }
  case object FgMemEntityCreatedByColumn extends CfColumnType.EntityCreatedBy with FarmGroupMembersGrid

  case object FgMemEntityCreatedOnColumn extends CfColumnType.EntityCreatedOn with FarmGroupMembersGrid

  case object FgMemEntityUIColumn extends CfColumnType.EntityUI with FarmGroupMembersGrid

  case object FgMemEntityUpdatedByColumn extends CfColumnType.EntityUpdatedBy with FarmGroupMembersGrid

  case object FgMemEntityUpdatedOnColumn extends CfColumnType.EntityUpdatedOn with FarmGroupMembersGrid

  override def hasWriteAccess(implicit deps: Dependencies): Boolean = true

  override def values: immutable.IndexedSeq[FarmGroupMembersGrid] = findValues

  override def dependencies: Set[RemoteDependency[Any]] = {
    super.dependencies ++ Seq(
      CfDependency.RootHoldingFarms,
      CfDependency.HoldingsWithParents,
      CfDependency.FarmsWithParents,
      CfDependency.Farms,
    )
  }

}
