package reactapp.client.help

import autowire._
import japgolly.scalajs.react.vdom.html_<^._
import japgolly.scalajs.react.{ Callback, ScalaComponent }
import monocle.macros.Lenses
import org.scalajs.dom
import reactapp.client.common.CloudFarmsOps._
import reactapp.client.common.component.TextInput
import reactapp.client.common.{ AutowireClient, CommonCss }
import reactapp.client.facades.{ M, NgPromise }
import reactapp.shared.help.HelpApi
import reactapp.shared.help.HelpApi._
import sjsgrid.client.common.react.ExtVar
import sjsgrid.flash.client.facade.AntJs
import sjsgrid.shared.common.pot.{ Empty, Pot }
import slogging.StrictLogging

import org.scalajs.macrotaskexecutor.MacrotaskExecutor.Implicits._
import scala.scalajs.js
import scala.scalajs.js.annotation.JSExportTopLevel
import scala.util.{ Failure, Success }

/**
  * Created by Milan Satala
  * Date: 11. 12. 2019
  * Time: 14:25
  */
object HelpButton extends StrictLogging {
  @js.native
  trait JsProps[T <: js.Any] extends js.Object {
    var v: T
  }

  @js.native
  trait PropsObj extends js.Object {
    var path: String
    var userInfo: js.Dynamic
  }

  type Props = JsProps[PropsObj]

  @Lenses
  case class State(
    inputValue: String = "",
    pathHelpMap: Map[Path, PageHelp],
    saving: Boolean = false,
    canEditHelpPot: Pot[Boolean] = Empty,
    visible: Boolean = false,
    helpsSeenInThisSession: Set[String] = Set.empty,
  )

  val HelpConfiguration: HelpConfiguration = {
    val jsonObj = js.Dynamic.global.HELPCONFIG.asInstanceOf[js.Object]
    readFromJsObj[HelpConfiguration](jsonObj)
  }

  private val cmp = ScalaComponent.builder[Props](implicitly[sourcecode.FullName].value)
    .initialStateFromProps { props =>
      val currentPageHelpOpt = HelpConfiguration.pathHelpMap.get(props.v.path)

      State(
        inputValue = currentPageHelpOpt.fold("")(_.helpUrl),
        pathHelpMap = HelpConfiguration.pathHelpMap,
      )
    }
    .render { params =>
      val stateEV: ExtVar[State] = ExtVar.state(params)

      val helpDataOpt = stateEV.value.pathHelpMap.get(params.props.v.path)

      val contentOpt = {
        if (stateEV.value.canEditHelpPot.contains(true) || helpDataOpt.exists(_.pageTopicsOpt.isDefined)) Some {
          <.div(
            ^.minWidth := "400px",
            if (stateEV.value.canEditHelpPot.contains(true)) <.form(
              ^.onSubmit --> Callback {
                stateEV.mod(_.copy(saving = true))

                if (stateEV.value.inputValue.nonEmpty) {
                  AutowireClient[HelpApi].saveOrRefresh(params.props.v.path, stateEV.value.inputValue).call().onComplete {
                    case Failure(e) =>
                      e.printStackTrace()
                      dom.window.alert(M("error.saveFailed"))
                      stateEV.mod(_.copy(saving = false))

                    case Success(pageTopicsOpt) =>
                      stateEV.mod((state: State) => {
                        state.copy(
                          saving = false,
                          pathHelpMap = state.pathHelpMap.updated(
                            params.props.v.path,
                            PageHelp(stateEV.value.inputValue, pageTopicsOpt = pageTopicsOpt, isCurrentLang = true),
                          ),
                        )
                      })
                  }
                } else {
                  AutowireClient[HelpApi].clear(params.props.v.path).call().onComplete {
                    case Failure(e) =>
                      e.printStackTrace()
                      dom.window.alert(M("error.saveFailed"))
                      stateEV.mod(_.copy(saving = false))

                    case Success(_) =>
                      stateEV.mod((state: State) => {
                        state.copy(
                          saving = false,
                          pathHelpMap = state.pathHelpMap - params.props.v.path,
                        )
                      })
                  }
                }
              },
              TextInput(
                valueEV = stateEV.zoomL(State.inputValue),
                additionalAttributes = TagMod(
                  ^.placeholder := M("js.help.urlPlaceholder"),
                  ^.display := "inline",
                  ^.width := "320px",
                  ^.disabled := stateEV.value.saving,
                ),
              )(),
              " ",
              <.input(
                ^.`type` := "submit",
                ^.disabled := stateEV.value.saving || (!stateEV.value.inputValue.startsWith("http") && stateEV.value.inputValue.nonEmpty),
                ^.cls := "button small",
                ^.value := (if (helpDataOpt.exists(_.helpUrl == stateEV.value.inputValue)) M("js.label.refresh")
                            else if (stateEV.value.inputValue.nonEmpty) M("js.label.save")
                            else M("js.label.clear")),
              ),
            )
            else EmptyVdom,
            helpDataOpt.map { helpData =>
              <.ul(
                ^.className := CommonCss.FoundationUlOffsetFix,
                helpData.pageTopicsOpt match {
                  case Some(pageTopics) =>
                    val allUnseen = pageTopics.topics.forall(!_.seen)

                    pageTopics.topics.toVdomArray(topic => {
                      <.li(
                        ^.key := topic.data.id,
                        if (allUnseen || topic.seen) EmptyVdom
                        else <.b(^.color := "black", s"${M("js.label.new")}: "),
                        topic.data match {
                          case TopicDb.Text(elementId, title) =>
                            <.a(^.target := "_blank", ^.href := s"${helpData.helpUrl}#${elementId}", title)

                          case TopicDb.Video(name, attachmentUrl) =>
                            TagMod(
                              <.span(^.color := "black", <.i(^.className := "awesome-icon-video-camera"), " "),
                              <.a(^.target := "_blank", ^.href := s"${HelpConfiguration.helpServerUrl}${attachmentUrl}", name),
                            )
                        },
                      )
                    })
                  case None => <.li(<.a(^.target := "_blank", ^.href := helpData.helpUrl, helpData.helpUrl))
                },
              )
            },
          )
        }
        else None
      }

      <.li(
        contentOpt match {
          case Some(content) =>
            AntJs.Popover2(
              obj(
                title = {
                  <.div(
                    ^.className := cssClass("Title wrapper", """display: flex;"""),
                    <.b(
                      ^.className := cssClass("Title text", """flex: 1;"""),
                      helpDataOpt.flatMap(_.pageTopicsOpt.map(_.name)).fold("Help")(name => s"$name - ${M("js.help.popupTitle")}"),
                    ),
                    <.a(
                      ^.className := cssClass("Close", "color: black;"),
                      ^.onClick --> stateEV.modCb(_.copy(visible = false)),
                      <.i(^.cls := "awesome-icon-times"),
                    ),
                  )
                }.rawElement,
                content = content.rawElement,
                trigger = "click",
                placement = "bottomRight",
                visible = stateEV.value.visible,
                onVisibleChange = (visible: Boolean) =>
                  stateEV.mod(_.copy(visible = visible)),
              ),
            )(
              <.a(
                <.i(^.cls := "awesome-icon-question-circle", ^.lineHeight := "0"),
              ),
            )

          case None =>
            <.a(
              ^.onClick --> Callback {
                dom.window.open(
                  url = helpDataOpt.fold(HelpConfiguration.helpHomeUrl)(_.helpUrl),
                  target = "_blank",
                )
              },
              <.i(^.cls := "awesome-icon-question-circle"),
            )
        },
      )
    }
    .componentWillMount(params => {
      Callback {
        NgPromise.toFuture(params.props.v.userInfo)
          .map { userInfo =>
            val roles = userInfo.roles.asInstanceOf[js.Dictionary[Boolean]]
            roles.getOrElse("app_backOffice", false) || roles.getOrElse("app_admin", false)
          }
          .toPotCb(modFn => params.modState(state => state.copy(canEditHelpPot = modFn(state.canEditHelpPot))))
      }
    })
    .componentDidUpdate(params => {
      Callback {
        val currentPageHelpOpt = params.currentState.pathHelpMap.get(params.currentProps.v.path)

        if (
          params.currentState.visible && !params.currentState.canEditHelpPot.contains(
            true,
          ) && !params.currentState.helpsSeenInThisSession.contains(params.currentProps.v.path)
        ) for {
          pageHelp <- currentPageHelpOpt
          pageTopics <- pageHelp.pageTopicsOpt
          if pageTopics.topics.exists(!_.seen)

        } {
          params.modState((state: State) =>
            state.copy(helpsSeenInThisSession = state.helpsSeenInThisSession + params.currentProps.v.path),
          ).runNow()

          AutowireClient[HelpApi].saveSeenTopics(pageHelp.helpUrl, pageTopics.topics.map(_.data.id)).call().failed.foreach(e =>
            logger.error("Failed to mark page as seen.", e),
          )
        }

        if (
          params.prevProps.v.path != params.currentProps.v.path || params.prevState.canEditHelpPot != params.currentState.canEditHelpPot
        ) {
          params.modState(state => {
            state.copy(
              inputValue = currentPageHelpOpt.fold("")(_.helpUrl),
              visible = {
                !state.helpsSeenInThisSession.contains(params.currentProps.v.path) &&
                currentPageHelpOpt.exists(_.pageTopicsOpt.exists(_.topics.exists(!_.seen))) &&
                params.currentState.canEditHelpPot.contains(false)
              },
            )
          }).runNow()
        }
      }
    })
    .build

  js.Dynamic.global.window.updateDynamic("HelpButton")(cmp.toJsComponent.raw)
}
