package reactapp.client.pages.reports.insight.graphs

import reactapp.shared.portlets.PortletDefinitionStructures.TimeSeriesDraw
import reactapp.shared.portlets.{ ScalaSeries, ScalaSinglePos }

import scala.scalajs.js.JSConverters._

object GraphJs {

  implicit class ScalaSinglePosMulti(p: ScalaSinglePos) {
    lazy val asJs = SinglePosMultiSeries(x = p.x.toString, y = p.y)
  }

  implicit class ScalaSeriesExt(s: ScalaSeries) {
    val t = s.`type` match {
      case TimeSeriesDraw.LineChart => LineChart
      case TimeSeriesDraw.BarChart  => BarChart
      case _                        => LineChart
    }

    lazy val asJs = SeriesMultiSeries(
      key = s.key,
      kpiName = s.kpiName,
      contextName = s.contextName,
      kpiMeta = s.kpiMeta,
      values = s.values.map(_.asJs).toJSArray,
      `type` = t,
    )
  }

}
