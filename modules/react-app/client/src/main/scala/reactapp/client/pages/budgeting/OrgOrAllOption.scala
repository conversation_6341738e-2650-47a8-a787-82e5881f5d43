package reactapp.client.pages.budgeting

import monocle.macros.Lenses
import pushka.annotation.pushka
import reactapp.client.common.component.ReactSelectOption
import reactapp.client.facades.M

import scala.scalajs.js.annotation.JSExport

/**
  * Created by <PERSON>
  * Date: 24.1.2018
  * Time: 15:37
  */

@pushka
sealed trait OrgOrAllOption extends ReactSelectOption

object OrgOrAllOption {

  @Lenses
  case class OrganizationOption(id: Long, name: String) extends OrgOrAllOption {
    @JSExport override val value: String = s"${id.toString}"
    @JSExport override val label: String = name
  }

  case object HoldingOption extends OrgOrAllOption {
    @JSExport override val value: String = "holding"
    @JSExport override val label: String = M("js.option.wholeholding")
  }

  case object NoneOption extends OrgOrAllOption {
    @JSExport override val value: String = ""
    @JSExport override val label: String = M("js.option.notransfer")
  }

}
