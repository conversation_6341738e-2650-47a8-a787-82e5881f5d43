package reactapp.client.pages.reports.overview.components.forms

import reactapp.client.common.CfContext
import reactapp.client.facades.{ CodeTypeJs, M }
import reactapp.client.pages.reports.Model.{ DateRange, InhabitantTransfer }
import reactapp.client.pages.reports.overview.Utils.getInhabitantsCodeTypesJsMap
import reactapp.client.pages.reports.overview.components.RangePicker
import reactapp.shared.common.grid.CfDependency
import sjsgrid.client.common.react.ExtVar
import sjsgrid.client.grid.SjsGridOps.o
import sjsgrid.flash.client.facade.AntJs
import slinky.core.FunctionalComponent
import slinky.core.annotations.react
import slinky.core.facade.Fragment
import slinky.web.html_<^.<

@react object TransferForm {
  case class Props(timeRangeEV: ExtVar[DateRange], inhabitantEV: ExtVar[Option[InhabitantTransfer]], cfContext: CfContext)

  val component: FunctionalComponent[Props] = {
    FunctionalComponent[Props] {
      case Props(timeRangeEV, inhabitantEV, cfContext) =>
        val inhabitantsMap = getInhabitantsCodeTypesJsMap(cfContext)

        def codeToName(codeType: CodeTypeJs) =
          cfContext.dependency(CfDependency.InhabitantTypes).codeToName(codeType.code).getOrElse("")

        def formatName(from: CodeTypeJs, to: CodeTypeJs): String = {
          Seq(from, to)
            .map(codeToName)
            .reduce((a, b) => s"$a -> $b")
        }

        def formatCode(from: CodeTypeJs, to: CodeTypeJs): String = {
          Seq(from, to)
            .map(_.code)
            .reduce((from, to) => InhabitantTransfer(from, to).toStringValue)
        }

        val fromToTransfers: List[(CodeTypeJs, CodeTypeJs)] = {
          List(("PIGL", "WEAN"), ("WEAN", "FATT"), ("WEAN", "GILT"))
            .map { case (from, to) => (inhabitantsMap(from), inhabitantsMap(to)) }
        }

        def handleOnFromSelect(value: String, optInstance: Any): Unit =
          inhabitantEV.set(Some(InhabitantTransfer.fromStringValue(value)))

        Fragment(
          AntJs.Form(
            o(
              layout = "horizontal",
              labelCol = o(span = 8),
              wrapperCol = o(span = 16),
            ),
          )(
            AntJs.FormItem(o(
              label = M("js.label.range"),
              required = true,
            )) {
              RangePicker(timeRangeEV)
            },
            AntJs.FormItem(o(
              label = "Transfer type",
              required = true,
            )) {

              AntJs.Select(
                o(
                  layout = "horizontal",
                  labelCol = o(span = 8),
                  wrapperCol = o(span = 16),
                  onSelect = handleOnFromSelect _,
                ),
              )(
                fromToTransfers.map {
                  case (from, to) =>
                    AntJs.SelectOption(o(title = formatName(from, to), key = formatCode(from, to)))(<.span(formatName(from, to)))
                },
              )
            },
          ),
        )
    }
  }
}
