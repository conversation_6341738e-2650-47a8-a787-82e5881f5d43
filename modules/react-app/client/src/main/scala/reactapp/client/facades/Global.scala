package reactapp.client.facades

import reactapp.client.common.CloudFarmsOps._

import scala.scalajs.js
import scala.scalajs.js.annotation.{ JSGlobal, JSImport }

/**
  * Created by Milan Satala
  * Date: 15.6.2016
  * Time: 9:44
  */
@js.native
@JSGlobal("Language")
class Language extends js.Object {
  var code: String = js.native
  var name: String = js.native
}

object Global {
  val languages = js.Dynamic.global.LANGUAGES.asInstanceOf[js.Array[Language]]

  val languagesMap: Map[String, String] = languages.map(lang => lang.code -> lang.name).toMap
}
