package reactapp.client.pages.reports.benchmarkfarmtofarm

import autowire._
import domainz.organization.Organization._
import japgolly.scalajs.react.vdom.VdomElement
import pushka.json._
import reactapp.client.common.CloudFarmsOps._
import reactapp.client.common.component.HoverMenu2.RefreshButton
import reactapp.client.common.component._
import reactapp.client.common.component.useCustomViews.ViewUrlState
import reactapp.client.common.component.views.{ AntViewsEditor, CommonViewEditorActionHandler, ViewEditorActionHandler }
import reactapp.client.common.hook.RootFunctionalComponent
import reactapp.client.common.{ AutowireClient, CfContext }
import reactapp.client.facades.{ AngularAppServices, M }
import reactapp.shared.common.SharedConstants
import reactapp.shared.common.grid.CfDependency
import reactapp.shared.portlets.NumberFormatUtils.doubleWithTrailingZeroesInDecimalsReplacesWithBlanks
import reactapp.shared.reports.benchmarkfarmtofarm.BenchmarkFarmToFarmReportPageApi.AdditionalData
import reactapp.shared.reports.benchmarkfarmtofarm._
import reactapp.shared.reports.common.{ CustomReportType, KpiSection, ReportTypeEnum }
import reactapp.shared.view.ViewId
import reactapp.shared.view.ViewTransformation.{ V1Reference, V1ViewObject }
import sjsgrid.client.common.PageContext
import sjsgrid.client.common.react.ExtVar
import sjsgrid.client.form.FormModel
import sjsgrid.client.grid.GridModel
import sjsgrid.client.grid.hook.GridHook._
import sjsgrid.client.grid.hook.GridRowModelType
import sjsgrid.client.grid.hook.ScalaHooks._
import sjsgrid.shared.common.FieldValue
import sjsgrid.shared.common.PushkaPicklers._
import sjsgrid.shared.grid.ExportTarget
import sjsgrid.shared.grid.dto.RowModel
import slinky.core.ReactComponentClass
import slinky.core.facade.ReactElement
import slinky.web.html_<^._

import java.time.{ Instant, LocalDate }
import scala.concurrent.ExecutionContext.Implicits.global
import scala.scalajs.js
import scala.util.{ Failure, Success }

object BenchmarkFarmToFarmReportPage {

  import CfDependency._
  import CustomReportType._

  val component: ReactComponentClass[AngularAppServices] = {
    val gridDependencies = BenchmarkFarmToFarmReportGrid().dependencies
    val allReportStructures = CfDependency.ReportStructures(withCommunityReports = true, withCustomReports = true)
    val pageDependencies = Set(allReportStructures, RootHoldingStructure, BenchmarkCommunities, FarmGroups)

    RootFunctionalComponent(gridDependencies ++ pageDependencies) { implicit pageContext =>
      val allOrganizationsInRootHolding = pageContext.dependency(RootHoldingStructure)
      val benchmarkCommunities = getBenchmarkCommunitiesSortedOrganizations
      val farmGroups = pageContext.dependency(FarmGroups)

      val initGridDef = BenchmarkFarmToFarmReportGrid()
      val defaultReportStructuresMap = pageContext.dependency(allReportStructures)
      val reportTypes = getReportTypes(defaultReportStructuresMap)
      val defaultReportType = reportTypes.headOption.getOrElse(ReportTypeEnum.Breed)
      val kpisToShow = getKpiCodesForReportType(defaultReportType, defaultReportStructuresMap)
      val gridScreenViewName = initGridDef.viewName
      val initialView = BenchmarkFarmToFarmView(reportType = defaultReportType, kpisToShow = kpisToShow)
      val initialState = State(
        view = initialView,
        initialView = true,
        gridDefinition = initGridDef,
        criteriaForm = FormModel.fromModelValues2(CriteriaForm, pageContext)(
          CriteriaForm.From -> Some(initFromDate),
          CriteriaForm.To -> Some(initToDate),
        ),
        reportTypes = reportTypes,
        defaultReportStructuresMap = defaultReportStructuresMap,
      )

      val stateEV = usePersistentUrlStateEV(initialState = initialState, urlStateLens = State.urlState)

      val viewTransformer = new BenchmarkFarmToFarmViewTransformer(allOrganizationsInRootHolding, farmGroups, benchmarkCommunities)
      val viewHandle = useCustomViews(
        gridScreenViewName,
        v1Reference = Some(V1Reference("benchmarkfarm2farm", "global", V1ViewObject.Settings)),
        v1ViewTransformerOpt = Some(viewTransformer),
        v3ViewTransformerOpt = Some(viewTransformer),
      )
      val availableViews = viewHandle.viewsStateEV.value.availableViews
      val selectedViewIdOptEV = viewHandle.urlViewStateEV.zoomL(ViewUrlState.selectedViewIdOpt)

      val previousReportType = usePrevious(stateEV.value.view.reportType)
      val newReportType = stateEV.value.view.reportType
      val previousUrlView = usePrevious(selectedViewIdOptEV.value)
      val newUrlView = selectedViewIdOptEV.value
      val viewWasChanged = previousUrlView != newUrlView
      val previousInitialView = usePrevious(stateEV.value.initialView)
      val newInitialView = stateEV.value.initialView
      val initialViewChanged = previousInitialView != newInitialView

      // Executed just once on page load
      useEffect() { () =>
        if (newUrlView.isEmpty) {
          // Initialize default view if it exists and no view is in the URL
          availableViews.maybeDefaultScopeAndView.map {
            case (scope, defaultView) =>
              val organizations = getSupportedOrganizationsInView(
                defaultView.data,
                allOrganizationsInRootHolding,
                farmGroups,
                benchmarkCommunities,
              )
              stateEV.mod { state =>
                state.copy(
                  view = defaultView.data.copy(orgOptions = organizations.map(_.uniqueId)),
                  initialView = false,
                )
              }
              selectedViewIdOptEV.set(Some(ViewId(scope, defaultView.name)))
          }
        } else {
          // Initialize view from the URL if there's any
          newUrlView.map(setView)
        }
      }

      useEffect(newUrlView) { () =>
        // When clicking "Reset settings" button (changing view to "init view") - don't consider view as changed and don't update state
        newUrlView.map(setView)
      }

      def setView(viewId: ViewId): Option[Unit] = {
        availableViews.scopesMap(viewId.scope)
          .viewsMap.get(viewId.name)
          .map(_.data)
          .map { view =>
            val organizations = getSupportedOrganizationsInView(
              view,
              allOrganizationsInRootHolding,
              farmGroups,
              benchmarkCommunities,
            )
            stateEV.mod { state =>
              state.copy(
                view = view.copy(orgOptions = organizations.map(_.uniqueId)),
                initialView = false,
              )
            }
          }
      }

      useEffect(newReportType) { () =>
        // Clear KPI list when view is not changed to another and initial view is not changed and report type changed
        if (!viewWasChanged && !initialViewChanged && (previousReportType != newReportType)) {
          stateEV.mod(state => {
            state.copy(view = {
              state.view.copy(
                kpisToShow = getKpiCodesForReportType(newReportType, state.defaultReportStructuresMap),
                breed = "",
                useCommonWeights = false,
              )
            })
          })
        }

        // If report type is changed
        //  - from some community report to non community report
        //  - from non community report to community report
        //  - or from one community report to another
        //  farm/holding selection needs to be cleared otherwise could keep non relevant organization(s) selected
        val wasCommunityReport = previousReportType.isCommunityReport
        val isCommunityReport = newReportType.isCommunityReport
        val communityReportChanged = wasCommunityReport && isCommunityReport && previousReportType != newReportType
        if (!viewWasChanged && !initialViewChanged && ((wasCommunityReport != isCommunityReport) || communityReportChanged)) {
          stateEV.mod(state => state.copy(view = state.view.copy(orgOptions = Seq.empty)))
        }
      }

      lazy val gridHandle: GridHandle[BenchmarkFarmToFarmReportColumn, AdditionalData] = {
        useGrid(GridParams[BenchmarkFarmToFarmReportColumn, AdditionalData](
          gridDefinition = stateEV.value.gridDefinition,
          gridModelEV = stateEV.zoomL(State.gridModel),
          rowModel = GridRowModelType.InMemory(),
          showStatusColumn = false,
          headerRendererOverride = {
            case BenchmarkFarmToFarmReportColumn.KpiValue(orgWithParent) =>
              val iconClsOpt = orgWithParent.org match {
                case _: FarmThin      => Some("awesome-icon-home")
                case _: HoldingThin   => Some("awesome-icon-building-o")
                case _: FarmGroupThin => Some("awesome-icon-sitemap")
                case _: CommunityThin => Some("awesome-icon-group")
                case _                => None
              }

              val icon: ReactElement = iconClsOpt match {
                case Some(iconCls) => <.i(^.className := iconCls + " " + cssClass(
                    "organization column header icon padding",
                    "paddingRight: 4px;",
                  ))
                case None => NoContent
              }

              val fontWeight = orgWithParent.org match {
                case _: FarmGroupThin | _: HoldingThin | _: CommunityThin => "bold"
                case _                                                    => "normal"
              }

              <.span(
                ^.className := cssClass(
                  "organization column header text weight",
                  s"fontWeight: $fontWeight;",
                ),
                icon,
                orgWithParent.org.name,
              )
          },
          cellRendererOverrideFn = row => {
            case FieldValue(kpiColumn: BenchmarkFarmToFarmReportColumn.KpiValue, Some(value: BigDecimal)) =>
              val kpiValue = {
                if (stateEV.zoomL(State.view).zoomL(BenchmarkFarmToFarmView.alignNumbersToDecimalPoint).value) {
                  val maxDecimals = stateEV.zoomL(State.gridModel).value.rowsMap.values.toSeq.map(_.additionalData.decimalPlaces).:+(0).max
                  alignNumbersToDecimalPoint(row.additionalData.decimalPlaces, maxDecimals, value)
                } else pageContext.formatNumber(value.toDouble)
              }
              kpiColumn.organization.org match {
                case _: HoldingThin | _: FarmGroupThin | _: CommunityThin => s"<b>$kpiValue</b>"
                case _                                                    => kpiValue
              }

            case BenchmarkFarmToFarmReportColumn.LineId(Some(value)) if row.additionalData.isHeaderRow =>
              s"<b>$value</b>"

            case BenchmarkFarmToFarmReportColumn.LineId(Some(value)) =>
              kpiLink(row.additionalData.kpiCode, value)
          },
          getPermanentId = gridRow => Some(gridRow.additionalData.kpiCode),
        ))
      }

      def onShowReportButtonClick(): (ExtVar[State], RowModel[CriteriaForm]) => Unit = {
        (stateEV, rowModel) =>
          val calculationAt = Instant.now()
          val enabledForm = stateEV.zoomL(State.criteriaForm).value.modFields((_, instance) => instance.setDisabled(false))
          val disabledForm = stateEV.zoomL(State.criteriaForm).value.modFields((_, instance) => instance.setDisabled(true))

          stateEV.mod { state =>
            state.copy(
              isReportLoading = true,
              calculationAtDisplay = None,
              criteriaForm = disabledForm,
              calculationParams = Some(CalculationParams(state.view, state.criteriaForm)),
            )
          }

          AutowireClient[BenchmarkFarmToFarmReportPageApi].getGridData(rowModel, stateEV.zoomL(State.view).value)
            .call()
            .onComplete {
              case Success(serverResponse) =>
                val orgOptions = OrganizationWithParent.deserializeOrganizations(stateEV.value.view.orgOptions)(
                  allOrganizationsInRootHolding,
                  farmGroups,
                  benchmarkCommunities,
                )

                val gridDef = BenchmarkFarmToFarmReportGrid(orgOptions)
                val rowsWithResolvedKpiValues = serverResponse.serverRows.map { sr =>
                  /** Map KPI values from the response with the column (headers = holding or farm) */
                  sr.copy(row = resolveKpiValues(gridDef)(sr.row))
                    .row
                    .toGridRow(sr.additionalData, gridDef)
                }
                val kpiGridModel = GridModel.fromRows(rowsWithResolvedKpiValues)
                stateEV.mod(_.copy(
                  gridDefinition = gridDef,
                  gridModel = kpiGridModel,
                  isReportLoading = false,
                  calculationAtDisplay = Some(calculationAt),
                  criteriaForm = enabledForm,
                ))
              case Failure(error) =>
                println(error)
                stateEV.mod(_.copy(
                  isReportLoading = false,
                  calculationAtDisplay = Some(calculationAt),
                  criteriaForm = enabledForm,
                ))
            }
      }

      def viewsBtn(stateEV: ExtVar[State])(implicit viewWriter: pushka.Writer[BenchmarkFarmToFarmView]): VdomElement = {
        def modal(closeModal: () => Unit, visible: Boolean): ReactElement = {
          val currentViewEV = stateEV.zoomL(State.view)
          val viewsStateEV = viewHandle.viewsStateEV
          val benchmarkFarmToFarmReportViewEditor: ReactElement = BenchmarkFarmToFarmReportViewEditor(
            currentViewEV,
            stateEV.zoomL(State.defaultReportStructuresMap)
              .value.getOrElse(
                stateEV.zoomL(State.view).zoomL(BenchmarkFarmToFarmView.reportType).value,
                Seq.empty,
              ),
            stateEV.zoomL(State.reportTypes).value,
          )
          implicit val viewEditorActionHandler: ViewEditorActionHandler[BenchmarkFarmToFarmView] = new CommonViewEditorActionHandler(
            gridScreenViewName,
            currentViewEV,
            viewsStateEV,
            selectedViewIdOptEV,
            viewWriter,
          )
          AntViewsEditor(AntViewsEditor.Props(
            closeModal,
            visible,
            currentViewEV,
            viewsStateEV,
            selectedViewIdOptEV,
            cfContext = pageContext,
            benchmarkFarmToFarmReportViewEditor,
            initialDimensions = AntViewsEditor.Dimensions(981, 531),
          ))
        }

        HoverMenu.modalOpt(
          label = M("js.label.views"),
          icon = "menu-icon awesome-icon-eye",
          renderModalCmpOpt = Some(modal),
          idOpt = Some(SharedConstants.ViewsBtnId),
        ).toScalaJSReact
      }

      val validateExportCriteriaForm = stateEV.value.criteriaForm.userInputsOrErrors

      PageLayout.headerAndPositionPage(
        pageTitle = M("js.title.benchmarkfarm2farm"),
        position = NoContent,
        topContent = TopContent(stateEV, onShowReportButtonClick()),
        content = ContentLayout.fullscreenGrid(
          hoverMenu = gridHandle.hoverMenu(
            showMultiEntryBtn = false,
            showRefreshBtn = RefreshButton.Hidden,
            additionalMenuItems = Seq(
              viewsBtn(stateEV),
              HoverMenu2.exportWithSubMenuThroughPost(
                urlPrefix = s"/export/reports/bf2f",
                disabled = validateExportCriteriaForm.isBad,
                customParamsMap = {
                  val bf2fViewParam = Some {
                    "bf2fView" -> write(stateEV.value.view)
                  }
                  val criteriaFormParam = validateExportCriteriaForm.toOption.map { userInputs =>
                    "criteriaForm" -> write(RowModel[CriteriaForm](userInputs.definedValuesMap))
                  }
                  (bf2fViewParam ++ criteriaFormParam).toMap
                },
                excludedExportTargets = Set(ExportTarget.Db),
              ),
            ),
          ),
          grid = gridHandle.gridElement,
        ),
      )
    }
  }

  // Account views are available across many holdings for the given user that created such views.
  // Considering that the view contains information about organizations, it is needed to "hide" pre-selected  organizations
  // when user switches to different holding and pre-selected organizations are not part of given holding or community.
  private def getSupportedOrganizationsInView(
    view: BenchmarkFarmToFarmView,
    allOrganizationsInRootHolding: Holding,
    farmGroups: Seq[FarmGroup],
    benchmarkCommunities: Seq[Community],
  ): Seq[OrganizationWithParent] =
    OrganizationWithParent.deserializeOrganizations(view.orgOptions)(allOrganizationsInRootHolding, farmGroups, benchmarkCommunities)

  private def getBenchmarkCommunitiesSortedOrganizations(implicit cfContext: CfContext): Seq[Community] = {
    cfContext.dependency(BenchmarkCommunities)
      .map { bc =>
        val allOrganizationsInCommunity = bc.members.map(o => FarmThin(o.id, o.name)).sortBy(_.name)
        Community(
          flatOrg = CommunityThin(bc.id, bc.name),
          members = allOrganizationsInCommunity,
        )
      }
  }

  private def initFromDate = LocalDate.now().minusMonths(1).withDayOfMonth(1)

  private def initToDate = LocalDate.now().withDayOfMonth(1).minusDays(1)

  private def resolveKpiValues(
    gridDefinition: BenchmarkFarmToFarmReportGrid,
  )(
    row: RowModel[BenchmarkFarmToFarmReportColumn],
  ): RowModel[BenchmarkFarmToFarmReportColumn] = {
    val newMap = row.map.flatMap {
      case (column, value) => column match {
          case BenchmarkFarmToFarmReportColumn.KpiValue(organization) =>
            gridDefinition.kpiCols.find(_.organization == organization).map(col => col -> value)
          case _ =>
            Some(column -> value)
        }
    }
    RowModel(newMap, gridDefinition)
  }

  private def getReportTypes(defaultReportStructuresMap: Map[ReportTypeEnum, Seq[KpiSection]]): Seq[ReportTypeEnum] = {
    val reportTypes = defaultReportStructuresMap.keySet.toSeq

    // Keeping consistent order in the "Report type" selection
    //  - Static reports (the same order with the selection on the old BF2F page)
    //  - Custom reports (sorted by translated label)
    //  - Community reports (sorted by community name)
    val staticReportTypes = ReportTypeEnum.values.filter {
      case rt @ (ReportTypeEnum.Breed | ReportTypeEnum.Gilt | ReportTypeEnum.Weaner | ReportTypeEnum.Fattener |
          ReportTypeEnum.ProductionOverview | ReportTypeEnum.Accounting | ReportTypeEnum.Feed) => reportTypes.contains(rt)
      case ReportTypeEnum.Location | ReportTypeEnum.Batch | ReportTypeEnum.BatchConsolidation |
          ReportTypeEnum.VirtualBatchCons | CustomReportType(_, _) => false
    }
    val customReportTypes = reportTypes.collect { case rt @ CustomReportType(_, _) if !rt.isCommunityReport => rt }.sortBy(_.label)
    val communityReportTypes = reportTypes.collect { case rt @ CustomReportType(_, _) if rt.isCommunityReport => rt }.sortBy(_.label)

    staticReportTypes ++ customReportTypes ++ communityReportTypes
  }

  private def getKpiCodesForReportType(
    reportType: ReportTypeEnum,
    reportStructures: Map[ReportTypeEnum, Seq[KpiSection]],
  ): Set[String] = {
    reportStructures
      .getOrElse(reportType, Seq.empty)
      .flatMap(_.visibleKpis.filter(_.isVisible).map(_.kpi.code))
      .toSet
  }

  private def alignNumbersToDecimalPoint(
    decimalPlaces: Int,
    maxDecimals: Int,
    value: BigDecimal,
  )(implicit pageContext: PageContext): String = doubleWithTrailingZeroesInDecimalsReplacesWithBlanks(
    decimalPlaces,
    maxDecimals,
    pageContext.formatNumber,
  )(value.toDouble)

  private def kpiLink(kpiCode: String, label: String) =
    s"""<a href="https://help.cloudfarms.com/kpis#$kpiCode" target="_blank">$label</a>"""

  keepPushkaPicklersImport

  js.Dynamic.global.window.updateDynamic("BenchmarkFarmToFarmReportPage")(component)

}
