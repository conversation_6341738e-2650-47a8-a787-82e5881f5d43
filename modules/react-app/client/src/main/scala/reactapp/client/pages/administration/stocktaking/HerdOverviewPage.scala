package reactapp.client.pages.administration.stocktaking

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import autowire._
import com.cloudfarms.utils.CaseSensitiveAlphaNumOrdering
import domainz.common.AnimalType
import enumeratum.EnumEntry
import japgolly.scalajs.react.{ Callback, React }
import japgolly.scalajs.react.component.builder.Lifecycle
import japgolly.scalajs.react.vdom.html_<^._
import monocle.macros.Lenses
import org.scalactic.{ Bad, Good }
import pushka.annotation.pushka
import reactapp.client.common.{ AutowireClient, CfContext, CloudFarmsOps }
import reactapp.client.common.CloudFarmsOps._
import reactapp.client.common.component.useViews.ViewUrlState
import reactapp.client.common.component.{ CollapsibleFieldset, ContentLayout2, HoverMenu2, InfoOverlay, JSURL2, LoadingOverlay, PageLayout2, VerticalField }
import reactapp.client.common.page.PageComponentBuilder
import reactapp.client.facades.{ AngularAppServices, M }
import reactapp.client.pages.SellingPage3
import reactapp.client.pages.SellingPage3.AdditionalViewData
import reactapp.client.pages.administration.stocktaking.HerdOverviewGridCmp.LocationNode
import reactapp.client.pages.batches.BatchesPage
import reactapp.shared.administation.stocktaking.herdoverview.HerdOperation._
import reactapp.shared.administation.stocktaking.Stocktaking2PageApi.LocationId
import reactapp.shared.administation.stocktaking.herdoverview.{ HerdOperation, HerdOverviewDateRangeForm, HerdOverviewDetailAnimalOperationData, HerdOverviewLocationTree, RowType }
import reactapp.shared.administation.stocktaking.Stocktaking2PageApi
import reactapp.shared.administation.stocktaking.stocktakingpage.Stocktaking2PageGrid
import reactapp.shared.batches.detail.{ BuyingGrid, DeadGrid, LocalTransfersGrid, SellingGrid }
import reactapp.shared.batches.{ BatchEventGrid, BatchLocationsGrid, BatchesGrid, BatchesGridColumn }
import reactapp.shared.common.SharedConstants
import reactapp.shared.common.grid.CfDependency
import reactapp.shared.selling.{ SellingFilterForm, SellingScreenGrid }
import reactapp.shared.view.ViewTransformation.View
import sjsgrid.client.common.ScalaJsOps.writeToJs
import sjsgrid.client.common.react.ExtVar
import sjsgrid.client.form.FormModel
import sjsgrid.client.form.component.Checkbox
import sjsgrid.client.page.{ MountableBackend, PageBackend }
import sjsgrid.flash.client.css.EmotionCss.cssClass
import sjsgrid.shared.common.ModelType
import sjsgrid.shared.common.PushkaPicklers._
import sjsgrid.shared.common.dto.{ DateEntry, FilterRange }
import sjsgrid.shared.common.pot.{ Pending, Pot, Ready }
import sjsgrid.shared.form.{ UserInputs, ValidForm }
import sjsgrid.shared.grid.{ ExportTarget, FilterModelType }
import sjsgrid.shared.grid.column.{ ColumnsState, GridColumnBase }
import sjsgrid.shared.grid.dto.{ FilteringAndSorting, GridFilters, GridSorting }
import org.scalajs.macrotaskexecutor.MacrotaskExecutor.Implicits._
import reactapp.shared.auth.FarmTypeS

import scala.scalajs.js
import scala.scalajs.js.URIUtils

object HerdOverviewPage {

  type Props = CfContext

  case class Detail(
    loc: LocationNode,
    at: AnimalType,
    startDateOpt: Option[LocalDate],
    startCountOpt: Option[Int],
    startWeightOpt: Option[BigDecimal],
    startTotalWeightOpt: Option[BigDecimal],
    endDate: LocalDate,
    endCount: Int,
    endWeightOpt: Option[BigDecimal],
    endTotalWeightOpt: Option[BigDecimal],
  )

  @Lenses
  case class State(
    dateRangeForm: FormModel[HerdOverviewDateRangeForm],
    effectiveAnimalTypes: Seq[AnimalType],
    addWeanedPiglets: Boolean,
    farmType: FarmTypeS,
    urlState: UrlState,
    gridModelPot: Pot[HerdOverviewLocationTree] = Pot.empty,
    detailOpt: Option[Detail] = None,
    showAge: Boolean = false,
  )

  @pushka
  @Lenses
  case class UrlState(
    detailAnimalsButtons: Seq[(AnimalType, Boolean)],
    startDateOpt: Option[LocalDate] = None,
    endDateOpt: Option[LocalDate] = None,
    showDetail: Boolean = false,
    hideDiffColumns: Boolean = false,
    hideAllAnimalsColumns: Boolean = false,
    hideWeightColumns: Boolean = false,
    hideAgeColumns: Boolean = false,
    hideTotalWeightColumns: Boolean = false,
  )

  def loadEffectiveAnimalTypes(params: Lifecycle.ComponentWillMount[CfContext, HerdOverviewPage.State, MountableBackend[
    CfContext,
    HerdOverviewPage.State,
    PageBackend[CfContext, HerdOverviewPage.State, AngularAppServices, HerdOverviewPage.UrlState],
  ]]): Unit = {
    AutowireClient[Stocktaking2PageApi].getEffectiveAnimalTypes().call().foreach { animalTypes =>
      params.toEV.zoomL(State.effectiveAnimalTypes).setCb(animalTypes).runNow()
    }
  }

  def setDefaultFormData(params: Lifecycle.ComponentWillMount[CfContext, State, MountableBackend[
    CfContext,
    State,
    PageBackend[CfContext, State, AngularAppServices, UrlState],
  ]]): Unit = {
    val stateEV = params.toEV

    stateEV.zoomL(State.dateRangeForm).mod(_ => {
      FormModel.fromModelValues2(HerdOverviewDateRangeForm, params.props)(
        HerdOverviewDateRangeForm.StartDate -> stateEV.value.urlState.startDateOpt,
        HerdOverviewDateRangeForm.EndDate -> (stateEV.value.urlState.endDateOpt match {
          case None       => Some(LocalDate.now())
          case Some(date) => Some(date)
        }),
      )
    })
  }

  def render(params: Lifecycle.RenderScope[CfContext, State, _]): VdomElement = {
    val stateEV = params.stateEV
    val dateRangeFormEV = stateEV.zoomL(State.dateRangeForm)
    val urlStateEV = stateEV.zoomL(State.urlState)
    val startDateOptEV = urlStateEV.zoomL(UrlState.startDateOpt)
    val endDateOptEV = urlStateEV.zoomL(UrlState.endDateOpt)

    def onShowClick(
      stateEV: ExtVar[State],
      params: Lifecycle.RenderScope[CfContext, State, _],
      validForm: UserInputs[HerdOverviewDateRangeForm],
    ): Unit = {
      val startDateFieldOpt = validForm(HerdOverviewDateRangeForm.StartDate)
      val endDateField = validForm(HerdOverviewDateRangeForm.EndDate)

      stateEV.zoomL(State.urlState).zoomL(UrlState.startDateOpt).mod(_ => startDateFieldOpt)
      stateEV.zoomL(State.urlState).zoomL(UrlState.endDateOpt).mod(_ => Some(endDateField))
      stateEV.zoomL(State.gridModelPot).set(Pot.empty)
      stateEV.zoomL(State.detailOpt).set(None)

      AutowireClient[Stocktaking2PageApi].getHerdOverviewPageData(startDateFieldOpt, endDateField)
        .call()
        .toPot(stateEV.zoomL(State.gridModelPot))
    }

    val getResultsButton: TagMod = {
      val disabledOrClickable: TagMod = {
        params.state.dateRangeForm.userInputsOrErrors match {
          case Good(validForm) => ^.onClick --> Callback {
              onShowClick(stateEV, params, validForm)
            }
          case Bad(_) => ^.cls := "disabled"
        }
      }

      val content: TagMod = {
        if (params.state.gridModelPot.isPending) {
          TagMod(
            ^.cls := "disabled",
            ^.padding := "7px 25px",
            ^.fontSize := "180%",
            <.span(
              ^.cls := "awesome-icon-spinner spinning-icon",
            ),
          )
        } else {
          M("js.label.get.result")
        }
      }

      <.button(
        ^.id := "showReport",
        ^.cls := "button medium",
        ^.float := "right",
        ^.right := "0.9375em",
        ^.minWidth := "120px",
        ^.maxWidth := "135px",
        ^.minHeight := "43px",
        ^.padding := "4px",
        ^.wordBreak := "break-word",
        disabledOrClickable,
        content,
      )
    }

    def checkboxSetting(
      label: String,
      boolValEV: ExtVar[Boolean],
      htmlIdOpt: Option[String] = None,
      isLast: Boolean = false,
    ): VdomElement = {
      val inputAdditionalAttrs = {
        (params.state.showAge, isLast) match {
          case (true, _) | (false, true) => TagMod(
              ^.padding := "0",
              ^.margin := "0",
            )
          case _ => TagMod()
        }
      }

      val labelAdditionalAttrs = {
        (params.state.showAge, isLast) match {
          case (true, _) | (false, true) => TagMod(
              ^.paddingBottom := "0px",
              ^.paddingTop := "0px",
            )
          case _ => TagMod()
        }
      }

      Checkbox.withLabel(
        label = <.span(
          ^.verticalAlign := "top",
          label,
        ),
        extVar = boolValEV,
        inputAttributes = Seq(
          htmlIdOpt.map(^.id := _).getOrElse(EmptyVdom),
          inputAdditionalAttrs,
        ).toTagMod,
        labelAttributes = labelAdditionalAttrs,
      )
    }

    PageLayout2.headerAndPositionPage(
      pageTitle = M("js.title.herd.overview"),
      position = EmptyVdom,
      topContent = <.div(
        ^.display := "flex",
        ^.flexWrap := "wrap",
        CollapsibleFieldset(
          mandatory = true,
          name = M("js.label.interval"),
          additionalClasses = "small-12 large-3 columns details " + cssClass(
            "herd interval Fieldset styles",
            """ & {
              flex: 3 1 auto;
              overflow-y: hidden;
              overflow-x: visible;
              min-width: 410px;
              min-height: 80px;
              margin-right: 6px;
            }""",
          ),
          content = <.div(
            ^.id := HerdOverviewDateRangeForm.cssId,
            ^.cls := "row",
            VerticalField.datePicker(dateRangeFormEV.zoomL(FormModel.field(HerdOverviewDateRangeForm.StartDate)), "small-4 large-4"),
            VerticalField.datePicker(dateRangeFormEV.zoomL(FormModel.field(HerdOverviewDateRangeForm.EndDate)), "small-4 large-4 left"),
            getResultsButton,
          ).toSlinky,
        ).toScalaJSReact,
        SelectAnimalTypesCmp(
          params.stateEV.zoomL(State.urlState).zoomL(UrlState.detailAnimalsButtons),
          params.state.farmType.allowedInhabitantTypes.flatMap(at => {
            if (params.state.addWeanedPiglets) AnimalType.stocktakingPageSubtypes(at)
            else Set(at)
          }),
          params.state.effectiveAnimalTypes.toSet,
          fieldsetClasses = {
            val flexGrow = params.state.farmType.allowedInhabitantTypes.size * 2
            val flexBasis = params.state.farmType.allowedInhabitantTypes.size * 6

            cssClass(
              "herd animal types fieldset additional styles",
              s""" & {
              flex: $flexGrow 1 $flexBasis%;
              margin-right: 6px;
              min-height: 80px;
            }""",
            )
          },
          ulClasses = cssClass(
            "SelectAnimalTypesCmp animal list additional styles",
            """ & {
              margin: 5px 0 0.875rem 0;
            }""",
          ),
        ).toScalaJSReact,
        CollapsibleFieldset(
          name = M("js.herd.settings"),
          additionalClasses = "small-12 large-3 columns details " + cssClass(
            "herd Additional Settings Fieldset styles",
            """ & {
              flex: 2 1 auto;
              overflow-y: hidden;
              overflow-x: visible;
              min-width: 400px;
              min-height: 80px;
              padding-bottom: 0px;
              margin-right: 6px;
            }""",
          ),
          content = React.Fragment(
            <.div(
              ^.cls := "columns small-6 large-6",
              ^.padding := "0",
              checkboxSetting(
                M("js.herd.hideDiffColumns"),
                params.stateEV.zoomL(State.urlState).zoomL(UrlState.hideDiffColumns),
                Some("hideDiffColumns"),
              ),
              checkboxSetting(
                M("js.herd.hideAllAnimalsColumns"),
                params.stateEV.zoomL(State.urlState).zoomL(UrlState.hideAllAnimalsColumns),
                Some("hideAllAnimalsColumns"),
                isLast = true,
              ),
            ),
            <.div(
              ^.cls := "columns small-6 large-6",
              ^.padding := "0 0 0 10px",
              checkboxSetting(
                M("js.herd.hideWeightColumns"),
                params.stateEV.zoomL(State.urlState).zoomL(UrlState.hideWeightColumns),
                Some("hideWeightColumns"),
              ),
              checkboxSetting(
                M("js.herd.hideAgeColumns"),
                params.stateEV.zoomL(State.urlState).zoomL(UrlState.hideAgeColumns),
                Some("hideAgeColumns"),
              ).when(params.state.showAge),
              checkboxSetting(
                M("js.herd.hideTotalWeightColumns"),
                params.stateEV.zoomL(State.urlState).zoomL(UrlState.hideTotalWeightColumns),
                Some("hideTotalWeightColumns"),
                isLast = true,
              ),
            ),
          ).toSlinky,
        ).toScalaJSReact,
      ),
      content = ContentLayout2.gridWithDetailOnRight(
        hoverMenu = HoverMenu2(
          (stateEV.value.gridModelPot match {
            case Ready(gridModel) => Seq(
                HoverMenu2.exportWithSubMenu(urlPrefix = {
                  s"/export/herdOverview" +
                    s"?startDateOptStr=${CloudFarmsOps.writeToParam(gridModel.startDateOpt)}" +
                    s"&endDateStr=${CloudFarmsOps.writeToParam(gridModel.endDate)}" +
                    s"&selectedAnimalTypes=${CloudFarmsOps.writeToParam(
                      params.state.urlState.detailAnimalsButtons
                        .filter(at => at._2 && params.state.effectiveAnimalTypes.contains(at._1))
                        .map(_._1),
                    )}" +
                    s"&hideDiffColumns=${params.state.urlState.hideDiffColumns}" +
                    s"&hideWeightColumns=${params.state.urlState.hideWeightColumns}" +
                    s"&hideAgeColumns=${params.state.urlState.hideAgeColumns}" +
                    s"&hideTotalWeightColumns=${params.state.urlState.hideTotalWeightColumns}" +
                    s"&hideAllAnimalsColumns=${params.state.urlState.hideAllAnimalsColumns}"
                }),
                HoverMenu2.exportWithSubMenuLabeled(
                  label = M("js.stocktaking.export.tablesByAnimals.title"),
                  className = "herd-animal-export",
                  awesomeIcon = "awesome-icon-file-text",
                  urlPrefix = s"/export/herdOverviewAnimals" +
                    s"?startDateOptStr=${CloudFarmsOps.writeToParam(gridModel.startDateOpt)}" +
                    s"&endDateStr=${CloudFarmsOps.writeToParam(gridModel.endDate)}" +
                    s"&selectedAnimalTypes=${CloudFarmsOps.writeToParam(
                      params.state.urlState.detailAnimalsButtons
                        .filter(at => at._2 && params.state.effectiveAnimalTypes.contains(at._1))
                        .map(_._1),
                    )}" +
                    s"&hideDiffColumns=${params.state.urlState.hideDiffColumns}" +
                    s"&hideWeightColumns=${params.state.urlState.hideWeightColumns}" +
                    s"&hideAgeColumns=${params.state.urlState.hideAgeColumns}" +
                    s"&hideTotalWeightColumns=${params.state.urlState.hideTotalWeightColumns}" +
                    s"&hideAllAnimalsColumns=${params.state.urlState.hideAllAnimalsColumns}",
                  dbTagDisabled = true,
                ),
              )
            case _ => Seq(
                HoverMenu2.exportWithSubMenu("", disabled = true),
                HoverMenu2.exportWithSubMenuLabeled(
                  label = M("js.stocktaking.export.tablesByAnimals.title"),
                  className = "herd-animal-export",
                  awesomeIcon = "awesome-icon-file-text",
                  urlPrefix = "",
                  dbTagDisabled = true,
                  disabled = true,
                ),
              )
          }): _*,
        ),
        grid = stateEV.value.gridModelPot match {
          case Ready(gridModel) => HerdOverviewGridCmp(
              params.state.farmType,
              params.state.effectiveAnimalTypes,
              gridModel,
              params.props,
              params.state.showAge,
              stateEV.value.urlState.detailAnimalsButtons.toMap,
              params.state.urlState.startDateOpt,
              params.state.urlState.endDateOpt.getOrElse(LocalDate.now()),
              stateEV.zoomL(State.detailOpt),
              params.state.urlState.hideDiffColumns,
              params.state.urlState.hideWeightColumns,
              params.state.urlState.hideAgeColumns,
              params.state.urlState.hideTotalWeightColumns,
              params.state.urlState.hideAllAnimalsColumns,
            )()
          case Pending(_) => LoadingOverlay()
          case _          => InfoOverlay(M("js.label.grid.fillform.top"))
        },
        detail = stateEV.value.detailOpt.map { detailData =>
          val locationRemDep = params.props.dependency(CfDependency.Locations)

          val colspan =
            2 + (if (params.state.urlState.hideWeightColumns) 0 else 1) + (if (params.state.urlState.hideTotalWeightColumns) 0 else 1)

          sealed trait DailyExportType

          object DailyExportType {
            case object AllInhabitants extends DailyExportType
            case class Animal(at: AnimalType) extends DailyExportType
          }

          def dailyChangesExport(exportType: DailyExportType, url: String) = {
            val (label, className) = exportType match {
              case DailyExportType.AllInhabitants => (M("js.label.all.inhabitants"), "all-inhabitants-export")
              case DailyExportType.Animal(at)     => (M(at.msgKeyPlural), s"animal-export ${at.id}")
            }

            <.tr(
              <.th(
                ^.className := "heading",
                ^.colSpan := colspan,
                <.a(
                  ^.href := url,
                  ^.target := "_blank",
                  ^.cls := className,
                  <.i(
                    ^.className := "menu-icon awesome-icon-table",
                  ),
                  s" $label",
                ),
              ),
            )
          }

          val titlesLine = {
            val cellStyle = TagMod(
              ^.color := "inherit",
              ^.className := "heading",
              ^.lineHeight := "unset",
            )

            <.tr(
              ^.cls := "detail-column-headers",
              <.th(
                cellStyle,
                "",
              ),
              <.td(
                cellStyle,
                M("js.label.count"),
              ),
              <.td(
                cellStyle,
                M("js.label.weight"),
              ).when(!params.state.urlState.hideWeightColumns),
              <.td(
                cellStyle,
                M("js.herd.totalWeight"),
              ).when(!params.state.urlState.hideTotalWeightColumns),
            )
          }

          def sumLine(
            label: String,
            count: Int,
            weightOpt: Option[BigDecimal],
            totalWeightOpt: Option[BigDecimal],
            additionLineTags: Seq[TagMod] = Seq.empty,
          ) = {
            <.tr(
              additionLineTags.toTagMod,
              <.th(
                ^.color := "inherit",
                ^.className := "heading",
                label,
              ),
              <.td(
                ^.color := "inherit",
                ^.className := "heading",
                params.props.formatNumber(count),
              ),
              <.td(
                ^.color := "inherit",
                ^.className := "heading",
                weightOpt match {
                  case Some(weight) => params.props.formatNumber(weight, 2)
                  case None         => ""
                },
              ).when(!params.state.urlState.hideWeightColumns),
              <.td(
                ^.color := "inherit",
                ^.className := "heading",
                totalWeightOpt match {
                  case Some(totalWeight) => params.props.formatNumber(totalWeight, 0)
                  case None              => ""
                },
              ).when(!params.state.urlState.hideTotalWeightColumns),
            )
          }

          val emptyLine = {
            <.tr(
              <.td(
                ^.colSpan := colspan,
                ^.backgroundColor := "transparent",
                ^.borderLeftColor := "transparent",
                ^.borderRightColor := "transparent",
              ),
            )
          }

          def locationRow(
            locationName: String,
            count: Int,
            countAbs: Int,
            totalWeightOpt: Option[BigDecimal],
            totalWeightAbsOpt: Option[BigDecimal],
            isTopLocation: Boolean = false,
            urlOpt: Option[String] = None,
          ) = {
            <.tr(
              (^.fontWeight := "bold").when(isTopLocation),
              <.td(
                ^.className := "left-align",
                ^.borderRight := "none",
                ^.overflowWrap := "anywhere",
                ^.lineHeight := "unset",
                ^.paddingTop := "3px",
                ^.paddingBottom := "3px",
                (^.paddingLeft := "20px").when(!isTopLocation),
                (^.fontSize := "80%").when(!isTopLocation),
                urlOpt match {
                  case Some(url) => <.a(
                      ^.href := url,
                      locationName,
                    )

                  case None => locationName
                },
              ),
              <.td(
                ^.borderLeft := "none",
                (^.paddingRight := "20px").when(!isTopLocation),
                (^.fontSize := "80%").when(!isTopLocation),
                params.props.formatNumber(count),
              ),
              <.td(
                ^.borderLeft := "none",
                (^.paddingRight := "20px").when(!isTopLocation),
                (^.fontSize := "80%").when(!isTopLocation),
                totalWeightAbsOpt match {
                  case None => ""
                  case Some(weightAbs) =>
                    val avgWeight = if (countAbs == 0) BigDecimal(0) else (weightAbs / countAbs)
                    params.props.formatNumber(avgWeight, 2)
                },
              ).when(!params.state.urlState.hideWeightColumns),
              <.td(
                ^.borderLeft := "none",
                (^.paddingRight := "20px").when(!isTopLocation),
                (^.fontSize := "80%").when(!isTopLocation),
                totalWeightOpt match {
                  case Some(weight) => params.props.formatNumber(weight, 0)
                  case None         => ""
                },
              ).when(!params.state.urlState.hideTotalWeightColumns),
            )
          }

          def linkToOperationPage(
            operation: HerdOperation,
            animalType: AnimalType,
            locIdOpt: Option[LocationId],
            startDateOpt: Option[LocalDate],
            endDate: LocalDate,
          ): String = {

            def oldPageUrl(
              baseUrl: String,
              columnsObjName: String,
              locationColumnName: String = "locationId",
              inhabitantTypeColumnName: String = "inhabitantTypeCode",
              openDetail: Boolean = false,
              additionalFilterOpt: Option[String] = None,
            ) = {
              val dateFilter =
                startDateOpt.map(_.format(DateTimeFormatter.ISO_DATE) + "..").getOrElse("") + endDate.format(DateTimeFormatter.ISO_DATE)
              val locationFilter = locIdOpt.map(locId => s""","$locationColumnName":"${locationRemDep.locationName(locId)}"""").getOrElse("")

              val detailOpenParam = {
                if (openDetail) "&details=1"
                else ""
              }

//              s"""#/$baseUrl?grids={"$columnsObjName":{"filter":{"actorDate":{"p":"${dateFilter}"}$locationFilter${additionalFilterOpt.getOrElse("")},"$inhabitantTypeColumnName":"${animalType.dbCode}"}}}${if (openDetail) "&details=1" else ""}"""

              s"#/$baseUrl?grids={" +
                s""""$columnsObjName":{""" +
                "\"filter\":{" +
                "\"actorDate\":{" +
                s""""p":"$dateFilter"""" +
                "}" +
                locationFilter +
                additionalFilterOpt.getOrElse("") +
                s""","$inhabitantTypeColumnName":"${animalType.dbCode}"""" +
                "}" +
                "}" +
                "}" + detailOpenParam
            }

            def newPageUrl(jsObj: js.Any): String =
              URIUtils.encodeURIComponent(JSURL2.stringify(jsObj))

            def columnStateDateFilter[G <: EnumEntry](col: G with GridColumnBase with FilterModelType[Any])(implicit
              dateFilter: FilterRange[DateEntry],
            ): ColumnsState[G] = {
              ColumnsState[G](filteringAndSorting = {
                FilteringAndSorting(
                  filters = GridFilters[G](Map(col -> Seq(dateFilter))),
                  sortings = Seq(GridSorting(col)),
                )
              })
            }

            def basicDateFilter(): FilterRange[DateEntry.Day] = {
              startDateOpt match {
                case Some(startDate) => FilterRange.Range(DateEntry.Day(startDate), DateEntry.Day(endDate))
                case None            => FilterRange.Until(DateEntry.Day(endDate))
              }
            }

            operation match {
              case Bought => oldPageUrl(
                  baseUrl = "transfer/in",
                  columnsObjName = "BuyingTransferColumns",
                  locationColumnName = "qf_locationId",
                  inhabitantTypeColumnName = "qf_locationInhabitantTypeCode",
                  openDetail = true,
                )

              case Moved =>
                oldPageUrl(baseUrl = "transfer/local", columnsObjName = "LocalTransferColumns", locationColumnName = "", openDetail = true)
              case MovedTo => oldPageUrl(
                  baseUrl = "transfer/local",
                  columnsObjName = "LocalTransferColumns",
                  locationColumnName = "qf_locationIdTo",
                  inhabitantTypeColumnName = "qf_inhabitantTo",
                  openDetail = true,
                )
              case MovedFrom =>
                oldPageUrl(
                  baseUrl = "transfer/local",
                  columnsObjName = "LocalTransferColumns",
                  locationColumnName = "qf_locationIdFrom",
                  openDetail = true,
                )

              case MovedToIndiv => oldPageUrl(
                  baseUrl = "hop",
                  columnsObjName = "HopColumns",
                  locationColumnName = "to",
                  inhabitantTypeColumnName = "inhabitantType",
                  openDetail = true,
                )
              case MovedFromIndiv => oldPageUrl(
                  baseUrl = "hop",
                  columnsObjName = "HopColumns",
                  locationColumnName = "fromLoc",
                  inhabitantTypeColumnName = "inhabitantType",
                  openDetail = true,
                )
              case MovedToGroup => oldPageUrl(
                  baseUrl = "transfer/local",
                  columnsObjName = "LocalTransferColumns",
                  locationColumnName = "qf_locationIdTo",
                  inhabitantTypeColumnName = "qf_inhabitantTo",
                  openDetail = true,
                )
              case MovedFromGroup =>
                oldPageUrl(
                  baseUrl = "transfer/local",
                  columnsObjName = "LocalTransferColumns",
                  locationColumnName = "qf_locationIdFrom",
                  openDetail = true,
                )

              case Born =>
                oldPageUrl(baseUrl = "farrow", columnsObjName = "FarrowEventColumns", locationColumnName = "serving_farrowLocationId")
              case Farrowed1st => oldPageUrl(
                  baseUrl = "farrow",
                  columnsObjName = "FarrowEventColumns",
                  locationColumnName = "serving_farrowLocationId",
                  additionalFilterOpt = Some(",\"serving_sow_parity\":\"1\",\"state\":\"2\""),
                )
              case Dead        => oldPageUrl(baseUrl = "dead", columnsObjName = "DeadColumns")
              case Inseminated => oldPageUrl(baseUrl = "serving", columnsObjName = "ServingEventColumns")
              case WeanedFrom  => oldPageUrl(baseUrl = "weaning", columnsObjName = "WeaningColumns", locationColumnName = "fromLocationId")
              case WeanedTo    => oldPageUrl(baseUrl = "weaning", columnsObjName = "WeaningColumns")

              case Stocktaking =>
                implicit val dateFilter: FilterRange[DateEntry.Day] = basicDateFilter()

                "#/stocktaking2?state=" +
                  newPageUrl(writeToJs(
                    Stocktaking2Page.UrlState(
                      params.state.effectiveAnimalTypes.map(at => (at, true)),
                    ).copy(view = {
                      Stocktaking2Page.View(columnsState =
                        columnStateDateFilter(Stocktaking2PageGrid.Effectivedate),
                      )
                    }),
                  ))

              case BatchMoved =>
                val dateFilterOpt = startDateOpt.map(startDate => FilterRange.Until(DateEntry.Day(startDate)))

                val view = dateFilterOpt.map(implicit dateFilter => {
                  BatchesPage.View().copy(
                    locationsColumnState = columnStateDateFilter(BatchLocationsGrid.EntryDate),
                    localTransferColumnState = columnStateDateFilter(LocalTransfersGrid.ActorDate),
                    deadColumnState = columnStateDateFilter(DeadGrid.ActorDate),
                    batchEventColumnState = columnStateDateFilter(BatchEventGrid.ActorDate),
                    buyingColumnState = columnStateDateFilter(BuyingGrid.ActorDate),
                    sellingColumnState = columnStateDateFilter(SellingGrid.ActorDate),
                  )
                }).getOrElse(BatchesPage.View())

                "#/batches?state=" +
                  newPageUrl(writeToJs(
                    BatchesPage.UrlState(
                      selectedRowIndexOpt = Some(0),
                      selectedViewIdOpt = None,
                      view = view.copy(
                        selectedSection = BatchesPage.Section.Overview,
                        columnsState = ColumnsState[BatchesGridColumn](filteringAndSorting = {
                          FilteringAndSorting(
                            filters =
                              BatchesGrid().filters(BatchesGridColumn.Name -> locIdOpt.map(locationRemDep.locationName).getOrElse("")),
                            sortings = Seq(),
                          )
                        }),
                      ),
                    ),
                  ))

              case Sold =>
                implicit val dateFilter: FilterRange[DateEntry.Day] = basicDateFilter()

                val view = ViewUrlState[AdditionalViewData, SellingScreenGrid](
                  View[AdditionalViewData, SellingScreenGrid](
                    columnsState = columnStateDateFilter(SellingScreenGrid.ShippingDate),
                    additionalView = AdditionalViewData(),
                  ),
                  None,
                )

                type ModelValues = Map[SellingFilterForm with ModelType[Any], Any]
                implicit val modelMapRW: pushka.RW[ModelValues] = {
                  implicit val formEnumRW: pushka.RW[SellingFilterForm] = SellingFilterForm.enumRW[SellingFilterForm]
                  ModelType.modelMapRW[SellingFilterForm]
                }
                implicit val pageContext: CfContext = params.props
                val formFilters: ModelValues = Seq(
                  Some(
                    SellingFilterForm.InhabitantType -> (animalType match {
                      case AnimalType.Maiden => AnimalType.GiltPigQuality.dbCode
                      case at @ _            => at.dbCode
                    }),
                  ),
                  locIdOpt.map(locId => SellingFilterForm.Location -> Seq(FilterRange.Exactly[Long](locId))),
                ).flatten.toMap

                val urlState = newPageUrl(writeToJs(SellingPage3.UrlState(selectedRowIndexOpt = Some(0))))

                s"#/sellingscreen?state=$urlState&" +
                  SellingScreenGrid.formName + "=" + newPageUrl(writeToJs(view)) + "~&" +
                  SellingFilterForm.formName + "=" + newPageUrl(writeToJs(formFilters))

              case Start => ""

            }
          }

          def operationTable(
            operationType: HerdOperation,
            animalType: AnimalType,
            totalCount: Int,
            totalCountAbs: Int,
            totalWeightOpt: Option[BigDecimal],
            totalWeightAbsOpt: Option[BigDecimal],
            content: TagMod,
          ): TagMod = {
            <.tbody(
              <.tr(
                <.th(
                  ^.textAlign := "left",
                  <.a(
                    ^.href := linkToOperationPage(
                      operationType,
                      animalType,
                      None,
                      detailData.startDateOpt,
                      detailData.endDate,
                    ),
                    M(HerdOperation.msgKey(operationType)),
                  ),
                ),
                <.th(
                  ^.textAlign := "right",
                  params.props.formatNumber(totalCount),
                ),
                <.th(
                  ^.textAlign := "right",
                  totalWeightAbsOpt match {
                    case None => ""
                    case Some(totalWeightAbs) =>
                      val avgWeight = if (totalCountAbs == 0) BigDecimal(0) else (totalWeightAbs / totalCountAbs)
                      params.props.formatNumber(avgWeight, 2)
                  },
                ).when(!params.state.urlState.hideWeightColumns),
                <.th(
                  ^.textAlign := "right",
                  totalWeightOpt match {
                    case Some(totalWeight) => params.props.formatNumber(totalWeight, 0)
                    case None              => ""
                  },
                ).when(!params.state.urlState.hideTotalWeightColumns),
              ),
              content,
              emptyLine,
            )
          }

          def locationSubTable(
            operationType: HerdOperation,
            animalType: AnimalType,
            subTableData: DetailOperationSubTableData,
          ): TagMod = {

            val nameFn = (locationName: String, count: Int) => {
              operationType match {
                // those 3 types have also other_loc_id which will be translated to intended table rows
                case BatchMoved | Moved =>
                  if (count >= 0) M("js.delta.from", locationName)
                  else M("js.delta.to", locationName)
                case MovedTo | MovedToIndiv | MovedToGroup | WeanedTo         => M("js.delta.from", locationName)
                case MovedFrom | MovedFromIndiv | MovedFromGroup | WeanedFrom => M("js.delta.to", locationName)
                case _                                                        => ""
              }
            }

            val tableContent = operationType match {
              case Born | Bought | Dead | Farrowed1st | Inseminated | Sold | Start | Stocktaking => EmptyVdom

              // other types have also other_loc_id which will be translated to intended table rows
              case _ =>
                subTableData.events.map { oe =>
                  val locationName = oe.otherLocIdOpt
                    .map(locationRemDep.locationName)
                    .map(locationName => nameFn(locationName, oe.count))
                    .getOrElse("")
                  val otherType = oe.otherInhabitantType.map(t => " (" + M(s"js.label.inhab.plural.${t.toLowerCase}") + ")").getOrElse("")
                  locationRow(
                    locationName = locationName + otherType,
                    count = oe.count,
                    countAbs = oe.count.abs,
                    totalWeightOpt = oe.totalWeightOpt,
                    totalWeightAbsOpt = oe.totalWeightOpt.map(_.abs),
                  )
                }.toTagMod
            }

            TagMod(
              locationRow(
                locationName = subTableData.locationName,
                count = subTableData.events.map(_.count).sum,
                countAbs = subTableData.events.map(_.count.abs).sum,
                totalWeightOpt = subTableData.events.flatMap(_.totalWeightOpt) match {
                  case Nil  => None
                  case list => Some(list.sum)
                },
                totalWeightAbsOpt = subTableData.events.flatMap(_.totalWeightOpt).map(_.abs) match {
                  case Nil  => None
                  case list => Some(list.sum)
                },
                isTopLocation = true,
                urlOpt = Some(linkToOperationPage(
                  operationType,
                  animalType,
                  Some(subTableData.locationId),
                  detailData.startDateOpt,
                  detailData.endDate,
                )),
              ),
              tableContent,
            )
          }

          case class DetailOperationSubTableData(
            locationId: LocationId,
            locationName: String,
            events: List[HerdOverviewDetailAnimalOperationData],
          )

          class LocationOrdering extends Ordering[DetailOperationSubTableData] {
            val nameOrdering: Ordering[DetailOperationSubTableData] =
              CaseSensitiveAlphaNumOrdering.on[DetailOperationSubTableData](_.locationName)
            override def compare(x: DetailOperationSubTableData, y: DetailOperationSubTableData): Int = nameOrdering.compare(x, y)
          }
          object LocationOrdering extends LocationOrdering()

          def operationEventsRows(
            operationType: HerdOperation,
            animalType: AnimalType,
            events: List[HerdOverviewDetailAnimalOperationData],
          ): TagMod = {
            val content = events
              .groupBy(_.locId)
              .toSeq
              .map(locationEventsMap => {
                DetailOperationSubTableData(
                  locationEventsMap._1,
                  locationRemDep.locationName(locationEventsMap._1),
                  locationEventsMap._2,
                )
              })
              .sorted(LocationOrdering)
              .map(locationEventsMap => {
                locationSubTable(
                  operationType,
                  animalType,
                  locationEventsMap,
                )
              }).toTagMod

            operationTable(
              operationType = operationType,
              animalType = animalType,
              totalCount = events.map(_.count).sum,
              totalCountAbs = events.map(_.count.abs).sum,
              totalWeightOpt = events.flatMap(_.totalWeightOpt) match {
                case Nil  => None
                case list => Some(list.sum)
              },
              totalWeightAbsOpt = events.flatMap(_.totalWeightOpt).map(_.abs) match {
                case Nil  => None
                case list => Some(list.sum)
              },
              content = content,
            )
          }

          val herdOperationsTables: TagMod = {
            detailData.loc.rowType match {

              case RowType.TotalSum | RowType.AggregatedRow =>
                detailData.loc.leafChildren
                  .flatMap(_.dataOpt)
                  .flatMap(_.detailData.dataValues.get(detailData.at))
                  .flatMap(_.data.toSeq)
                  .groupBy(_._1)
                  .mapValues(_.flatMap(_._2))
                  .filter(_._2.nonEmpty)
                  .toSeq
                  .sortBy(_._1)
                  .map(o => operationEventsRows(o._1, detailData.at, o._2.toList))
                  .toTagMod

              case RowType.DataRow =>
                detailData.loc.dataOpt.flatMap(data => data.detailData.dataValues.get(detailData.at)).map(animalData => {
                  animalData.data
                    .filter(_._2.nonEmpty)
                    .toSeq
                    .sortBy(_._1)
                    .map(o => operationEventsRows(o._1, detailData.at, o._2))
                }).getOrElse(Seq(EmptyVdom)).toTagMod
            }
          }

          val changesByDates: Int = detailData.endCount - detailData.startCountOpt.getOrElse(0)
          val changesByEvents: Int = {
            detailData.loc.rowType match {
              case RowType.TotalSum | RowType.AggregatedRow =>
                detailData.loc.leafChildren
                  .flatMap(_.dataOpt)
                  .flatMap(_.detailData.dataValues.get(detailData.at))
                  .flatMap(_.data.values)
                  .flatten
                  .map(_.count)
                  .sum

              case RowType.DataRow =>
                detailData.loc.dataOpt
                  .flatMap(data => data.detailData.dataValues.get(detailData.at))
                  .map(animalData => animalData.data.values.flatten.map(_.count).sum)
                  .getOrElse(0)
            }
          }

          val exportLocationIds: Seq[Long] = {
            detailData.loc.rowType match {
              case RowType.TotalSum => Seq(-1)
              case RowType.DataRow  => Seq(detailData.loc.locIdOpt.getOrElse(-1))
              case RowType.AggregatedRow =>
                detailData.loc.locIdOpt match {
                  case None => Seq(-1)
                  case Some(locId) =>
                    val batchIds = detailData.loc.leafChildren
                      .filter(loc => loc.uniqueIdOpt != loc.locIdOpt)
                      .flatMap(_.locIdOpt)
                      .toSet

                    Seq(locId) ++ batchIds
                }

            }
          }

          <.div(
            ^.className := s"row herd-detail at_${detailData.at.dbCode} locId_${detailData.loc.locIdOpt.getOrElse(-1).toString} duplicate_${detailData.loc.duplicate}",
            ^.padding := "10px",
            <.fieldset(
              <.legend(
                ^.display := "flex",
                ^.alignItems := "center",
                detailData.loc.locationNumber,
                <.span(
                  ^.fontWeight := "normal",
                  s"\u00A0-\u00A0${M(detailData.at.msgKeyPlural)}",
                ),
                <.i(
                  ^.className := s"herd-info-icons ${detailData.at.iconName}",
                  ^.fontSize := "1.5rem",
                  ^.marginLeft := "10px",
                ),
              ),
              <.table(
                ^.className := "gridtable herd-info",
                ^.width := "100%",
                ^.maxWidth := "unset",
                <.thead(
                  dailyChangesExport(
                    DailyExportType.Animal(detailData.at),
                    s"/export/herd/dailyChanges" +
                      s"?startDateOptStr=${CloudFarmsOps.writeToParam(detailData.startDateOpt)}" +
                      s"&endDateStr=${CloudFarmsOps.writeToParam(detailData.endDate)}" +
                      s"&locationIdsStr=${CloudFarmsOps.writeToParam(exportLocationIds)}" +
                      s"&selectedAnimalTypeOptStr=${CloudFarmsOps.writeToParam(Some(detailData.at).asInstanceOf[Option[AnimalType]])}" +
                      s"&hideWeightColumns=${CloudFarmsOps.writeToParam(params.state.urlState.hideWeightColumns)}" +
                      s"&hideTotalWeightColumns=${CloudFarmsOps.writeToParam(params.state.urlState.hideTotalWeightColumns)}" +
                      s"&${SharedConstants.ExportTargetParam}=${ExportTarget.Excel.entryName}",
                  ),
                  dailyChangesExport(
                    DailyExportType.AllInhabitants,
                    s"/export/herd/dailyChanges" +
                      s"?startDateOptStr=${CloudFarmsOps.writeToParam(detailData.startDateOpt)}" +
                      s"&endDateStr=${CloudFarmsOps.writeToParam(detailData.endDate)}" +
                      s"&locationIdsStr=${CloudFarmsOps.writeToParam(exportLocationIds)}" +
                      s"&selectedAnimalTypeOptStr=${CloudFarmsOps.writeToParam(Option.empty[AnimalType])}" +
                      s"&hideWeightColumns=${CloudFarmsOps.writeToParam(params.state.urlState.hideWeightColumns)}" +
                      s"&hideTotalWeightColumns=${CloudFarmsOps.writeToParam(params.state.urlState.hideTotalWeightColumns)}" +
                      s"&${SharedConstants.ExportTargetParam}=${ExportTarget.Excel.entryName}",
                  ),
                  titlesLine,
                  params.state.urlState.startDateOpt.map(startDate => {
                    sumLine(
                      label = params.props.formatLocalDate(startDate),
                      count = detailData.startCountOpt.getOrElse(0),
                      weightOpt = detailData.startWeightOpt,
                      totalWeightOpt = detailData.startTotalWeightOpt,
                      additionLineTags = Seq(^.cls := "start-date-values"),
                    )
                  }).getOrElse(EmptyVdom),
                  emptyLine,
                ),
                <.tfoot(
                  params.state.urlState.startDateOpt.map(_ => {
                    sumLine(
                      label = s"${M("js.label.total.change")}:",
                      count = changesByDates,
                      weightOpt = (detailData.startWeightOpt, detailData.endWeightOpt) match {
                        case (Some(startWeight), Some(endWeight)) => Some(endWeight - startWeight)
                        case (None, Some(endWeight))              => Some(endWeight)
                        case _                                    => None
                      },
                      totalWeightOpt = (detailData.startTotalWeightOpt, detailData.endTotalWeightOpt) match {
                        case (Some(startWeight), Some(endWeight)) => Some(endWeight - startWeight)
                        case (None, Some(endWeight))              => Some(endWeight)
                        case _                                    => None
                      },
                      additionLineTags = Seq(^.cls := "total-change-values"),
                    )
                  }).getOrElse(EmptyVdom),
                  if (detailData.startCountOpt.isDefined && changesByEvents != changesByDates) {
                    sumLine(
                      label = s"${M("js.label.difference")}:",
                      count = Math.abs(changesByDates - changesByEvents),
                      weightOpt = None,
                      totalWeightOpt = None,
                      additionLineTags = Seq(^.color := "red", ^.cls := "diff-values"),
                    )
                  } else {
                    EmptyVdom
                  },
                  params.state.urlState.endDateOpt.map(endDate => {
                    sumLine(
                      label = params.props.formatLocalDate(endDate),
                      count = detailData.endCount,
                      weightOpt = detailData.endWeightOpt,
                      totalWeightOpt = detailData.endTotalWeightOpt,
                      additionLineTags = Seq(^.cls := "end-date-values"),
                    )
                  }).getOrElse(EmptyVdom),
                ),
                herdOperationsTables,
              ),
            ),
          )
        }.getOrElse(<.div()),
        showDetailEV = stateEV.zoomL(State.urlState).zoomL(UrlState.showDetail),
        detailWidth = 3,
      ),
    )
  }
  def loadWeanedPigletsSetting(props: Props): Boolean =
    props.angularAppServices.settings.getOrg[Boolean]("stocktaking_weaned_piglets").getOrElse(false)

  PageComponentBuilder("HerdOverviewPage", Set(CfDependency.Locations, CfDependency.UseAgeInGroups))
    .initialStateFromProps { props =>
      val addWeanedPiglets = loadWeanedPigletsSetting(props)
      val effectiveAnimalTypes = AnimalType.stocktakingValues(addWeanedPiglets)

      State(
        showAge = props.dependency(CfDependency.UseAgeInGroups),
        dateRangeForm = FormModel.fromModelValues2(HerdOverviewDateRangeForm, props)(
          HerdOverviewDateRangeForm.StartDate -> None,
          HerdOverviewDateRangeForm.EndDate -> Some(LocalDate.now()),
        ),
        effectiveAnimalTypes = effectiveAnimalTypes,
        addWeanedPiglets = addWeanedPiglets,
        farmType = props.getFarmType,
        urlState = UrlState(effectiveAnimalTypes.map(at => (at, true))), // by default all animals are selected)
      )
    }
    .bindUrlState(State.urlState)
    .componentWillMount(params => {
      Callback {
        setDefaultFormData(params)
        loadEffectiveAnimalTypes(params)
      }
    })
    .render(render(_))
    .build()
}
