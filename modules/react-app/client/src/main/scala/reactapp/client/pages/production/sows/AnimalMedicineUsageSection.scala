package reactapp.client.pages.production.sows

import autowire._
import reactapp.client.common.CloudFarmsOps._
import reactapp.client.common.component.{ AsyncLoader, useViews }
import reactapp.client.common.{ AutowireClient, CfContext }
import reactapp.client.facades.{ AngularAppServices, M }
import reactapp.shared.common.grid.CfCellIcon
import reactapp.shared.medicine.MedicineUsageApi.{ AnimalMedicineUsageAD => AD }
import reactapp.shared.medicine.{ AnimalMedicineUsageGrid => MGrid, MedicineUsageApi => MApi }
import sjsgrid.client.grid.hook.GridHook.{ GridHandle, GridParams, useGrid }
import sjsgrid.client.grid.hook.GridRowModelType
import sjsgrid.client.grid.hook.ScalaHooks.{ useDelay, useEffect, useStateEV }
import sjsgrid.client.grid.{ GridModel, GridRow }
import sjsgrid.flash.client.components.SubGrid
import sjsgrid.shared.common.PushkaPicklers._
import sjsgrid.shared.common.pot.Pot
import sjsgrid.shared.grid.dto.RowModel
import slinky.core.facade.Fragment
import slinky.core.{ FunctionalComponent, ReactComponentClass }
import slinky.web.html_<^.{ <, ^ }

import org.scalajs.macrotaskexecutor.MacrotaskExecutor.Implicits._
import scala.scalajs.js

object AnimalMedicineUsageSection {

  @js.native
  trait JsProps extends js.Object {
    var ng: AngularAppServices
    var isVisible: Boolean
    var isInRaceCondition: Boolean
    var dbAnimalId: String // this is a Long that is passed as String due to JS compatibility problems (no implicit casting from js number to Long)
  }

  case class Props(cfContext: CfContext, animalId: Long)

  type MGridRow = GridRow[MGrid, AD]
  type MRowModel = RowModel[MGrid]

  keepPushkaPicklersImport

  val component: ReactComponentClass[JsProps] = ReactComponentClass.functionalComponentToClass(FunctionalComponent[JsProps] { jsProps =>
    lazy val gridComponent = FunctionalComponent[Props] { props =>
      implicit val context: CfContext = props.cfContext
      val animalId = props.animalId

      val gridPotEV = useStateEV[Pot[GridModel[MGrid, AD]]](Pot.empty)
      val viewHandle = useViews(MGrid)
      val gridHandle: GridHandle[MGrid, AD] = {
        val loadGridDataFn = () => {
          AutowireClient[MApi]
            .listForAnimal(animalId, viewHandle.filteringAndSorting)
            .call()
            .map(_.map(adToRow => adToRow._2.toGridRow(adToRow._1, context, MGrid)))
            .map(GridModel.fromRows)
            .toPot(gridPotEV)
        }

        useGrid(
          GridParams[MGrid, AD](
            gridDefinition = MGrid,
            gridModelEV = gridPotEV.toGridModelEV,
            rowModel = GridRowModelType.InMemory(Some(loadGridDataFn)),
            filteringAndSortingEV = viewHandle.filteringAndSortingEV,
            columnPositionsEV = viewHandle.columnPositionsEV,
            getPermanentId = (gridRow: MGridRow) => Some(gridRow.additionalData.toString),
            overlayNoRowsTemplate =
              s"""<div class="aggrid-empty-overlay">${CfCellIcon.InfoHtml} ${M("js.label.animal.medicineusage.norows")}</div>""",
          ),
        )
      }

      useEffect()(() => gridHandle.reloadGridData())

      // Prevent label font shrinking in old sow screen. Not needed when the screen is rewritten
      val cssLabelFontFix = cssClass("OverrideLabelFontSize", "label {font-size: 16px !important;}")

      <.div(
        ^.className := cssLabelFontFix,
        SubGrid(
          name = M("js.label.medicineusage"),
          height = SubGrid.Height.Autosize(800),
          gridHandle = gridHandle,
          hoverMenu = { collapsed =>
            gridHandle.hoverMenu(
              collapsed = collapsed,
              viewsHandle = viewHandle,
            )
          },
          minHeightPxOpt = Some(200),
        ),
      )
    }

    /** Display the component with delay to avoid race condition on screen width calculation */
    lazy val delayedGridComponent = FunctionalComponent[Props] { props =>
      val isAfterDelay = useDelay(true, false, 0)
      if (isAfterDelay) gridComponent(props)
      else Fragment()
    }

    lazy val cfContext = CfContext.prepare(MGrid.dependencies, jsProps.ng)

    AsyncLoader(() => cfContext)(
      (cfContext, _) => {
        lazy val propsOpt = Option(jsProps.dbAnimalId)
          .filterNot(_ == "")
          .map(strId => Props(cfContext, strId.toLong))
        val showComponent = jsProps.isVisible && propsOpt.isDefined

        if (showComponent && jsProps.isInRaceCondition)
          delayedGridComponent(propsOpt.get)
        else if (showComponent)
          gridComponent(propsOpt.get)
        else
          Fragment()
      },
      s"medicine-usage-sow-${jsProps.dbAnimalId}",
    )
  })
  js.Dynamic.global.window.updateDynamic("AnimalMedicineUsageSection")(component)
}
