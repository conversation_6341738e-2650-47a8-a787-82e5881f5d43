package reactapp.client.pages.danishcrown

import autowire._
import japgolly.scalajs.react.vdom.html_<^._
import monocle.macros.Lenses
import pushka.annotation.pushka
import reactapp.client.common._
import reactapp.client.common.component._
import reactapp.client.facades.{ AngularAppServices, M }
import reactapp.shared.common.grid.CfDependency
import reactapp.shared.danishcrown._
import org.scalajs.macrotaskexecutor.MacrotaskExecutor.Implicits._
import reactapp.client.common.CloudFarmsOps._
import reactapp.client.common.grid.views.ViewsState
import reactapp.shared.auth.Authorization
import sjsgrid.client.grid.params.GridRenderParams
import reactapp.shared.view.ViewId
import sjsgrid.client.grid.{ GridBuilder, GridModel }
import sjsgrid.client.grid.params.RowParams
import sjsgrid.shared.common.pot.{ Empty, Pot }
import sjsgrid.shared.grid.column.ColumnsState

/**
  * Created by gregor on 9/7/16.
  */
object DanishCrownPage {
  case class Props(cfContext: CfContext, danishCrownEnums: DanishCrownEnums)

  @pushka
  @Lenses
  case class View(columnsState: ColumnsState[DanishCrownGrid] = DanishCrownGrid.columnsState())

  @pushka
  @Lenses
  case class UrlState(maybeSelectedRowIndex: Option[Int] = None, view: View = View(), selectedViewIdOpt: Option[ViewId] = None)

  @Lenses
  case class State(
    userName: String = "",
    password: String = "",
    fromBeginning: Boolean = false,
    isShowingMore: Boolean = false,
    downloadState: Pot[DanishCrownScreenApi.FetchResponse] = Empty,
    urlState: UrlState = UrlState(),
    viewsState: ViewsState[View],
    gridModel: GridModel[DanishCrownGrid, Long] = DanishCrownGrid.pendingModel,
  ) {}

  GridBuilder[AngularAppServices, DanishCrownGrid]("DanishCrownPage", DanishCrownGrid)
    .asyncProps(ng => {
      for {
        cfContext <- CfContext.prepare(DanishCrownGrid.dependencies, ng)
        danishCrownEnums <- AutowireClient[DanishCrownScreenApi].getEnums().call()
      } yield Props(cfContext, danishCrownEnums)
    })(_.cfContext)
    .initialStateFromProps(props => State(viewsState = ViewsState(props.cfContext.getAvailableViews[View](DanishCrownGrid.viewName))))
    .getGridModelEVFromState(State.gridModel)
    .virtualPaging(
      countGetter =
        params => AutowireClient[DanishCrownScreenApi].count2(params.state.urlState.view.columnsState.filteringAndSorting).call(),
      dataGetter = params => {
        AutowireClient[DanishCrownScreenApi].list(params).call().map(serverRows => {
          serverRows.map {
            case (id, row) =>
              row.rowModel.toGridRow(id, params.pageContext, params.gridDefinition)
          }
        })
      },
    )
    .bindUrlState(State.urlState)
    .rememberState
    .withView[View](
      currentViewL = State.urlState composeLens UrlState.view,
      viewsStateL = State.viewsState,
      selectedViewL = State.urlState composeLens UrlState.selectedViewIdOpt,
      columnsStateL = View.columnsState,
    )
    .cellRendererOverride(params => {
      case DanishCrownGrid.sygdomskodes(valueOpt) =>
        valueOpt.fold("")(illnessCodes =>
          illnessCodes.map(ic => s"<span title='${params.props.danishCrownEnums.illnesses.getOrElse(ic, ic)}'>$ic</span>").mkString(", "),
        )
    })
    .getRowClass((params: RowParams[Props, State, DanishCrownGrid, Long]) =>
      if (params.row.getValue(DanishCrownGrid.delete_date).nonEmpty) "deleted" else "",
    )
    .render { (params: GridRenderParams[Props, State, DanishCrownGrid, Long]) =>
      val downloadState = params.stateEV.value.downloadState

      PageLayout2.headerAndPositionPage(
        pageTitle = M("js.title.danishcrown"),
        position = params.indexSlashCount,
        content = ContentLayout2.fullscreenGrid(
          hoverMenu = params.hoverMenu(exportElOpt =
            Some(HoverMenu2.exportWithSubMenu(s"/export/danishcrown?columnsState=${CloudFarmsOps.writeToParam(params.state.urlState.view.columnsState)}")),
          ),
          grid = params.gridElement,
        ),
        topContent = <.div(
          <.div(
            TagMod(
              <.div(^.cls := "small-12 large-2 columns", <.label(M("js.label.username")), TextInput(params.stateEV.zoomL(State.userName))()),
              <.div(^.cls := "small-12 large-2 columns", <.label(M("js.label.password")), TextInput(params.stateEV.zoomL(State.password))()),
              <.div(
                ^.cls := "small-12 large-1 columns",
                Sswitch(params.stateEV.zoomL(State.fromBeginning), M("js.label.all"), M("js.label.recent"))(),
              ),
              <.button(
                ^.marginRight := "2ex",
                if (downloadState.isPending) ^.disabled := true
                else ^.onClick -->
                  Pot.loadCb(
                    params.stateEV.zoomL(State.downloadState),
                    AutowireClient[DanishCrownScreenApi].fetchDataFromDanishCrown(
                      params.stateEV.value.userName,
                      params.stateEV.value.password,
                      params.stateEV.value.fromBeginning,
                    ).call() map { resp =>
                      if (resp.aggregatedResponse.isRight) params.reloadDataCb.runNow()
                      resp
                    },
                  ),
                if (downloadState.isFailed || downloadState.exists(_.aggregatedResponse.isLeft)) TagMod(
                  <.i(
                    ^.cls := "awesome-icon-frown-o",
                  ),
                  if (downloadState.exists(_.goodExists)) TagMod(^.backgroundColor := "yellow", ^.color := "black") else ^.cls := "alert",
                )
                else if (downloadState.isEmptyState || downloadState.exists(_.aggregatedResponse.isRight))
                  <.i(^.cls := "awesome-icon-download")
                else <.i(^.cls := "spinning-icon awesome-icon-repeat"),
              ),
              downloadState.fold(TagMod.empty)(response => {
                TagMod(
                  response.aggregatedResponse match {
                    case Left(data)  => data
                    case Right(data) => data
                  },
                  <.a(
                    M("js.label.show_more"),
                    ^.marginLeft := "1ex",
                    ^.onClick --> params.stateEV.zoomL(State.isShowingMore).setCb(true),
                  ).when(!params.state.isShowingMore),
                )
              }),
            ).when(params.pageContext.dependency(CfDependency.CurrentPersona).isAuthorized(Authorization.DanishCrownFetchData)),
          ),
          (for {
            response <- downloadState if params.state.isShowingMore
          } yield <.div(
            <.table(
              <.thead(<.tr(<.th(M("js.danishcrown.column.leverandoer")), <.th(M("js.label.response")))),
              <.tbody(
                response.responses.toVdomArray {
                  case (supplierNumber, Right(Modifications(inserted, deleted, updated))) =>
                    <.tr(
                      <.td(^.color := "green", supplierNumber),
                      <.td(^.color := "green", M("js.label.inserted.deleted.updated", inserted, deleted, updated)),
                    )

                  case (supplierNumber, Left(err)) =>
                    <.tr(
                      <.td(^.color := "red", supplierNumber),
                      <.td(^.color := "red", err),
                    )
                },
              ),
            ),
            <.a(M("js.label.show_less"), ^.onClick --> params.stateEV.zoomL(State.isShowingMore).setCb(false)),
          )).getOrElse(EmptyVdom),
        ),
      )
    }
    .build()
}
