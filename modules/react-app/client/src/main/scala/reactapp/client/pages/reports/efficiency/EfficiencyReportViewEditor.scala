package reactapp.client.pages.reports.efficiency

import domainz.common.ResourceScope
import monocle.macros.Lenses
import reactapp.client.common.CloudFarmsOps._
import reactapp.client.pages.reports.common.ReportViewsEditorElements
import reactapp.shared.reports.common.{ CountryAndCode, IntermediateKpisServiceApi, KpiSection, ReportTypeEnum }
import reactapp.shared.reports.efficiency.EfficiencyReportPageApi.EfficiencyView
import sjsgrid.client.common.react.ExtVar
import sjsgrid.client.grid.hook.ScalaHooks.useStateEV
import sjsgrid.shared.common.pot.Pot
import slinky.core.FunctionalComponent
import slinky.core.annotations.react
import slinky.web.html_<^._

@react object EfficiencyReportViewEditor extends IntermediateKpisServiceApi {

  case class Props(
    currentViewEV: ExtVar[EfficiencyView],
    defaultReportStructure: Seq[KpiSection],
    mayBeCountriesPot: Pot[Seq[CountryAndCode]],
    reportTypes: Seq[ReportTypeEnum] = Seq.empty,
  )

  val component: FunctionalComponent[Props] = FunctionalComponent[Props] { props =>
    @Lenses
    case class State(
      viewName: String = "",
      maybeSelectedScope: Option[ResourceScope] = None,
      kpiFilter: String = "",
      countryFilter: String = "",
      showAverageKPIsOptions: Boolean = false,
    )

    val stateEV = useStateEV(State())

    val reportTypeEV = props.currentViewEV.zoomL(EfficiencyView.reportType)
    val reportType = reportTypeEV.value
    val averageKpisToShowEVSet = props.currentViewEV.zoomL(EfficiencyView.averageKpisToShow)
    averageKpisToShowEVSet.modCb(_ => {
      props.defaultReportStructure.flatMap(_.visibleKpis
        .map(_.kpi.code)
        .filter(suggestedKpis.contains)
        .toSet).toSet
    })

    <.div(
      ^.key := "efficiency-report-view-editor",
      ^.className := classes(
        "columns-view",
        cssClass(
          "efficiency-report-view-editor",
          """& {
                  display: flex;
                }
                .detail {
                  display: flex;
                  height: 400px;
                }
                """,
        ),
      ),
      <.div(
        <.div(
          // Report type selection
          ReportViewsEditorElements.reportTypeSelection(props.reportTypes, reportTypeEV).toSlinky,
          // Breed
          ReportViewsEditorElements.breedFilter(props.currentViewEV.zoomL(EfficiencyView.breed), reportType).toSlinky,
        ),
        <.div(
          // Numbers aligning checkbox
          ReportViewsEditorElements.alignNumbersToDecimalPoint(
            props.currentViewEV.zoomL(EfficiencyView.alignNumbersToDecimalPoint),
          ).toSlinky,
          // KPI filter and KPIs selection
          ReportViewsEditorElements.kpiFilter(stateEV.zoomL(State.kpiFilter)).toSlinky,
          ReportViewsEditorElements.allKpisToggle(
            props.defaultReportStructure,
            props.currentViewEV.zoomL(EfficiencyView.kpisToShow),
          ).toSlinky,
          ReportViewsEditorElements.kpiListWithDynamicKpis(
            props.defaultReportStructure,
            props.currentViewEV.zoomL(EfficiencyView.kpisToShow),
            props.currentViewEV.zoomL(EfficiencyView.dynamicKpisToShow),
            stateEV.zoomL(State.kpiFilter).value,
          ).toSlinky,
        ),
      ),
      <.div(
        ^.className := "detail",
        // Details to open/close
        ReportViewsEditorElements.toggleDetails(stateEV.zoomL(State.showAverageKPIsOptions)).toSlinky,
        // Countries selection
        if (stateEV.zoomL(State.showAverageKPIsOptions).value) {
          ReportViewsEditorElements.countries(
            props.mayBeCountriesPot,
            props.currentViewEV.zoomL(EfficiencyView.countriesToShow),
            stateEV.zoomL(State.countryFilter),
            reportType,
          ).toSlinky
        } else {
          NoContent
        },
        if (stateEV.zoomL(State.showAverageKPIsOptions).value) {
          <.div(
            // Farm types selection
            ReportViewsEditorElements.farmTypes(props.currentViewEV.zoomL(EfficiencyView.farmType), reportType).toSlinky,
            // Spread types selection
            ReportViewsEditorElements.spreadTypes(props.currentViewEV.zoomL(EfficiencyView.spreadType), reportType).toSlinky,
          )
        } else {
          NoContent
        },
      ),
    )
  }

}
