package reactapp.client.common.component

import japgolly.scalajs.react.vdom.html_<^._
import japgolly.scalajs.react.{ Callback, CallbackTo, ScalaComponent }
import japgolly.scalajs.react.vdom.VdomElement
import org.scalajs.dom
import org.scalajs.jquery._
import sjsgrid.client.common.ScalaJsOps._
import slinky.core.facade.ReactElement
import sjsgrid.client.common.react.ReactConverters._

import scala.scalajs.js

/**
  * Created by Milan Satala
  * Date: 7/12/16
  * Time: 1:03 PM
  */
object Autosize2 {

  case class AutosizeState(height: Double, updateHeightFnOpt: Option[js.Function1[dom.Event, Unit]])

  /**
    * Function that updates height of the element to max possible height
    *
    * WARNING: changes to this function should be reflected also in app.js (function resizeContent)
    *
    * @param modStateOpt function that changes the state (height) of the component
    * @param el element to which should be height set
    */
  def updateHeight(modStateOpt: Option[Double => CallbackTo[Unit]], el: dom.Element): Unit = {
    val headerPane = jQuery("#cf-heading")
    val menuPane = jQuery("#cf-header-menu")
    val topBannerPane = jQuery("#topBarBanner")
    val pageMenuPane = jQuery("#cf-header-menu .top-bar-section ul.left")
    val settingPane = jQuery("#cf-header-menu .top-bar-section ul.right")
    val smallScreenMenuPane = jQuery("#cf-header-menu ul.title-area")
    val quickFiltersPane = jQuery("#cf-quick-filters")
    val footerPane = jQuery("#cf-footer")

    val topBannerHeight = {
      if (topBannerPane.is(":visible")) topBannerPane.outerHeight()
      else 0
    }

    val topBarHeight = {
      if (smallScreenMenuPane.outerWidth() > 0) smallScreenMenuPane.outerHeight() + topBannerHeight
      else Seq(
        menuPane.outerHeight() + topBannerHeight,
        settingPane.position().dynamic.top.asDouble + settingPane.outerHeight(),
      ).max
    }

    val sumHeight = {
      headerPane.outerHeight() +
        topBarHeight +
        quickFiltersPane.outerHeight() +
        footerPane.outerHeight()
    }

    val usableHeight = jQuery(dom.window).height() - sumHeight

    jQuery(el).height(usableHeight)

    modStateOpt.map(modState => modState(usableHeight).runNow())
  }

  private val Autosize = ScalaComponent.builder[TagMod]("Autosize")
    .initialState(AutosizeState(0.0, None))
    .render_PCS((props, propsChildren, state) => {
      <.div(
        ^.id := "cf-content",
        props,
        propsChildren,
      )
    })
    .componentDidMount($ => {
      Callback {
        val updateHeightFn: js.Function1[dom.Event, Unit] =
          (e: dom.Event) => updateHeight(Some(h => $.modState(_.copy(height = h))), $.getDOMNode.asElement)
        dom.window.addEventListener("resize", updateHeightFn, true)
        updateHeight(Some(h => $.setState(AutosizeState(h, Some(updateHeightFn)))), $.getDOMNode.asElement)
      }
    })
    .componentWillUnmount($ => {
      Callback {
        $.state.updateHeightFnOpt.foreach(updateHeightFn =>
          dom.window.removeEventListener("resize", updateHeightFn, true),
        )
      }
    })
    // optimization with shouldComponentUpdate is not allowed here because child components will not re-render
    .build

  def apply(attributes: TagMod = TagMod.empty)(children: VdomElement*): VdomElement = Autosize(attributes)(children.toVdomArray)
  def apply(classNames: String)(children: VdomElement*): VdomElement = Autosize(^.cls := classNames)(children.toVdomArray)
}
