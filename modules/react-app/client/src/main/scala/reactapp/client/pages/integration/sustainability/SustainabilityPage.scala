package reactapp.client.pages.integration.sustainability

import autowire._
import monocle.macros.Lenses
import org.scalactic.{ Every, Or }
import org.scalajs.dom.Event
import org.scalajs.dom.raw.HTMLInputElement
import pushka.annotation.pushka
import reactapp.client.common.AutowireClient
import reactapp.client.common.CloudFarmsOps._
import reactapp.client.common.GridHandleOps.DeleteBtnParams
import reactapp.client.common.component.HoverMenu.{ CloseCb, Visible }
import reactapp.client.common.component.HoverMenu2.{ DeleteButton, DeleteFnParams }
import reactapp.client.common.component._
import reactapp.client.common.hook.RootFunctionalComponent
import reactapp.client.facades.{ AngularAppServices, M }
import reactapp.client.pages.integration.sustainability.{ SustainabilityDetailSection => DetailSection }
import reactapp.shared.sustainability.SustainabilityApi.{ FeedIngredient, FeedIngredientLocation, FeedingProgramParams, IngredientAD, Period, SustainabilityData, SustainabilityServerAD => ServerAD }
import reactapp.shared.sustainability.{ SustainabilityApi => SApi, SustainabilityGrid => SGrid, SustainabilityIngredientsGrid => IngredientsGrid }
import sjsgrid.client.grid.hook.GridHook.{ GridHandle, GridParams, useGrid }
import sjsgrid.client.grid.hook.ScalaHooks._
import sjsgrid.client.grid.hook.{ GridRowModelType, OnSaveConf }
import sjsgrid.client.grid.params.UnsavedRows
import sjsgrid.client.grid.{ GridModel, GridRow }
import sjsgrid.flash.client.facade.AntJs
import sjsgrid.shared.common.PushkaPicklers._
import sjsgrid.shared.common.pot.{ Pending, PendingStale, Pot, Ready }
import sjsgrid.shared.grid.dto.{ ServerProblem, ValidRow }
import slinky.core.facade.ReactElement
import slinky.core.{ ReactComponentClass, SyntheticEvent, TagElement }
import slinky.web.html_<^._

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future
import scala.scalajs.js
import scala.util.Success

object SustainabilityPage {

  val StatsIcon: String = "awesome-icon-line-chart"
  val IngredientsIcon: String = "awesome-icon-flask"
  val LocationsIcon: String = "awesome-icon-map-marker"

  type SGridRow = GridRow[SGrid, SustainabilityAD]
  type SGridHandle = GridHandle[SGrid, SustainabilityAD]
  type SGridModel = GridModel[SGrid, SustainabilityAD]
  type SDeleteFnParams = DeleteFnParams[SGrid, SustainabilityAD]
  type SValidRow = ValidRow[SGrid, SustainabilityAD]

  @pushka
  @Lenses
  case class View(
    selectedRowIndexOpt: Option[Int] = None,
    showDetail: Boolean = false,
    selectedSection: DetailSection = DetailSection.Parameters,
  )

  @pushka
  @Lenses
  case class UrlState(view: View = View())

  @Lenses
  case class State(
    gridModelPot: Pot[SGridModel] = Pot.empty,
    urlState: UrlState = UrlState(),
    ingredientsPot: Pot[Seq[FeedIngredient]] = Pot.empty,
    locationsPot: Pot[Seq[FeedIngredientLocation]] = Pot.empty,
  )

  @Lenses
  case class SustainabilityAD(
    serverDataOpt: Option[ServerAD] = None,
    weanerFeedIngredientsPot: Pot[GridModel[IngredientsGrid, IngredientAD]] = Pot.empty,
    finisherFeedIngredientsPot: Pot[GridModel[IngredientsGrid, IngredientAD]] = Pot.empty,
  ) {
    def isInDb: Boolean = serverDataOpt.flatMap(_.periodIdOpt).isDefined

    def dataOpt: Option[SustainabilityData] = serverDataOpt.flatMap(_.dataOpt)

    def hasDataLoaded: Boolean = serverDataOpt.exists(_.hasDataLoaded)

    def withId(id: Long): SustainabilityAD = copy(serverDataOpt = {
      Some(
        serverDataOpt.map(_.withId(id))
          .getOrElse(ServerAD(periodIdOpt = Some(id))),
      )
    })

    def withServerData(paramsOpt: Option[FeedingProgramParams], dataOpt: Option[SustainabilityData]): SustainabilityAD = {
      copy(serverDataOpt = {
        Some(
          serverDataOpt.map(_.withParamsAndData(paramsOpt, dataOpt))
            .getOrElse(ServerAD(paramsOpt, dataOpt)),
        )
      })
    }

    def withoutParamsAndData: SustainabilityAD = serverDataOpt match {
      case Some(serverData) if serverData.hasDataLoaded => copy(serverDataOpt = Some(serverData.withoutParamsAndData))
      case _                                            => this
    }

    def serverDataOrEmpty: ServerAD = serverDataOpt.getOrElse(ServerAD())

    def isParamsFilledAndSaved: Boolean = {
      def isFilledAndSaved(gridModel: GridModel[IngredientsGrid, IngredientAD]) =
        !gridModel.isEmpty && !gridModel.isUnsaved

      weanerFeedIngredientsPot.exists(isFilledAndSaved) &&
      finisherFeedIngredientsPot.exists(isFilledAndSaved)
    }
  }

  object SustainabilityAD {
    def apply(serverAD: ServerAD): SustainabilityAD = SustainabilityAD(Some(serverAD))
  }

  private def rowToPeriod(row: SGridRow): Period = (for {
    from <- row.getValue(SGrid.FromDate)
    to <- row.getValue(SGrid.ToDate)
  } yield Period(from, to))
    .getOrElse(sys.error("Missing from/to date")) // won't happen unless there's a bug (dates are mandatory)

  val component: ReactComponentClass[AngularAppServices] = {
    RootFunctionalComponent(SGrid.dependencies ++ IngredientsGrid.dependencies) { implicit pageContext =>
      val stateEV = usePersistentUrlStateEV(initialState = State(), urlStateLens = State.urlState)
      val gridModelPotEV = stateEV.zoomL(State.gridModelPot)
      val viewEV = stateEV.zoomL(State.urlState composeLens UrlState.view)
      val selectedSectionEV = viewEV.zoomL(View.selectedSection)
      val statsSectionEV = useStateEV[SustainabilityStatsSection](SustainabilityStatsSection.PersonYears)

      def switchSection(section: SustainabilityDetailSection): Unit = selectedSectionEV.set(section)

      useEffect()(() => if (gridModelPotEV.value.isEmptyState) gridHandle.reloadGridData())

      lazy val gridHandle: SGridHandle = {
        val loadGridDataFn = () => {
          AutowireClient[SApi].listPeriods.call
            .map(_.map(rowData => rowData.gridRow.toGridRow(SustainabilityAD(Some(rowData.additionalData)), pageContext, SGrid)))
            .map(GridModel.fromRows)
            .toPot(gridModelPotEV)
        }

        def onSave(unsavedRows: UnsavedRows[SGrid, SustainabilityAD]) = {
          val rowsToSave = unsavedRows.rowsToSave.map(row => row.toDTO(row.metadata.serverDataOrEmpty))
          val rowsOrProblemsFuture = AutowireClient[SApi].savePeriods(rowsToSave).call()
          rowsOrProblemsFuture.map(modifyGridModel(_, markCalculated = false))
        }

        useGrid(GridParams[SGrid, SustainabilityAD](
          gridDefinition = SGrid,
          gridModelEV = gridModelPotEV.toGridModelEV,
          rowModel = GridRowModelType.InMemory(Some(loadGridDataFn)),
          onSave = OnSaveConf(Some(onSave)),
          selectedRowIndexOptEV = viewEV.zoomL(View.selectedRowIndexOpt),
          createEmptyNewRow = () => SGrid.unsavedRow(SustainabilityAD()),
          getPermanentId = row => Some(rowToPeriod(row).toString),
        ))
      }

      def modifyGridModel(rowsOrProblems: Map[Int, ServerAD Or Every[ServerProblem]], markCalculated: Boolean): Unit = {
        gridModelPotEV.mod(_.map(_.saveRowsOrProblems(rowsOrProblems)((serverAD, _, row) => {
          row
            .modAdditionalData(_ => SustainabilityAD(serverAD))
            .modColumn(SGrid.Calculated)(_.setModelValue(Some(markCalculated)))
        })))
      }

      def computeSustainabilityForRows(rows: Map[Int, SGridRow]) = {
        if (rows.isEmpty) Future.unit
        else {
          // todo: this way of extracting rows to save is just a hack to quickly overcome the difficulties of working with the new version
          // of our grid. I have very limited time to bring this feature to a compilable state, I'm sorry :\
          val rowIdsToSave = rows.keySet
          val rowsToSave = gridModelPotEV.value
            .map { gridModel =>
              val unsavedRows = gridModel.unsavedRows.rowsToSave
              val unsavedRowsIntendedToSave = unsavedRows.filter(rowDTO => rowIdsToSave.contains(rowDTO.rowId))
              unsavedRowsIntendedToSave.map(row => row.toDTO(row.metadata.serverDataOrEmpty))
            }
            .getOrElse(Seq.empty)

          AutowireClient[SApi]
            .computeAndSave(rowsToSave).call()
            .map(modifyGridModel(_, markCalculated = true))
        }
      }

      def loadDataForRows(rowsMap: Map[Int, SGridRow]): Unit = {
        def addDataToAD(rowId: Int, fetchedData: ServerAD): Unit = gridModelPotEV.mod(gridModelPot => {
          gridModelPot.map(gridModel => {
            gridModel.modRow(rowId)(rowToMod =>
              rowToMod.modAdditionalData(_.withServerData(fetchedData.paramsOpt, fetchedData.dataOpt)),
            )
          })
        })

        def extractPeriodId(row: SGridRow) = row.additionalData.serverDataOpt
          .flatMap(_.periodIdOpt)
          .getOrElse(sys.error("No period id when fetching data"))

        val rowsToPeriodIds = rowsMap
          .filterNot(_._2.additionalData.hasDataLoaded)
          .view
          .mapValues(extractPeriodId)
          .toMap

        AutowireClient[SApi].fetchDataAndParams(rowsToPeriodIds).call()
          .map(_.map {
            case (rowId, fetchedData) =>
              addDataToAD(rowId, fetchedData)
          })
      }

      def detailSections: ReactElement = gridHandle.selectedRowOpt match {
        case Some(selectedRow) =>
          val computeRowFn = () => {
            val rowToSave = Map(selectedRow.rowId -> selectedRow.row)
            computeSustainabilityForRows(rowToSave)
              .andThen {
                case Success(_) =>
                  switchSection(DetailSection.ReceivedData)
              }
          }

          // When updating params, remove the old calculation info
          val onModifyUserParams = () => {
            selectedRow.rowEV.mod(_
              .setValue(SGrid.Calculated, Some(false))
              .modAdditionalData(_.withoutParamsAndData))
          }

          val ad = selectedRow.row.additionalData
          val needToLoadData = ad.isInDb && !ad.hasDataLoaded && ad.isParamsFilledAndSaved
          if (needToLoadData) {
            val selectedRowAsMap = gridHandle.selectedRowOpt.map(rowOpt => rowOpt.rowId -> rowOpt.row).toMap
            loadDataForRows(selectedRowAsMap)
          }

          val showDataSections = ad.hasDataLoaded
          if (!showDataSections) switchSection(DetailSection.Parameters)

          Sections2(
            sections = DetailSection.sectionTitles(showDataSections),
            selectedSectionEV = selectedSectionEV.bMap(_.entryName)(DetailSection.withName),
            content = selectedSectionEV.value.renderContent(selectedRow.rowEV, computeRowFn, onModifyUserParams).toScalaJSReact,
          ).toSlinky
        case None => EmptyDetail.noRowSelected
      }

      def showSpinner: ReactElement = <.i(^.className := "spinning-icon awesome-icon-repeat")

      def updateIngredientsMenuItem(): ReactElement = {
        val modalRenderer: (CloseCb, Visible) => ReactElement = (closeModal, visible) => {
          val modalParams = o(
            title = "Ingredients", // todo: add message here and below if we keep this modal
            onCancel = closeModal,
            visible = visible,
            footer = null, // removes "Ok" & "Cancel" buttons
          )
          val isPending = stateEV.value.ingredientsPot.isPending
          val modalContent = <.div(
            <.div(<.button(
              ^.disabled := isPending,
              ^.className := "getIngredients",
              "Fetch ingredients",
              ^.onClick := (_ => AutowireClient[SApi].listIngredients.call().toPot(stateEV.zoomL(State.ingredientsPot))),
            )),
            <.div("Click to update ingredients data from Opteinics API. In future this should be done in as a scheduled background job."),
            <.div(<.button(
              ^.disabled := isPending,
              ^.className := "updateIngredients",
              if (isPending) showSpinner else "Update ingredients",
              ^.onClick := { _ =>
                AutowireClient[SApi].updateIngredientsFromOpteinics().call()
                  .flatMap(_ => AutowireClient[SApi].listIngredients.call())
                  .toPot(stateEV.zoomL(State.ingredientsPot))
              },
            )),
            <.div(
              stateEV.value.ingredientsPot match {
                case Ready(ingredients)              => ingredients.map(_.name).map(<.div(_))
                case Pending(_) | PendingStale(_, _) => ""
                case _                               => "Ingredients will appear here"
              },
            ),
          )

          AntJs.Modal(modalParams)(modalContent)
        }
        HoverMenu.modal("Ingredients", IngredientsIcon, modalRenderer)
      }

      def updateLocationsMenuItem(): ReactElement = {
        val modalRenderer: (CloseCb, Visible) => ReactElement = (closeModal, visible) => {
          val modalParams = o(
            title = "Locations", // todo: add message here and below if we keep this modal
            onCancel = closeModal,
            visible = visible,
            footer = null, // removes "Ok" & "Cancel" buttons
          )
          val isPending = stateEV.value.locationsPot.isPending
          val modalContent = <.div(
            <.div(<.button(
              ^.disabled := isPending,
              ^.className := "getLocations",
              "Fetch locations",
              ^.onClick := (_ => AutowireClient[SApi].listLocations.call().toPot(stateEV.zoomL(State.locationsPot))),
            )),
            <.div("Click to update locations data from Opteinics API. In future this should be done in as a scheduled background job."),
            <.div(<.button(
              ^.disabled := isPending,
              ^.className := "updateLocations",
              if (isPending) showSpinner else "Update locations",
              ^.onClick := { _ =>
                AutowireClient[SApi].updateLocationsFromOpteinics().call()
                  .flatMap(_ => AutowireClient[SApi].listLocations.call())
                  .toPot(stateEV.zoomL(State.locationsPot))
              },
            )),
            <.div(
              stateEV.value.locationsPot match {
                case Ready(locations)                => locations.map(_.name).map(<.div(_))
                case Pending(_) | PendingStale(_, _) => ""
                case _                               => "Locations will appear here"
              },
            ),
          )

          AntJs.Modal(modalParams)(modalContent)
        }
        HoverMenu.modal("Locations", LocationsIcon, modalRenderer)
      }

      def statsMenuItem: ReactElement = {
        val modalRenderer: (CloseCb, Visible) => ReactElement = (closeModal, visible) => {
          val modalParams = o(
            title = M("js.label.statistics"),
            onCancel = closeModal,
            visible = visible,
            width = "90%",
            footer = null, // removes "Ok" & "Cancel" buttons
          )
          val modalContent: ReactElement = gridModelPotEV.value match {
            case Ready(gridModel) =>
              val markedRows = gridModel.rowsMap.filter(_._2.marked)
              val (fetchedRows, unfetchedRows) = markedRows.partition(_._2.additionalData.hasDataLoaded)
              val containsUncalculatedRow = unfetchedRows.exists(_._2.getValue(SGrid.Calculated).contains(false))

              if (markedRows.isEmpty) <.div("Please mark the periods to add to statistics graphs")
              else if (containsUncalculatedRow) <.div("Please select only periods with calculated sustainability")
              else if (unfetchedRows.nonEmpty) {
                loadDataForRows(unfetchedRows) // if some rows are not fetched yet, fetch them and wait till loaded
                showSpinner
              } else {
                val sustainabilities = fetchedRows.values.toSeq.map(_.additionalData.dataOpt)
                val periods = markedRows.values.toSeq.map(rowToPeriod)
                Sections2(
                  sections = SustainabilityStatsSection.sectionTitles,
                  selectedSectionEV = statsSectionEV.bMap(_.entryName)(SustainabilityStatsSection.withName),
                  content = statsSectionEV.value.renderContent(sustainabilities, periods).toScalaJSReact,
                ).toSlinky
              }
            case _ => showSpinner
          }

          AntJs.Modal(modalParams)(modalContent)
        }

        HoverMenu.modal(M("js.label.statistics"), StatsIcon, modalRenderer)
      }

      val hoverMenu = {
        val deleteFn: SDeleteFnParams => DeleteButton = params => {
          params.row.additionalData.serverDataOpt.flatMap(_.periodIdOpt) match {
            case Some(id) => DeleteButton.RemoveRemotely(() => AutowireClient[SApi].deletePeriod(id).call)
            case None     => DeleteButton.RemoveLocally
          }
        }

        gridHandle.hoverMenu(
          additionalMenuItems = Seq(
            statsMenuItem.toScalaJSReact,
            updateIngredientsMenuItem().toScalaJSReact,
            updateLocationsMenuItem().toScalaJSReact,
          ),
          deleteBtn = DeleteBtnParams(
            deleteFn = deleteFn,
            rowToLabelFn = params => Some(rowToPeriod(params.row).toString),
          ),
        )
      }

      PageLayout.headerAndPositionPage(
        pageTitle = M("js.title.sustainability"),
        position = gridHandle.indexSlashCount,
        content = ContentLayout.gridWithDetailOnRight(
          hoverMenu = hoverMenu,
          grid = gridHandle.gridElement,
          detail = useMemo(gridHandle.selectedRowOpt, selectedSectionEV.value)(detailSections),
          showDetailEV = viewEV.zoomL(View.showDetail),
        ),
        // todo: topContent = <.div(),
      )
    }
  }

  keepPushkaPicklersImport

  def parseDate(strDate: String): LocalDate =
    LocalDate.parse(strDate, DateTimeFormatter.ISO_LOCAL_DATE)

  def parseDate(event: SyntheticEvent[TagElement#RefType, Event]): LocalDate =
    parseDate(event.target.asInstanceOf[HTMLInputElement].value)

  js.Dynamic.global.window.updateDynamic("SustainabilityPage")(component)

}
