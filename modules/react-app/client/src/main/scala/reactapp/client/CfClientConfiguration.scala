package reactapp.client

import flash.shared.system.ExceptionSummary
import org.scalajs.dom
import reactapp.client.common.component.ActionFeedback
import reactapp.client.common.grid.{ CfEntityStateRenderer, CfIconHtml }
import reactapp.client.facades.M
import reactapp.shared.CfSharedConfiguration
import reactapp.shared.common.grid.CfCellIcon
import sjsgrid.client.common.PageContext
import sjsgrid.client.config.ClientConfiguration
import sjsgrid.client.exceptionreport.GridExceptionReport
import sjsgrid.client.grid.component.EntityStateRenderer
import sjsgrid.flash.client.components.{ FlashExceptionReporter, SubGrid }
import sjsgrid.flash.client.{ FlashClientConfig, FlashLocalization }
import slinky.core.facade.ReactElement
import slinky.web.html_<^._

import scala.scalajs.js
import reactapp.client.common.CloudFarmsOps._

/**
  * Created by Milan Satala
  * Date: 11/27/17
  * Time: 10:23 AM
  */
object CfClientConfiguration extends ClientConfiguration with CfSharedConfiguration with CfIconHtml with FlashClientConfig
    with SubGrid.Renderer {
  override def noRowsOverlay(implicit pageContext: PageContext): String =
    s"""<div class="aggrid-empty-overlay">${CfCellIcon.InfoHtml} ${pageContext.localize("js.label.grid.norowstoshow")}</div>"""

  override val entityStateRenderer: EntityStateRenderer = CfEntityStateRenderer

  override val warningModalClass: String = "warning-editor"
  override val warningSaveBtnClass: String = "small button"
  override val warningCancelBtnClass: String = "small button"

  override val statusColumnHeaderName: String = "<i class='awesome-icon-info heading-centered-icon'></i>"

  override val sortingAscIcon: ReactElement = <.i(^.className := s"awesome-icon-sort-asc")
  override val sortingDescIcon: ReactElement = <.i(^.className := s"awesome-icon-sort-desc")
  override val loadingSpinnerHtml: String = """<span class="awesome-icon-spinner spinning-icon loading-indicator"></span>"""

  override def reportExceptions: Seq[GridExceptionReport] => Unit = { exceptions =>
    exceptions.foreach { r =>
      dom.console.error(s"Grid exception at ${r.method} with params [${r.parameters.mkString(", ")}]:")
      r.e.printStackTrace()
    }
    ActionFeedback.showErrorNotification()
  }

  override def filterIcon(filtering: Boolean, filteringHidden: Boolean): ReactElement = {
    <.i(
      ^.className := s"awesome-icon-filter slick-filter-indicator heading-centered-icon ${if (filtering) "filtering" else ""} ${if (filteringHidden) "filtering-hidden"
      else ""}",
    )
  }

  override val markColumnIconHtml: String = """<i class="awesome-icon-check-square-o" aria-hidden="true"></i>"""

  override val flashExceptionReporter: FlashExceptionReporter = new FlashExceptionReporter {
    override val errorMsg: String = M("js.feedback.error.desc")

    override def sendSummary(summary: ExceptionSummary): Unit = ()
  }
  override val flashLocalization: FlashLocalization = new FlashLocalization {
    override def unknownServerErrorTitle: String = "unknownServerErrorTitle"

    override def unknownServerErrorDesc: String = "unknownServerErrorDesc"

    override def tryAgain: String = "tryAgain"

    override def emptyAutocomplete: String = "emptyAutocomplete"

    override def multiInputAddBtn: String = "multiInputAddBtn"
  }
  override val antLocale: js.Any = js.Dynamic.global.AntLocaleEnUS

  override def renderSubGrid(
    gridElement: ReactElement,
    hoverMenu: ReactElement,
    heightPxOpt: Option[Int],
    minHeightPxOpt: Option[Int] = None,
  ): ReactElement = {
    val minHeightOpt = minHeightPxOpt.map(height => s"min-height: ${height}px;").map(cssClass("GridMinHeight", _))

    <.div(
      heightPxOpt match {
        case None           => NoContent
        case Some(heightPx) => ^.style := o(height = heightPx + "px")
      },
      ^.className := "ag-fresh columns " + cssClass(
        "Grid container",
        """& {
          overflow-x: hidden;
          overflow-y: hidden;
          border: 1px solid #b0b0b0;
          padding-right: 0px;
          padding-left: 40px;
        }""",
      ) + " with-left-menu " +
        minHeightOpt.getOrElse(""),
      gridElement,
      <.div(
        ^.className := cssClass("Hover menu container", """& {font-size:16px;}"""),
        hoverMenu,
      ),
    )
  }
}
