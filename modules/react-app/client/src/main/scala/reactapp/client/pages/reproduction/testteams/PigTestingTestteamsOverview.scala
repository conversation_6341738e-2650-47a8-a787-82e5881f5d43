package reactapp.client.pages.reproduction.testteams

import autowire._
import japgolly.scalajs.react._
import japgolly.scalajs.react.vdom.html_<^._
import moment.Moment
import monocle.macros.Lenses
import org.scalajs.dom
import org.scalajs.macrotaskexecutor.MacrotaskExecutor.Implicits._
import pushka.annotation.pushka
import reactapp.client.common.CloudFarmsOps._
import reactapp.client.common._
import reactapp.client.common.component.HoverMenu2.{ DeleteButton2, RefreshButton }
import reactapp.client.common.component.{ HoverMenu2, VerticalField }
import reactapp.client.common.grid.views.ViewsState
import reactapp.client.facades.M
import reactapp.shared.common.form.FormField
import reactapp.shared.grid.Field
import reactapp.shared.reproduction.testteams.{ PigTestingTestteamsApi, PigTestingTestteamsOverviewGrid }
import reactapp.shared.view.ViewId
import sjsgrid.client.form.{ AutocompleteSource, FormModel }
import sjsgrid.client.grid.params.OnSaveParams
import sjsgrid.client.grid.{ <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ridMode<PERSON>, SavingHandler }
import sjsgrid.shared.common.PushkaPicklers._
import sjsgrid.shared.common.dto.BusinessException
import sjsgrid.shared.form.{ FieldType, FormDefinition }
import sjsgrid.shared.grid.LabelValue
import sjsgrid.shared.grid.column.ColumnsState
import sjsgrid.shared.grid.dto.FilteringAndSorting

import java.time.Instant
import scala.collection.immutable
import scala.util.{ Failure, Success }

/**
  * Created by sunik on 12/12/17.
  */
case class PigTestingTestteamsOverview(id: Int, testTeamStartOpt: Option[Instant], savingHandler: SavingHandler, cfContext: CfContext) {
  def apply(): VdomElement = PigTestingTestteamsOverview.overviewGrid.withKey(id)(this)
}

object PigTestingTestteamsOverview {
  type OverviewAD = Long

  @Lenses
  case class State(
    gridModel: GridModel[PigTestingTestteamsOverviewGrid, OverviewAD] = PigTestingTestteamsOverviewGrid.pendingModel,
    selectedRowIdOpt: Option[Int] = None,
    addAnimalForm: FormModel[AddAnimalForm],
    urlState: UrlState = UrlState(),
    freeAnimalIds: Seq[String] = Seq(),
    viewsState: ViewsState[View],
    columnsState: ColumnsState[PigTestingTestteamsOverviewGrid],
    animalId: Int,
  ) {}

  @pushka
  @Lenses
  case class UrlState(view: View = View(), selectedViewIdOpt: Option[ViewId] = None)

  @pushka
  @Lenses
  case class View(
    columnsState: ColumnsState[PigTestingTestteamsOverviewGrid] = PigTestingTestteamsOverviewGrid.columnsState(),
  )

  private def onSave(params: OnSaveParams[PigTestingTestteamsOverview, State, PigTestingTestteamsOverviewGrid, OverviewAD]) = Some {
    AutowireClient[PigTestingTestteamsApi].saveGiltsToTestteam(params.props.id.toLong, params.unsavedRows.rowsToSaveDTO).call().map {
      responseMap =>
        params.gridModelEV.modCb(_.saveRowsOrProblems(responseMap)((_, _, row) => row)).runNow()
    }
  }

  val overviewGrid = GridBuilder[PigTestingTestteamsOverview, PigTestingTestteamsOverviewGrid](
    "PigTestingTestteamsOverviewGrid",
    PigTestingTestteamsOverviewGrid,
  )
    .pageContextGetter(_.cfContext)
    .initialStateFromProps { props =>
      val defaultStartDate = props.testTeamStartOpt.getOrElse(
        props.cfContext.localDateToInstant(props.cfContext.localDateFromMillis(Moment().dynamic.valueOf().asInstanceOf[Double])),
      )
      State(
        animalId = props.id,
        columnsState = ColumnsState[PigTestingTestteamsOverviewGrid](FilteringAndSorting.empty),
        addAnimalForm = FormModel.fromModelValues2(AddAnimalForm, props.cfContext)(
          AddAnimalForm.Date -> Some(defaultStartDate),
          AddAnimalForm.AnimalId -> None,
        ),
        viewsState = ViewsState(props.cfContext.getAvailableViews[View](PigTestingTestteamsOverviewGrid.viewName)),
      )
    }
    .getGridModelEVFromState[OverviewAD](State.gridModel)
    .inMemory
    .bindUrlState(State.urlState, "overview")
    .withView[View](
      currentViewL = State.urlState composeLens UrlState.view,
      viewsStateL = State.viewsState,
      selectedViewL = State.urlState composeLens UrlState.selectedViewIdOpt,
      columnsStateL = View.columnsState,
    )
    .componentWillMount(params => {
      Callback {
        AutowireClient[PigTestingTestteamsApi].getOverviewData(params.props.id).call.map { data =>
          val gridRows = data.map { row =>
            row._2.rowModel.toGridRow(row._1, params.props.cfContext, params.gridDefinition)
          }
          params.stateEV.zoomL(State.gridModel).setCb(GridModel.fromRows(gridRows)).runNow()
        }
      }
    })
    .componentWillReceiveProps(params => {
      Callback {
        if (params.currentProps.id != params.nextProps.id) {
          AutowireClient[PigTestingTestteamsApi].getOverviewData(params.nextProps.id).call.map { data =>
            val gridRows = data.map { row =>
              row._2.rowModel.toGridRow(row._1, params.currentProps.cfContext, PigTestingTestteamsOverviewGrid)
            }
            params.modState(_.copy(gridModel =
              GridModel.fromRows(gridRows),
            )).runNow()
          }
          val defaultStartDate = params.nextProps.testTeamStartOpt.getOrElse(params.nextProps.cfContext.localDateToInstant(
            params.nextProps.cfContext.localDateFromMillis(Moment().dynamic.valueOf().asInstanceOf[Double]),
          ))
          params.modState(_.copy(addAnimalForm = {
            FormModel.fromModelValues2(AddAnimalForm, params.currentProps.cfContext)(
              AddAnimalForm.Date -> Some(defaultStartDate),
              AddAnimalForm.AnimalId -> None,
            )
          })).runNow()
        }
      }
    })
    .bindSelectedRowId(id => id.stateEV.zoomL(State.selectedRowIdOpt))
    .onSave(onSave)
    .render { params =>
      def reloadGridData = {
        AutowireClient[PigTestingTestteamsApi].getOverviewData(params.props.id).call.map { data =>
          val gridRows = data.map { row =>
            row._2.rowModel.toGridRow(row._1, params.props.cfContext, params.gridDefinition)
          }
          params.stateEV.zoomL(State.gridModel).setCb(GridModel.fromRows(gridRows)).runNow()
        }
      }

      <.div(
        ^.height := "97%",
        <.div(
          ^.cls := "FlexLayout FlexLayout-Row",
          VerticalField.dateTimePicker(params.stateEV.zoomL(State.addAnimalForm).zoomL(FormModel.field(AddAnimalForm.Date))),
          VerticalField.autocomplete(
            fieldInstanceEV = params.stateEV.zoomL(State.addAnimalForm).zoomL(FormModel.field(AddAnimalForm.AnimalId)),
            autocompleteSource = AutocompleteSource.Async(term => {
              AutowireClient[PigTestingTestteamsApi].getAnimalIdsWithoutTestteam(term).call.map(_.map(animalId =>
                LabelValue(animalId, animalId),
              ))
            }),
            minLength = 3,
          ),
          VerticalField.number(params.stateEV.zoomL(State.addAnimalForm).zoomL(FormModel.field(AddAnimalForm.StartWeight))),
          <.button(
            ^.cls := "button",
            M("js.label.testteam.add.animal"),
            (
              params.stateEV.value.addAnimalForm.getValue(AddAnimalForm.AnimalId),
              params.stateEV.value.addAnimalForm.getValue(AddAnimalForm.Date),
              params.stateEV.value.addAnimalForm.getValue(AddAnimalForm.StartWeight),
            ) match {
              case (Some(animalId), Some(date), startWeightOpt) =>
                ^.onClick --> Callback {
                  AutowireClient[PigTestingTestteamsApi].addGiltsToTestteam(
                    params.props.id,
                    date,
                    animalId,
                    startWeightOpt,
                    supressWarnings = false,
                  ).call().onComplete {
                    case Success(warnings) =>
                      if (warnings.isEmpty)
                        reloadGridData
                      else if (dom.window.confirm(M("js.label.warning") + "\n" + warnings.foldLeft("")((x, y) => x + "\n" + y))) {
                        AutowireClient[PigTestingTestteamsApi].addGiltsToTestteam(
                          params.props.id,
                          date,
                          animalId,
                          startWeightOpt,
                          supressWarnings = true,
                        ).call().onComplete {
                          case Success(_) =>
                            reloadGridData

                          case Failure(failure) =>
                            failure match {
                              case BusinessException(errorSeq) => dom.window.alert(errorSeq.head.message)
                              case _                           => dom.window.alert(M("error.testteam.adding.failed", animalId))
                            }
                        }
                      }

                    case Failure(failure) =>
                      failure match {
                        case BusinessException(errorSeq) => dom.window.alert(errorSeq.head.message)
                        case _                           => dom.window.alert(M("error.testteam.adding.failed", animalId))

                      }
                  }

                }
              case _ => ^.cls := "disabled"
            },
          ),
        ),
        <.div(
          ^.cls := "FlexLayout__Grow FlexLayout FlexLayout-Row",
          ^.height := "90%",
          ^.width := "100%",
          ^.marginTop := "10px",
          <.div(
            ^.cls := "ag-fresh",
            ^.width := "100%",
            params.gridElement,
          ),
        ),
        params.hoverMenu(
          showRefreshBtn = RefreshButton.ShowInMemoryCb(
            Callback {
              params.stateEV.zoomL(State.gridModel).setCb(PigTestingTestteamsOverviewGrid.pendingModel[OverviewAD]).runNow()
              reloadGridData
            },
          ),
          deleteBtnFnOpt = Some((_, row) => {
            DeleteButton2.OnClickCb(Callback {
              val animalId = row.additionalData
              AutowireClient[PigTestingTestteamsApi].removeGilt(params.props.id, animalId).call().onComplete {
                case Success(_) =>
                  reloadGridData

                case Failure(_) =>
                  dom.window.alert(M("error.action.failed.to.delete", M("js.label.transfer")))
              }
            })
          }),
          exportElOpt =
            Some(HoverMenu2.exportWithSubMenu(s"/export/pigtesting/overview?columnsState=${CloudFarmsOps.writeToParam(params.state.urlState.view.columnsState)}&testTeam=${params.props.id}")),
        ),
      )
    }.build()
  keepPushkaPicklersImport

}

sealed trait AddAnimalForm extends FormField

object AddAnimalForm extends FormDefinition[AddAnimalForm] {

  case object Date extends AddAnimalForm with FieldType.Instant with Field.Mandatory

  case object AnimalId extends AddAnimalForm with FieldType.Text with Field.Mandatory

  case object StartWeight extends AddAnimalForm with FieldType.Long with Field.Editable

  def values: immutable.IndexedSeq[AddAnimalForm] = findValues

  val cssId = s"Form-AddAnimalForm"
}
