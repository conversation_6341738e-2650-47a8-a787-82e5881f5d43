package reactapp.client.pages.reports.efficiency

import japgolly.scalajs.react.CtorType
import japgolly.scalajs.react.component.Js.Component
import sjsgrid.flash.client.facade.JsComponentHelper
import sjsgrid.flash.client.facade.JsComponentHelper.SlinkyJsCmp

import scala.scalajs.js
import scala.scalajs.js.annotation.JSImport

object ReportAssistantWrapper extends JsComponentHelper {
  @JSImport("ReportAssistantWrapper", JSImport.Default, "ReportAssistantWrapper")
  @js.native
  private object ReportAssistantWrapperJs extends js.Any

  val cmp: Component[js.Object, js.Object, CtorType.Props] = jsComponentSjsReact(ReportAssistantWrapperJs)
}
