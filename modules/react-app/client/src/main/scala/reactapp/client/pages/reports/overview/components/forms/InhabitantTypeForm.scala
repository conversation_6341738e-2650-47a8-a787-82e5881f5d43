package reactapp.client.pages.reports.overview.components.forms

import reactapp.client.common.CfContext
import reactapp.client.common.component.FormRenderer
import reactapp.shared.reports.overview.InhabitantSettingsFormDefinition
import sjsgrid.client.common.react.ExtVar
import sjsgrid.client.form.FormModel
import slinky.core.FunctionalComponent
import slinky.core.annotations.react

@react object InhabitantTypeForm {
  case class Props(
    formModelEV: ExtVar[FormModel[InhabitantSettingsFormDefinition]],
    cfContext: CfContext,
  )

  val component: FunctionalComponent[Props] = {
    FunctionalComponent[Props] {
      case Props(formModelEV, cfContext) =>
        FormRenderer(
          InhabitantSettingsFormDefinition,
          formModelEV,
        )(cfContext).render
    }
  }
}
