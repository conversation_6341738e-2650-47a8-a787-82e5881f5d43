//If security_restrictions.session_duration_mins has value for holding, it will wait set up time and show red window with text - you need to move mouse or click in 2 minutes (countdown), or you will be logged out automatically.

export function sessionDurationLogout(sessionDuration, farmId, userName) {
    if (sessionDuration > 0) {
        (function () {
            'use strict';

            const logoutJsonMessage = {
                userName: userName,
                farmId: farmId,
                tz: Intl.DateTimeFormat().resolvedOptions().timeZone,
            };

            const warnDiv = document.getElementById('sessionwarningbg');
            const remSpan = document.getElementById('sessionwarningsec');
            let timeout = window.setTimeout(warn, sessionDuration);
            let heartBeat;
            let interval = null;
            let remaining;
            const slow = 1800000, fast = 300000;
            let ok = true;

            let hidden, visibilityChange;
            if (typeof document.hidden !== "undefined") {
                hidden = "hidden";
                visibilityChange = "visibilitychange";
            } else if (typeof document.mozHidden !== "undefined") { // Firefox up to v17
                hidden = "mozHidden";
                visibilityChange = "mozvisibilitychange";
            } else if (typeof document.webkitHidden !== "undefined") { // Chrome up to v32, Android up to v4.4, Blackberry up to v10
                hidden = "webkitHidden";
                visibilityChange = "webkitvisibilitychange";
            }

            function handleVisibilityChange() {
                if (document[hidden]) {
                    if (timeout) window.clearTimeout(timeout);
                    if (interval) window.clearInterval(interval);
                    if (heartBeat) window.clearInterval(heartBeat);
                    interval = null;
                    timeout = null;
                    heartBeat = null;
                } else {
                    restartInactivityTimeout();
                }
            }

            function showSec() {
                remSpan.innerText = `${Math.floor(remaining / 60)}:${Math.floor(remaining % 60 / 10)}${Math.floor(remaining % 10)}`
            }

            function countdown() {
                if (remaining > 0) {
                    --remaining;
                    showSec();
                } else {
                    const logMessage = `[session-duration-logout] sessionDuration = ${sessionDuration}, remaining expired - logout user`;
                    console.info(logMessage, logoutJsonMessage);
                    window.DD_LOGS && window.DD_LOGS.logger && window.DD_LOGS.logger.info(logMessage, logoutJsonMessage);
                    window.location = '/logout';
                    window.clearInterval(interval);
                    interval = null;
                }
            }

            function warn() {
                if (heartBeat) {
                    window.clearInterval(heartBeat);
                    heartBeat = null;
                }
                remaining = 120;
                showSec();
                warnDiv.style.display = 'block';
                interval = window.setInterval(countdown, 1000);
            }

            function iAmHere() {
                const req = new XMLHttpRequest();
                req.timeout = fast - 1000;

                req.addEventListener("load", (event) => {
                    if (event && event.target && event.target.status === 401) {
                        const logMessage = `[session-duration-logout] sessionDuration = ${sessionDuration}, iamhere failed - logout user`;
                        console.info(logMessage, logoutJsonMessage);
                        window.DD_LOGS && window.DD_LOGS.logger && window.DD_LOGS.logger.info(logMessage, logoutJsonMessage);
                        window.location = '/logout';
                    } else if (event && event.target && event.target.status < 400) {
                        if (!ok) {
                            ok = true;
                            if (heartBeat) {
                                window.clearInterval(heartBeat);
                                heartBeat = window.setInterval(iAmHere, slow);
                            }
                        }
                    } else {
                        handleError(event);
                    }
                });

                function handleError(event) {
                    if (ok) {
                        ok = false
                        if (heartBeat) {
                            window.clearInterval(heartBeat);
                            heartBeat = window.setInterval(iAmHere, fast);
                        }
                    }
                }

                req.addEventListener("error", handleError);
                req.addEventListener("abort", handleError);
                req.open("GET", '/iamhere');
                req.send();
            }

            function restartInactivityTimeout() {
                if (timeout) window.clearTimeout(timeout);
                if (interval) window.clearInterval(interval);
                interval = null;
                timeout = window.setTimeout(warn, sessionDuration);
                warnDiv.style.display = 'none';
                if (!heartBeat) {
                    iAmHere();
                    heartBeat = window.setInterval(iAmHere, ok ? slow : fast);
                }
            }

            document.addEventListener(visibilityChange, handleVisibilityChange, false);
            document.addEventListener('mousemove', restartInactivityTimeout, false);
            document.addEventListener('keypress', restartInactivityTimeout, false);
        })();
    } else {
        (function () {
            'use strict';
            const slow = 1800000, fast = 300000;
            let ok = true;

            const logoutJsonMessage = {
                userName: userName,
                farmId: farmId,
                tz: Intl.DateTimeFormat().resolvedOptions().timeZone,
            };

            function iAmHere() {
                const req = new XMLHttpRequest();
                req.timeout = fast - 1000;

                req.addEventListener("load", (event) => {
                    if (event && event.target && event.target.status === 401) {
                        const logMessage = '[session-duration-logout] iamhere failed - logout user';
                        console.info(logMessage, logoutJsonMessage);
                        window.DD_LOGS && window.DD_LOGS.logger && window.DD_LOGS.logger.info(logMessage, logoutJsonMessage);
                        window.location = '/logout';
                    } else if (event && event.target && event.target.status < 400) {
                        if (!ok) {
                            ok = true;
                            if (heartBeat) window.clearInterval(heartBeat);
                            heartBeat = window.setInterval(iAmHere, slow);
                        }
                    } else {
                        handleError(event);
                    }
                });

                function handleError(event) {
                    if (ok) {
                        ok = false
                        if (heartBeat) window.clearInterval(heartBeat);
                        heartBeat = window.setInterval(iAmHere, fast);
                    }
                }

                req.addEventListener("error", handleError);
                req.addEventListener("abort", handleError);
                req.open("GET", '/iamhere');
                req.send();
            }

            let heartBeat = window.setInterval(iAmHere, slow);
        })();
    }
}
