package utils

import java.sql._
import java.util.Objects

import com.cloudfarms.integrator.integration.ResponseData
import org.jooq._
import org.jooq.impl.DSL

/**
 * Inspired by http://www.jooq.org/doc/3.6/manual/code-generation/custom-data-type-bindings/
 *
 * Created by <PERSON><PERSON><PERSON> on 13.8.2015.
 */
class ResponseDataJooqConverter extends Binding[Object, ResponseData] {
  override def converter(): Converter[Object, ResponseData] = {
    new Converter[Object, ResponseData] {
      override def toType: Class[ResponseData] = classOf[ResponseData]

      override def from(databaseObject: Object): ResponseData = {
        if (databaseObject == null) {
          // null string convert to null ResponseData. It is used when select returns null for example when joining column with ResponseData
          // type and there is nothing joined.
          null: ResponseData
        } else {
          val stringData = databaseObject.toString
          ResponseData.readData(stringData)
        }
      }

      override def to(userObject: ResponseData): Object =
        ResponseData.writeData(userObject)

      override def fromType(): Class[Object] = classOf[Object]
    }
  }

  override def set(ctx: BindingSetStatementContext[ResponseData]): Unit =
    ctx.statement().setString(ctx.index(), Objects.toString(ctx.convert[Object](converter()).value(), null))

  override def set(ctx: BindingSetSQLOutputContext[ResponseData]): Unit =
    throw new SQLFeatureNotSupportedException()

  override def get(ctx: BindingGetResultSetContext[ResponseData]): Unit =
    ctx.convert[Object](converter()).value(ctx.resultSet().getString(ctx.index()))

  override def get(ctx: BindingGetStatementContext[ResponseData]): Unit =
    ctx.convert[Object](converter()).value(ctx.statement().getString(ctx.index()))

  override def get(ctx: BindingGetSQLInputContext[ResponseData]): Unit =
    throw new SQLFeatureNotSupportedException()

  override def sql(ctx: BindingSQLContext[ResponseData]): Unit =
    ctx.render().visit(DSL.`val`(ctx.convert[Object](converter()).value())).sql("::jsonb")

  override def register(ctx: BindingRegisterContext[ResponseData]): Unit =
    ctx.statement().registerOutParameter(ctx.index(), Types.VARCHAR)
}
