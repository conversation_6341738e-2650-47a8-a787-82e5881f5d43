/**
 * This class is generated by jOOQ
 */
package jooq.public_.tables.records;

/**
 * This class is generated by jOOQ.
 */
@javax.annotation.Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.5.1"
	},
	comments = "This class is generated by jOOQ"
)
@java.lang.SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MsaHoldingApproverRecord extends org.jooq.impl.UpdatableRecordImpl<jooq.public_.tables.records.MsaHoldingApproverRecord> implements org.jooq.Record2<java.lang.Long, java.lang.Long> {

	private static final long serialVersionUID = *********;

	/**
	 * Setter for <code>public.msa_holding_approver.holding_id</code>.
	 */
	public void setHoldingId(java.lang.Long value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>public.msa_holding_approver.holding_id</code>.
	 */
	public java.lang.Long getHoldingId() {
		return (java.lang.Long) getValue(0);
	}

	/**
	 * Setter for <code>public.msa_holding_approver.account_id</code>.
	 */
	public void setAccountId(java.lang.Long value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>public.msa_holding_approver.account_id</code>.
	 */
	public java.lang.Long getAccountId() {
		return (java.lang.Long) getValue(1);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Record2<java.lang.Long, java.lang.Long> key() {
		return (org.jooq.Record2) super.key();
	}

	// -------------------------------------------------------------------------
	// Record2 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Row2<java.lang.Long, java.lang.Long> fieldsRow() {
		return (org.jooq.Row2) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Row2<java.lang.Long, java.lang.Long> valuesRow() {
		return (org.jooq.Row2) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Field<java.lang.Long> field1() {
		return jooq.public_.tables.MsaHoldingApprover.MSA_HOLDING_APPROVER.HOLDING_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Field<java.lang.Long> field2() {
		return jooq.public_.tables.MsaHoldingApprover.MSA_HOLDING_APPROVER.ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.lang.Long value1() {
		return getHoldingId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.lang.Long value2() {
		return getAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MsaHoldingApproverRecord value1(java.lang.Long value) {
		setHoldingId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MsaHoldingApproverRecord value2(java.lang.Long value) {
		setAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MsaHoldingApproverRecord values(java.lang.Long value1, java.lang.Long value2) {
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached MsaHoldingApproverRecord
	 */
	public MsaHoldingApproverRecord() {
		super(jooq.public_.tables.MsaHoldingApprover.MSA_HOLDING_APPROVER);
	}

	/**
	 * Create a detached, initialised MsaHoldingApproverRecord
	 */
	public MsaHoldingApproverRecord(java.lang.Long holdingId, java.lang.Long accountId) {
		super(jooq.public_.tables.MsaHoldingApprover.MSA_HOLDING_APPROVER);

		setValue(0, holdingId);
		setValue(1, accountId);
	}
}
