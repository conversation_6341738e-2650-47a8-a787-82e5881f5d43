/**
 * This class is generated by jOOQ
 */
package jooq.public_.tables.records;

/**
 * Every root holding (with parent_id is null) will automatically create a 
 * row here with the same id. This is a target of the foreign keys from the 
 * code tables
 */
@javax.annotation.Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.5.1"
	},
	comments = "This class is generated by jOOQ"
)
@java.lang.SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RootHoldingRecord extends org.jooq.impl.UpdatableRecordImpl<jooq.public_.tables.records.RootHoldingRecord> implements org.jooq.Record2<java.lang.Long, java.lang.String> {

	private static final long serialVersionUID = 1797404486;

	/**
	 * Setter for <code>public.root_holding.id</code>.
	 */
	public void setId(java.lang.Long value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>public.root_holding.id</code>.
	 */
	public java.lang.Long getId() {
		return (java.lang.Long) getValue(0);
	}

	/**
	 * Setter for <code>public.root_holding.migrated_to</code>.
	 */
	public void setMigratedTo(java.lang.String value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>public.root_holding.migrated_to</code>.
	 */
	public java.lang.String getMigratedTo() {
		return (java.lang.String) getValue(1);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Record1<java.lang.Long> key() {
		return (org.jooq.Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record2 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Row2<java.lang.Long, java.lang.String> fieldsRow() {
		return (org.jooq.Row2) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Row2<java.lang.Long, java.lang.String> valuesRow() {
		return (org.jooq.Row2) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Field<java.lang.Long> field1() {
		return jooq.public_.tables.RootHolding.ROOT_HOLDING.ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Field<java.lang.String> field2() {
		return jooq.public_.tables.RootHolding.ROOT_HOLDING.MIGRATED_TO;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.lang.Long value1() {
		return getId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.lang.String value2() {
		return getMigratedTo();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public RootHoldingRecord value1(java.lang.Long value) {
		setId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public RootHoldingRecord value2(java.lang.String value) {
		setMigratedTo(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public RootHoldingRecord values(java.lang.Long value1, java.lang.String value2) {
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached RootHoldingRecord
	 */
	public RootHoldingRecord() {
		super(jooq.public_.tables.RootHolding.ROOT_HOLDING);
	}

	/**
	 * Create a detached, initialised RootHoldingRecord
	 */
	public RootHoldingRecord(java.lang.Long id, java.lang.String migratedTo) {
		super(jooq.public_.tables.RootHolding.ROOT_HOLDING);

		setValue(0, id);
		setValue(1, migratedTo);
	}
}
