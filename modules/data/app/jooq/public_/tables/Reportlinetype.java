/**
 * This class is generated by jOOQ
 */
package jooq.public_.tables;

/**
 * Contain all the lines per report.
 */
@javax.annotation.Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.5.1"
	},
	comments = "This class is generated by jOOQ"
)
@java.lang.SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Reportlinetype extends org.jooq.impl.TableImpl<jooq.public_.tables.records.ReportlinetypeRecord> {

	private static final long serialVersionUID = -1556818422;

	/**
	 * The reference instance of <code>public.reportlinetype</code>
	 */
	public static final jooq.public_.tables.Reportlinetype REPORTLINETYPE = new jooq.public_.tables.Reportlinetype();

	/**
	 * The class holding records for this type
	 */
	@Override
	public java.lang.Class<jooq.public_.tables.records.ReportlinetypeRecord> getRecordType() {
		return jooq.public_.tables.records.ReportlinetypeRecord.class;
	}

	/**
	 * The column <code>public.reportlinetype.id</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.ReportlinetypeRecord, java.lang.Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>public.reportlinetype.reporttype_code</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.ReportlinetypeRecord, java.lang.String> REPORTTYPE_CODE = createField("reporttype_code", org.jooq.impl.SQLDataType.VARCHAR.length(15).nullable(false), this, "");

	/**
	 * The column <code>public.reportlinetype.pos</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.ReportlinetypeRecord, java.lang.Integer> POS = createField("pos", org.jooq.impl.SQLDataType.INTEGER.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>public.reportlinetype.name</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.ReportlinetypeRecord, java.lang.String> NAME = createField("name", org.jooq.impl.SQLDataType.VARCHAR.length(30).nullable(false), this, "");

	/**
	 * The column <code>public.reportlinetype.description</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.ReportlinetypeRecord, java.lang.String> DESCRIPTION = createField("description", org.jooq.impl.SQLDataType.VARCHAR.length(150), this, "");

	/**
	 * The column <code>public.reportlinetype.comment</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.ReportlinetypeRecord, java.lang.String> COMMENT = createField("comment", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "");

	/**
	 * Create a <code>public.reportlinetype</code> table reference
	 */
	public Reportlinetype() {
		this("reportlinetype", null);
	}

	/**
	 * Create an aliased <code>public.reportlinetype</code> table reference
	 */
	public Reportlinetype(java.lang.String alias) {
		this(alias, jooq.public_.tables.Reportlinetype.REPORTLINETYPE);
	}

	private Reportlinetype(java.lang.String alias, org.jooq.Table<jooq.public_.tables.records.ReportlinetypeRecord> aliased) {
		this(alias, aliased, null);
	}

	private Reportlinetype(java.lang.String alias, org.jooq.Table<jooq.public_.tables.records.ReportlinetypeRecord> aliased, org.jooq.Field<?>[] parameters) {
		super(alias, jooq.public_.Public.PUBLIC, aliased, parameters, "Contain all the lines per report.");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.Identity<jooq.public_.tables.records.ReportlinetypeRecord, java.lang.Long> getIdentity() {
		return jooq.public_.Keys.IDENTITY_REPORTLINETYPE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.UniqueKey<jooq.public_.tables.records.ReportlinetypeRecord> getPrimaryKey() {
		return jooq.public_.Keys.REPORTLINETYPE_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.util.List<org.jooq.UniqueKey<jooq.public_.tables.records.ReportlinetypeRecord>> getKeys() {
		return java.util.Arrays.<org.jooq.UniqueKey<jooq.public_.tables.records.ReportlinetypeRecord>>asList(jooq.public_.Keys.REPORTLINETYPE_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.util.List<org.jooq.ForeignKey<jooq.public_.tables.records.ReportlinetypeRecord, ?>> getReferences() {
		return java.util.Arrays.<org.jooq.ForeignKey<jooq.public_.tables.records.ReportlinetypeRecord, ?>>asList(jooq.public_.Keys.REPORTLINETYPE__REPORTLINETYPE_REPORTTYPE_CODE_FKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public jooq.public_.tables.Reportlinetype as(java.lang.String alias) {
		return new jooq.public_.tables.Reportlinetype(alias, this);
	}

	/**
	 * Rename this table
	 */
	public jooq.public_.tables.Reportlinetype rename(java.lang.String name) {
		return new jooq.public_.tables.Reportlinetype(name, null);
	}
}
