/**
 * This class is generated by jOOQ
 */
package jooq.public_.tables;

/**
 * This class is generated by jOOQ.
 */
@javax.annotation.Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.5.1"
	},
	comments = "This class is generated by jOOQ"
)
@java.lang.SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SikvaTreatmenCodes extends org.jooq.impl.TableImpl<jooq.public_.tables.records.SikvaTreatmenCodesRecord> {

	private static final long serialVersionUID = -539506437;

	/**
	 * The reference instance of <code>public.sikva_treatmen_codes</code>
	 */
	public static final jooq.public_.tables.SikvaTreatmenCodes SIKVA_TREATMEN_CODES = new jooq.public_.tables.SikvaTreatmenCodes();

	/**
	 * The class holding records for this type
	 */
	@Override
	public java.lang.Class<jooq.public_.tables.records.SikvaTreatmenCodesRecord> getRecordType() {
		return jooq.public_.tables.records.SikvaTreatmenCodesRecord.class;
	}

	/**
	 * The column <code>public.sikva_treatmen_codes.sikava_code</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.SikvaTreatmenCodesRecord, java.lang.Long> SIKAVA_CODE = createField("sikava_code", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

	/**
	 * The column <code>public.sikva_treatmen_codes.name</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.SikvaTreatmenCodesRecord, java.lang.String> NAME = createField("name", org.jooq.impl.SQLDataType.VARCHAR.length(160).nullable(false), this, "");

	/**
	 * Create a <code>public.sikva_treatmen_codes</code> table reference
	 */
	public SikvaTreatmenCodes() {
		this("sikva_treatmen_codes", null);
	}

	/**
	 * Create an aliased <code>public.sikva_treatmen_codes</code> table reference
	 */
	public SikvaTreatmenCodes(java.lang.String alias) {
		this(alias, jooq.public_.tables.SikvaTreatmenCodes.SIKVA_TREATMEN_CODES);
	}

	private SikvaTreatmenCodes(java.lang.String alias, org.jooq.Table<jooq.public_.tables.records.SikvaTreatmenCodesRecord> aliased) {
		this(alias, aliased, null);
	}

	private SikvaTreatmenCodes(java.lang.String alias, org.jooq.Table<jooq.public_.tables.records.SikvaTreatmenCodesRecord> aliased, org.jooq.Field<?>[] parameters) {
		super(alias, jooq.public_.Public.PUBLIC, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.UniqueKey<jooq.public_.tables.records.SikvaTreatmenCodesRecord> getPrimaryKey() {
		return jooq.public_.Keys.SIKVA_TREATMEN_CODES_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.util.List<org.jooq.UniqueKey<jooq.public_.tables.records.SikvaTreatmenCodesRecord>> getKeys() {
		return java.util.Arrays.<org.jooq.UniqueKey<jooq.public_.tables.records.SikvaTreatmenCodesRecord>>asList(jooq.public_.Keys.SIKVA_TREATMEN_CODES_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public jooq.public_.tables.SikvaTreatmenCodes as(java.lang.String alias) {
		return new jooq.public_.tables.SikvaTreatmenCodes(alias, this);
	}

	/**
	 * Rename this table
	 */
	public jooq.public_.tables.SikvaTreatmenCodes rename(java.lang.String name) {
		return new jooq.public_.tables.SikvaTreatmenCodes(name, null);
	}
}
