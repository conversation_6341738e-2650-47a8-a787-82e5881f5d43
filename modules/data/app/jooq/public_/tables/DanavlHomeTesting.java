/**
 * This class is generated by jOOQ
 */
package jooq.public_.tables;

/**
 * This class is generated by jOOQ.
 */
@javax.annotation.Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.5.1"
	},
	comments = "This class is generated by jOOQ"
)
@java.lang.SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DanavlHomeTesting extends org.jooq.impl.TableImpl<jooq.public_.tables.records.DanavlHomeTestingRecord> {

	private static final long serialVersionUID = 642020642;

	/**
	 * The reference instance of <code>public.danavl_home_testing</code>
	 */
	public static final jooq.public_.tables.DanavlHomeTesting DANAVL_HOME_TESTING = new jooq.public_.tables.DanavlHomeTesting();

	/**
	 * The class holding records for this type
	 */
	@Override
	public java.lang.Class<jooq.public_.tables.records.DanavlHomeTestingRecord> getRecordType() {
		return jooq.public_.tables.records.DanavlHomeTestingRecord.class;
	}

	/**
	 * The column <code>public.danavl_home_testing.animalid</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.lang.String> ANIMALID = createField("animalid", org.jooq.impl.SQLDataType.VARCHAR.length(11).nullable(false), this, "");

	/**
	 * The column <code>public.danavl_home_testing.sex</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.lang.Integer> SEX = createField("sex", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "");

	/**
	 * The column <code>public.danavl_home_testing.site1</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.lang.Integer> SITE1 = createField("site1", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "");

	/**
	 * The column <code>public.danavl_home_testing.site2</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.lang.Integer> SITE2 = createField("site2", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "");

	/**
	 * The column <code>public.danavl_home_testing.site3</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.lang.Integer> SITE3 = createField("site3", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "");

	/**
	 * The column <code>public.danavl_home_testing.year</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.lang.Integer> YEAR = createField("year", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "");

	/**
	 * The column <code>public.danavl_home_testing.repeat</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.lang.Integer> REPEAT = createField("repeat", org.jooq.impl.SQLDataType.INTEGER.nullable(false), this, "");

	/**
	 * The column <code>public.danavl_home_testing.dwg</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.math.BigDecimal> DWG = createField("dwg", org.jooq.impl.SQLDataType.NUMERIC.precision(6, 1), this, "");

	/**
	 * The column <code>public.danavl_home_testing.dfc</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.math.BigDecimal> DFC = createField("dfc", org.jooq.impl.SQLDataType.NUMERIC.precision(6, 1), this, "");

	/**
	 * The column <code>public.danavl_home_testing.lean</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.math.BigDecimal> LEAN = createField("lean", org.jooq.impl.SQLDataType.NUMERIC.precision(6, 1), this, "");

	/**
	 * The column <code>public.danavl_home_testing.strength</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.lang.Integer> STRENGTH = createField("strength", org.jooq.impl.SQLDataType.INTEGER, this, "");

	/**
	 * The column <code>public.danavl_home_testing.start_weight</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.lang.Integer> START_WEIGHT = createField("start_weight", org.jooq.impl.SQLDataType.INTEGER, this, "");

	/**
	 * The column <code>public.danavl_home_testing.scan_weight</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.lang.Integer> SCAN_WEIGHT = createField("scan_weight", org.jooq.impl.SQLDataType.INTEGER, this, "");

	/**
	 * The column <code>public.danavl_home_testing.days</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.lang.Integer> DAYS = createField("days", org.jooq.impl.SQLDataType.INTEGER, this, "");

	/**
	 * The column <code>public.danavl_home_testing.index</code>.
	 */
	public final org.jooq.TableField<jooq.public_.tables.records.DanavlHomeTestingRecord, java.lang.Integer> INDEX = createField("index", org.jooq.impl.SQLDataType.INTEGER, this, "");

	/**
	 * Create a <code>public.danavl_home_testing</code> table reference
	 */
	public DanavlHomeTesting() {
		this("danavl_home_testing", null);
	}

	/**
	 * Create an aliased <code>public.danavl_home_testing</code> table reference
	 */
	public DanavlHomeTesting(java.lang.String alias) {
		this(alias, jooq.public_.tables.DanavlHomeTesting.DANAVL_HOME_TESTING);
	}

	private DanavlHomeTesting(java.lang.String alias, org.jooq.Table<jooq.public_.tables.records.DanavlHomeTestingRecord> aliased) {
		this(alias, aliased, null);
	}

	private DanavlHomeTesting(java.lang.String alias, org.jooq.Table<jooq.public_.tables.records.DanavlHomeTestingRecord> aliased, org.jooq.Field<?>[] parameters) {
		super(alias, jooq.public_.Public.PUBLIC, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public org.jooq.UniqueKey<jooq.public_.tables.records.DanavlHomeTestingRecord> getPrimaryKey() {
		return jooq.public_.Keys.DANAVL_HOME_TESTING_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public java.util.List<org.jooq.UniqueKey<jooq.public_.tables.records.DanavlHomeTestingRecord>> getKeys() {
		return java.util.Arrays.<org.jooq.UniqueKey<jooq.public_.tables.records.DanavlHomeTestingRecord>>asList(jooq.public_.Keys.DANAVL_HOME_TESTING_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public jooq.public_.tables.DanavlHomeTesting as(java.lang.String alias) {
		return new jooq.public_.tables.DanavlHomeTesting(alias, this);
	}

	/**
	 * Rename this table
	 */
	public jooq.public_.tables.DanavlHomeTesting rename(java.lang.String name) {
		return new jooq.public_.tables.DanavlHomeTesting(name, null);
	}
}
