/**
 * This class is generated by jOOQ
 */
package jooq.public_.routines;

/**
 * This class is generated by jOOQ.
 */
@javax.annotation.Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.5.1"
	},
	comments = "This class is generated by jOOQ"
)
@java.lang.SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SetMobilePersona2 extends org.jooq.impl.AbstractRoutine<java.lang.Boolean> {

	private static final long serialVersionUID = -1379985531;

	/**
	 * The parameter <code>public.set_mobile_persona2.RETURN_VALUE</code>.
	 */
	public static final org.jooq.Parameter<java.lang.Boolean> RETURN_VALUE = createParameter("RETURN_VALUE", org.jooq.impl.SQLDataType.BOOLEAN, false);

	/**
	 * The parameter <code>public.set_mobile_persona2.a_acc_id</code>.
	 */
	public static final org.jooq.Parameter<java.lang.Long> A_ACC_ID = createParameter("a_acc_id", org.jooq.impl.SQLDataType.BIGINT, false);

	/**
	 * The parameter <code>public.set_mobile_persona2.a_org_id</code>.
	 */
	public static final org.jooq.Parameter<java.lang.Long> A_ORG_ID = createParameter("a_org_id", org.jooq.impl.SQLDataType.BIGINT, false);

	/**
	 * Create a new routine call instance
	 */
	public SetMobilePersona2() {
		super("set_mobile_persona2", jooq.public_.Public.PUBLIC, org.jooq.impl.SQLDataType.BOOLEAN);

		setReturnParameter(RETURN_VALUE);
		addInParameter(A_ACC_ID);
		addInParameter(A_ORG_ID);
	}

	/**
	 * Set the <code>a_acc_id</code> parameter IN value to the routine
	 */
	public void setAAccId(java.lang.Long value) {
		setValue(jooq.public_.routines.SetMobilePersona2.A_ACC_ID, value);
	}

	/**
	 * Set the <code>a_acc_id</code> parameter to the function to be used with a {@link org.jooq.Select} statement
	 */
	public void setAAccId(org.jooq.Field<java.lang.Long> field) {
		setField(A_ACC_ID, field);
	}

	/**
	 * Set the <code>a_org_id</code> parameter IN value to the routine
	 */
	public void setAOrgId(java.lang.Long value) {
		setValue(jooq.public_.routines.SetMobilePersona2.A_ORG_ID, value);
	}

	/**
	 * Set the <code>a_org_id</code> parameter to the function to be used with a {@link org.jooq.Select} statement
	 */
	public void setAOrgId(org.jooq.Field<java.lang.Long> field) {
		setField(A_ORG_ID, field);
	}
}
