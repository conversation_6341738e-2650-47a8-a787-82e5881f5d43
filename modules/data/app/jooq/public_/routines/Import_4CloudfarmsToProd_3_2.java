/**
 * This class is generated by jOOQ
 */
package jooq.public_.routines;

/**
 * This class is generated by jOOQ.
 */
@javax.annotation.Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.5.1"
	},
	comments = "This class is generated by jOOQ"
)
@java.lang.SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Import_4CloudfarmsToProd_3_2 extends org.jooq.impl.AbstractRoutine<java.lang.Void> {

	private static final long serialVersionUID = 709961794;

	/**
	 * Create a new routine call instance
	 */
	public Import_4CloudfarmsToProd_3_2() {
		super("import_4_cloudfarms_to_prod_3_2", jooq.public_.Public.PUBLIC);
	}
}
