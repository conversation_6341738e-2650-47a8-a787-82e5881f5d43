/**
 * This class is generated by jOOQ
 */
package jooq.public_.routines;

/**
 * This class is generated by jOOQ.
 */
@javax.annotation.Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.5.1"
	},
	comments = "This class is generated by jOOQ"
)
@java.lang.SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class GbtFloat4Consistent extends org.jooq.impl.AbstractRoutine<java.lang.Boolean> {

	private static final long serialVersionUID = -221837180;

	/**
	 * The parameter <code>public.gbt_float4_consistent.RETURN_VALUE</code>.
	 */
	public static final org.jooq.Parameter<java.lang.Boolean> RETURN_VALUE = createParameter("RETURN_VALUE", org.jooq.impl.SQLDataType.BOOLEAN, false);

	/**
	 * The parameter <code>public.gbt_float4_consistent._1</code>.
	 */
	public static final org.jooq.Parameter<java.lang.Object> _1 = createParameter("_1", org.jooq.impl.DefaultDataType.getDefaultDataType("internal"), false);

	/**
	 * The parameter <code>public.gbt_float4_consistent._2</code>.
	 */
	public static final org.jooq.Parameter<java.lang.Float> _2 = createParameter("_2", org.jooq.impl.SQLDataType.REAL, false);

	/**
	 * The parameter <code>public.gbt_float4_consistent._3</code>.
	 */
	public static final org.jooq.Parameter<java.lang.Short> _3 = createParameter("_3", org.jooq.impl.SQLDataType.SMALLINT, false);

	/**
	 * The parameter <code>public.gbt_float4_consistent._4</code>.
	 */
	public static final org.jooq.Parameter<java.lang.Long> _4 = createParameter("_4", org.jooq.impl.SQLDataType.BIGINT, false);

	/**
	 * The parameter <code>public.gbt_float4_consistent._5</code>.
	 */
	public static final org.jooq.Parameter<java.lang.Object> _5 = createParameter("_5", org.jooq.impl.DefaultDataType.getDefaultDataType("internal"), false);

	/**
	 * Create a new routine call instance
	 */
	public GbtFloat4Consistent() {
		super("gbt_float4_consistent", jooq.public_.Public.PUBLIC, org.jooq.impl.SQLDataType.BOOLEAN);

		setReturnParameter(RETURN_VALUE);
		addInParameter(_1);
		addInParameter(_2);
		addInParameter(_3);
		addInParameter(_4);
		addInParameter(_5);
	}

	/**
	 * Set the <code>_1</code> parameter IN value to the routine
	 */
	public void set__1(java.lang.Object value) {
		setValue(jooq.public_.routines.GbtFloat4Consistent._1, value);
	}

	/**
	 * Set the <code>_1</code> parameter to the function to be used with a {@link org.jooq.Select} statement
	 */
	public void set__1(org.jooq.Field<java.lang.Object> field) {
		setField(_1, field);
	}

	/**
	 * Set the <code>_2</code> parameter IN value to the routine
	 */
	public void set__2(java.lang.Float value) {
		setValue(jooq.public_.routines.GbtFloat4Consistent._2, value);
	}

	/**
	 * Set the <code>_2</code> parameter to the function to be used with a {@link org.jooq.Select} statement
	 */
	public void set__2(org.jooq.Field<java.lang.Float> field) {
		setField(_2, field);
	}

	/**
	 * Set the <code>_3</code> parameter IN value to the routine
	 */
	public void set__3(java.lang.Short value) {
		setValue(jooq.public_.routines.GbtFloat4Consistent._3, value);
	}

	/**
	 * Set the <code>_3</code> parameter to the function to be used with a {@link org.jooq.Select} statement
	 */
	public void set__3(org.jooq.Field<java.lang.Short> field) {
		setField(_3, field);
	}

	/**
	 * Set the <code>_4</code> parameter IN value to the routine
	 */
	public void set__4(java.lang.Long value) {
		setValue(jooq.public_.routines.GbtFloat4Consistent._4, value);
	}

	/**
	 * Set the <code>_4</code> parameter to the function to be used with a {@link org.jooq.Select} statement
	 */
	public void set__4(org.jooq.Field<java.lang.Long> field) {
		setField(_4, field);
	}

	/**
	 * Set the <code>_5</code> parameter IN value to the routine
	 */
	public void set__5(java.lang.Object value) {
		setValue(jooq.public_.routines.GbtFloat4Consistent._5, value);
	}

	/**
	 * Set the <code>_5</code> parameter to the function to be used with a {@link org.jooq.Select} statement
	 */
	public void set__5(org.jooq.Field<java.lang.Object> field) {
		setField(_5, field);
	}
}
