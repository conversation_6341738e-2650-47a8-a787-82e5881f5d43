/**
 * This class is generated by jOOQ
 */
package jooq.public_.routines;

/**
 * This class is generated by jOOQ.
 */
@javax.annotation.Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.5.1"
	},
	comments = "This class is generated by jOOQ"
)
@java.lang.SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class FullWeekreport extends org.jooq.impl.AbstractRoutine<java.lang.Void> {

	private static final long serialVersionUID = -2057884643;

	/**
	 * The parameter <code>public.full_weekreport.endweek</code>.
	 */
	public static final org.jooq.Parameter<java.sql.Date> ENDWEEK = createParameter("endweek", org.jooq.impl.SQLDataType.DATE, false);

	/**
	 * Create a new routine call instance
	 */
	public FullWeekreport() {
		super("full_weekreport", jooq.public_.Public.PUBLIC);

		addInParameter(ENDWEEK);
	}

	/**
	 * Set the <code>endweek</code> parameter IN value to the routine
	 */
	public void setEndweek(java.sql.Date value) {
		setValue(jooq.public_.routines.FullWeekreport.ENDWEEK, value);
	}
}
