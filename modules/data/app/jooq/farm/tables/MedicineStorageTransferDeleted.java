/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.MedicineStorageTransferDeletedRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MedicineStorageTransferDeleted extends TableImpl<MedicineStorageTransferDeletedRecord> {

	private static final long serialVersionUID = -320450664;

	/**
	 * The reference instance of <code>farm.medicine_storage_transfer_deleted</code>
	 */
	public static final MedicineStorageTransferDeleted MEDICINE_STORAGE_TRANSFER_DELETED = new MedicineStorageTransferDeleted();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<MedicineStorageTransferDeletedRecord> getRecordType() {
		return MedicineStorageTransferDeletedRecord.class;
	}

	/**
	 * The column <code>farm.medicine_storage_transfer_deleted.id</code>.
	 */
	public final TableField<MedicineStorageTransferDeletedRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

	/**
	 * The column <code>farm.medicine_storage_transfer_deleted.update_date</code>.
	 */
	public final TableField<MedicineStorageTransferDeletedRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.medicine_storage_transfer_deleted.update_account_id</code>.
	 */
	public final TableField<MedicineStorageTransferDeletedRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.medicine_storage_transfer_deleted.update_ui</code>.
	 */
	public final TableField<MedicineStorageTransferDeletedRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * Create a <code>farm.medicine_storage_transfer_deleted</code> table reference
	 */
	public MedicineStorageTransferDeleted() {
		this("medicine_storage_transfer_deleted", null);
	}

	/**
	 * Create an aliased <code>farm.medicine_storage_transfer_deleted</code> table reference
	 */
	public MedicineStorageTransferDeleted(String alias) {
		this(alias, MEDICINE_STORAGE_TRANSFER_DELETED);
	}

	private MedicineStorageTransferDeleted(String alias, Table<MedicineStorageTransferDeletedRecord> aliased) {
		this(alias, aliased, null);
	}

	private MedicineStorageTransferDeleted(String alias, Table<MedicineStorageTransferDeletedRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<MedicineStorageTransferDeletedRecord> getPrimaryKey() {
		return Keys.MEDICINE_STORAGE_TRANSFER_DELETED_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<MedicineStorageTransferDeletedRecord>> getKeys() {
		return Arrays.<UniqueKey<MedicineStorageTransferDeletedRecord>>asList(Keys.MEDICINE_STORAGE_TRANSFER_DELETED_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TableField<MedicineStorageTransferDeletedRecord, Timestamp> getRecordTimestamp() {
		return UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedicineStorageTransferDeleted as(String alias) {
		return new MedicineStorageTransferDeleted(alias, this);
	}

	/**
	 * Rename this table
	 */
	public MedicineStorageTransferDeleted rename(String name) {
		return new MedicineStorageTransferDeleted(name, null);
	}
}
