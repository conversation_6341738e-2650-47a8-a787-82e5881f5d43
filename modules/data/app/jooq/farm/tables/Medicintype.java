/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.tables.records.MedicintypeRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Medicintype extends TableImpl<MedicintypeRecord> {

	private static final long serialVersionUID = 423434486;

	/**
	 * The reference instance of <code>farm.medicintype</code>
	 */
	public static final Medicintype MEDICINTYPE = new Medicintype();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<MedicintypeRecord> getRecordType() {
		return MedicintypeRecord.class;
	}

	/**
	 * The column <code>farm.medicintype.create_date</code>.
	 */
	public final TableField<MedicintypeRecord, Timestamp> CREATE_DATE = createField("create_date", org.jooq.impl.SQLDataType.TIMESTAMP, this, "");

	/**
	 * The column <code>farm.medicintype.update_date</code>.
	 */
	public final TableField<MedicintypeRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP, this, "");

	/**
	 * The column <code>farm.medicintype.create_account_id</code>.
	 */
	public final TableField<MedicintypeRecord, Long> CREATE_ACCOUNT_ID = createField("create_account_id", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.medicintype.update_account_id</code>.
	 */
	public final TableField<MedicintypeRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.medicintype.create_ui</code>.
	 */
	public final TableField<MedicintypeRecord, String> CREATE_UI = createField("create_ui", org.jooq.impl.SQLDataType.CHAR.length(1), this, "");

	/**
	 * The column <code>farm.medicintype.update_ui</code>.
	 */
	public final TableField<MedicintypeRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1), this, "");

	/**
	 * The column <code>farm.medicintype.code</code>.
	 */
	public final TableField<MedicintypeRecord, String> CODE = createField("code", org.jooq.impl.SQLDataType.VARCHAR.length(15), this, "");

	/**
	 * The column <code>farm.medicintype.name</code>.
	 */
	public final TableField<MedicintypeRecord, String> NAME = createField("name", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.medicintype.description</code>.
	 */
	public final TableField<MedicintypeRecord, String> DESCRIPTION = createField("description", org.jooq.impl.SQLDataType.VARCHAR.length(300), this, "");

	/**
	 * The column <code>farm.medicintype.disabled</code>.
	 */
	public final TableField<MedicintypeRecord, Boolean> DISABLED = createField("disabled", org.jooq.impl.SQLDataType.BOOLEAN, this, "");

	/**
	 * Create a <code>farm.medicintype</code> table reference
	 */
	public Medicintype() {
		this("medicintype", null);
	}

	/**
	 * Create an aliased <code>farm.medicintype</code> table reference
	 */
	public Medicintype(String alias) {
		this(alias, MEDICINTYPE);
	}

	private Medicintype(String alias, Table<MedicintypeRecord> aliased) {
		this(alias, aliased, null);
	}

	private Medicintype(String alias, Table<MedicintypeRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Medicintype as(String alias) {
		return new Medicintype(alias, this);
	}

	/**
	 * Rename this table
	 */
	public Medicintype rename(String name) {
		return new Medicintype(name, null);
	}
}
