/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.tables.Medapplicationtype;

import org.jooq.Field;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class MedapplicationtypeRecord extends TableRecordImpl<MedapplicationtypeRecord> implements Record10<Timestamp, Timestamp, Long, Long, String, String, String, String, String, Boolean> {

	private static final long serialVersionUID = 1032275886;

	/**
	 * Setter for <code>farm.medapplicationtype.create_date</code>.
	 */
	public void setCreateDate(Timestamp value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.medapplicationtype.create_date</code>.
	 */
	public Timestamp getCreateDate() {
		return (Timestamp) getValue(0);
	}

	/**
	 * Setter for <code>farm.medapplicationtype.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.medapplicationtype.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(1);
	}

	/**
	 * Setter for <code>farm.medapplicationtype.create_account_id</code>.
	 */
	public void setCreateAccountId(Long value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.medapplicationtype.create_account_id</code>.
	 */
	public Long getCreateAccountId() {
		return (Long) getValue(2);
	}

	/**
	 * Setter for <code>farm.medapplicationtype.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.medapplicationtype.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(3);
	}

	/**
	 * Setter for <code>farm.medapplicationtype.create_ui</code>.
	 */
	public void setCreateUi(String value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>farm.medapplicationtype.create_ui</code>.
	 */
	public String getCreateUi() {
		return (String) getValue(4);
	}

	/**
	 * Setter for <code>farm.medapplicationtype.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(5, value);
	}

	/**
	 * Getter for <code>farm.medapplicationtype.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(5);
	}

	/**
	 * Setter for <code>farm.medapplicationtype.code</code>.
	 */
	public void setCode(String value) {
		setValue(6, value);
	}

	/**
	 * Getter for <code>farm.medapplicationtype.code</code>.
	 */
	public String getCode() {
		return (String) getValue(6);
	}

	/**
	 * Setter for <code>farm.medapplicationtype.name</code>.
	 */
	public void setName(String value) {
		setValue(7, value);
	}

	/**
	 * Getter for <code>farm.medapplicationtype.name</code>.
	 */
	public String getName() {
		return (String) getValue(7);
	}

	/**
	 * Setter for <code>farm.medapplicationtype.description</code>.
	 */
	public void setDescription(String value) {
		setValue(8, value);
	}

	/**
	 * Getter for <code>farm.medapplicationtype.description</code>.
	 */
	public String getDescription() {
		return (String) getValue(8);
	}

	/**
	 * Setter for <code>farm.medapplicationtype.disabled</code>.
	 */
	public void setDisabled(Boolean value) {
		setValue(9, value);
	}

	/**
	 * Getter for <code>farm.medapplicationtype.disabled</code>.
	 */
	public Boolean getDisabled() {
		return (Boolean) getValue(9);
	}

	// -------------------------------------------------------------------------
	// Record10 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row10<Timestamp, Timestamp, Long, Long, String, String, String, String, String, Boolean> fieldsRow() {
		return (Row10) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row10<Timestamp, Timestamp, Long, Long, String, String, String, String, String, Boolean> valuesRow() {
		return (Row10) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field1() {
		return Medapplicationtype.MEDAPPLICATIONTYPE.CREATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field2() {
		return Medapplicationtype.MEDAPPLICATIONTYPE.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field3() {
		return Medapplicationtype.MEDAPPLICATIONTYPE.CREATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field4() {
		return Medapplicationtype.MEDAPPLICATIONTYPE.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field5() {
		return Medapplicationtype.MEDAPPLICATIONTYPE.CREATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field6() {
		return Medapplicationtype.MEDAPPLICATIONTYPE.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field7() {
		return Medapplicationtype.MEDAPPLICATIONTYPE.CODE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field8() {
		return Medapplicationtype.MEDAPPLICATIONTYPE.NAME;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field9() {
		return Medapplicationtype.MEDAPPLICATIONTYPE.DESCRIPTION;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Boolean> field10() {
		return Medapplicationtype.MEDAPPLICATIONTYPE.DISABLED;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value1() {
		return getCreateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value2() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value3() {
		return getCreateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value4() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value5() {
		return getCreateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value6() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value7() {
		return getCode();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value8() {
		return getName();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value9() {
		return getDescription();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Boolean value10() {
		return getDisabled();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedapplicationtypeRecord value1(Timestamp value) {
		setCreateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedapplicationtypeRecord value2(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedapplicationtypeRecord value3(Long value) {
		setCreateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedapplicationtypeRecord value4(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedapplicationtypeRecord value5(String value) {
		setCreateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedapplicationtypeRecord value6(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedapplicationtypeRecord value7(String value) {
		setCode(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedapplicationtypeRecord value8(String value) {
		setName(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedapplicationtypeRecord value9(String value) {
		setDescription(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedapplicationtypeRecord value10(Boolean value) {
		setDisabled(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public MedapplicationtypeRecord values(Timestamp value1, Timestamp value2, Long value3, Long value4, String value5, String value6, String value7, String value8, String value9, Boolean value10) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		value6(value6);
		value7(value7);
		value8(value8);
		value9(value9);
		value10(value10);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached MedapplicationtypeRecord
	 */
	public MedapplicationtypeRecord() {
		super(Medapplicationtype.MEDAPPLICATIONTYPE);
	}

	/**
	 * Create a detached, initialised MedapplicationtypeRecord
	 */
	public MedapplicationtypeRecord(Timestamp createDate, Timestamp updateDate, Long createAccountId, Long updateAccountId, String createUi, String updateUi, String code, String name, String description, Boolean disabled) {
		super(Medapplicationtype.MEDAPPLICATIONTYPE);

		setValue(0, createDate);
		setValue(1, updateDate);
		setValue(2, createAccountId);
		setValue(3, updateAccountId);
		setValue(4, createUi);
		setValue(5, updateUi);
		setValue(6, code);
		setValue(7, name);
		setValue(8, description);
		setValue(9, disabled);
	}
}
