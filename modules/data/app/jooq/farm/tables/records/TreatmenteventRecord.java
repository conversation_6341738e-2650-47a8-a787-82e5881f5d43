/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.tables.Treatmentevent;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * Contains all the events for a treatment
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class TreatmenteventRecord extends UpdatableRecordImpl<TreatmenteventRecord> implements Record14<Timestamp, Timestamp, Long, Long, String, String, Long, Long, BigDecimal, Boolean, Timestamp, String, Long, Timestamp> {

	private static final long serialVersionUID = -1056327080;

	/**
	 * Setter for <code>farm.treatmentevent.create_date</code>.
	 */
	public void setCreateDate(Timestamp value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.create_date</code>.
	 */
	public Timestamp getCreateDate() {
		return (Timestamp) getValue(0);
	}

	/**
	 * Setter for <code>farm.treatmentevent.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(1);
	}

	/**
	 * Setter for <code>farm.treatmentevent.create_account_id</code>.
	 */
	public void setCreateAccountId(Long value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.create_account_id</code>.
	 */
	public Long getCreateAccountId() {
		return (Long) getValue(2);
	}

	/**
	 * Setter for <code>farm.treatmentevent.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(3);
	}

	/**
	 * Setter for <code>farm.treatmentevent.create_ui</code>.
	 */
	public void setCreateUi(String value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.create_ui</code>.
	 */
	public String getCreateUi() {
		return (String) getValue(4);
	}

	/**
	 * Setter for <code>farm.treatmentevent.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(5, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(5);
	}

	/**
	 * Setter for <code>farm.treatmentevent.id</code>.
	 */
	public void setId(Long value) {
		setValue(6, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.id</code>.
	 */
	public Long getId() {
		return (Long) getValue(6);
	}

	/**
	 * Setter for <code>farm.treatmentevent.treatment_id</code>.
	 */
	public void setTreatmentId(Long value) {
		setValue(7, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.treatment_id</code>.
	 */
	public Long getTreatmentId() {
		return (Long) getValue(7);
	}

	/**
	 * Setter for <code>farm.treatmentevent.amount</code>. Amount given total for the aminal or location for this specific event
	 */
	public void setAmount(BigDecimal value) {
		setValue(8, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.amount</code>. Amount given total for the aminal or location for this specific event
	 */
	public BigDecimal getAmount() {
		return (BigDecimal) getValue(8);
	}

	/**
	 * Setter for <code>farm.treatmentevent.finished</code>. When a treatment is finished it will marked with finish = true and as well on the table treatment.finished = true
	 */
	public void setFinished(Boolean value) {
		setValue(9, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.finished</code>. When a treatment is finished it will marked with finish = true and as well on the table treatment.finished = true
	 */
	public Boolean getFinished() {
		return (Boolean) getValue(9);
	}

	/**
	 * Setter for <code>farm.treatmentevent.date</code>. Date and time for the given treatmentevent
	 */
	public void setDate(Timestamp value) {
		setValue(10, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.date</code>. Date and time for the given treatmentevent
	 */
	public Timestamp getDate() {
		return (Timestamp) getValue(10);
	}

	/**
	 * Setter for <code>farm.treatmentevent.comment</code>.
	 */
	public void setComment(String value) {
		setValue(11, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.comment</code>.
	 */
	public String getComment() {
		return (String) getValue(11);
	}

	/**
	 * Setter for <code>farm.treatmentevent.actor_id</code>.
	 */
	public void setActorId(Long value) {
		setValue(12, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.actor_id</code>.
	 */
	public Long getActorId() {
		return (Long) getValue(12);
	}

	/**
	 * Setter for <code>farm.treatmentevent.actor_date</code>.
	 */
	public void setActorDate(Timestamp value) {
		setValue(13, value);
	}

	/**
	 * Getter for <code>farm.treatmentevent.actor_date</code>.
	 */
	public Timestamp getActorDate() {
		return (Timestamp) getValue(13);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record1<Long> key() {
		return (Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record14 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row14<Timestamp, Timestamp, Long, Long, String, String, Long, Long, BigDecimal, Boolean, Timestamp, String, Long, Timestamp> fieldsRow() {
		return (Row14) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row14<Timestamp, Timestamp, Long, Long, String, String, Long, Long, BigDecimal, Boolean, Timestamp, String, Long, Timestamp> valuesRow() {
		return (Row14) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field1() {
		return Treatmentevent.TREATMENTEVENT.CREATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field2() {
		return Treatmentevent.TREATMENTEVENT.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field3() {
		return Treatmentevent.TREATMENTEVENT.CREATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field4() {
		return Treatmentevent.TREATMENTEVENT.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field5() {
		return Treatmentevent.TREATMENTEVENT.CREATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field6() {
		return Treatmentevent.TREATMENTEVENT.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field7() {
		return Treatmentevent.TREATMENTEVENT.ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field8() {
		return Treatmentevent.TREATMENTEVENT.TREATMENT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<BigDecimal> field9() {
		return Treatmentevent.TREATMENTEVENT.AMOUNT;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Boolean> field10() {
		return Treatmentevent.TREATMENTEVENT.FINISHED;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field11() {
		return Treatmentevent.TREATMENTEVENT.DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field12() {
		return Treatmentevent.TREATMENTEVENT.COMMENT;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field13() {
		return Treatmentevent.TREATMENTEVENT.ACTOR_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field14() {
		return Treatmentevent.TREATMENTEVENT.ACTOR_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value1() {
		return getCreateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value2() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value3() {
		return getCreateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value4() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value5() {
		return getCreateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value6() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value7() {
		return getId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value8() {
		return getTreatmentId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BigDecimal value9() {
		return getAmount();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Boolean value10() {
		return getFinished();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value11() {
		return getDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value12() {
		return getComment();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value13() {
		return getActorId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value14() {
		return getActorDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value1(Timestamp value) {
		setCreateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value2(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value3(Long value) {
		setCreateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value4(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value5(String value) {
		setCreateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value6(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value7(Long value) {
		setId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value8(Long value) {
		setTreatmentId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value9(BigDecimal value) {
		setAmount(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value10(Boolean value) {
		setFinished(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value11(Timestamp value) {
		setDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value12(String value) {
		setComment(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value13(Long value) {
		setActorId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord value14(Timestamp value) {
		setActorDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TreatmenteventRecord values(Timestamp value1, Timestamp value2, Long value3, Long value4, String value5, String value6, Long value7, Long value8, BigDecimal value9, Boolean value10, Timestamp value11, String value12, Long value13, Timestamp value14) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		value6(value6);
		value7(value7);
		value8(value8);
		value9(value9);
		value10(value10);
		value11(value11);
		value12(value12);
		value13(value13);
		value14(value14);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached TreatmenteventRecord
	 */
	public TreatmenteventRecord() {
		super(Treatmentevent.TREATMENTEVENT);
	}

	/**
	 * Create a detached, initialised TreatmenteventRecord
	 */
	public TreatmenteventRecord(Timestamp createDate, Timestamp updateDate, Long createAccountId, Long updateAccountId, String createUi, String updateUi, Long id, Long treatmentId, BigDecimal amount, Boolean finished, Timestamp date, String comment, Long actorId, Timestamp actorDate) {
		super(Treatmentevent.TREATMENTEVENT);

		setValue(0, createDate);
		setValue(1, updateDate);
		setValue(2, createAccountId);
		setValue(3, updateAccountId);
		setValue(4, createUi);
		setValue(5, updateUi);
		setValue(6, id);
		setValue(7, treatmentId);
		setValue(8, amount);
		setValue(9, finished);
		setValue(10, date);
		setValue(11, comment);
		setValue(12, actorId);
		setValue(13, actorDate);
	}
}
