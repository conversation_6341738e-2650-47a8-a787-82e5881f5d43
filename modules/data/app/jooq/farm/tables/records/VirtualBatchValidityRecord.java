/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import java.sql.Date;
import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.tables.VirtualBatchValidity;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class VirtualBatchValidityRecord extends UpdatableRecordImpl<VirtualBatchValidityRecord> implements Record11<Timestamp, Timestamp, Long, Long, String, String, Long, Long, Long, Date, Date> {

	private static final long serialVersionUID = -1124626017;

	/**
	 * Setter for <code>farm.virtual_batch_validity.create_date</code>.
	 */
	public void setCreateDate(Timestamp value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.virtual_batch_validity.create_date</code>.
	 */
	public Timestamp getCreateDate() {
		return (Timestamp) getValue(0);
	}

	/**
	 * Setter for <code>farm.virtual_batch_validity.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.virtual_batch_validity.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(1);
	}

	/**
	 * Setter for <code>farm.virtual_batch_validity.create_account_id</code>.
	 */
	public void setCreateAccountId(Long value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.virtual_batch_validity.create_account_id</code>.
	 */
	public Long getCreateAccountId() {
		return (Long) getValue(2);
	}

	/**
	 * Setter for <code>farm.virtual_batch_validity.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.virtual_batch_validity.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(3);
	}

	/**
	 * Setter for <code>farm.virtual_batch_validity.create_ui</code>.
	 */
	public void setCreateUi(String value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>farm.virtual_batch_validity.create_ui</code>.
	 */
	public String getCreateUi() {
		return (String) getValue(4);
	}

	/**
	 * Setter for <code>farm.virtual_batch_validity.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(5, value);
	}

	/**
	 * Getter for <code>farm.virtual_batch_validity.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(5);
	}

	/**
	 * Setter for <code>farm.virtual_batch_validity.id</code>.
	 */
	public void setId(Long value) {
		setValue(6, value);
	}

	/**
	 * Getter for <code>farm.virtual_batch_validity.id</code>.
	 */
	public Long getId() {
		return (Long) getValue(6);
	}

	/**
	 * Setter for <code>farm.virtual_batch_validity.virtual_batch_id</code>.
	 */
	public void setVirtualBatchId(Long value) {
		setValue(7, value);
	}

	/**
	 * Getter for <code>farm.virtual_batch_validity.virtual_batch_id</code>.
	 */
	public Long getVirtualBatchId() {
		return (Long) getValue(7);
	}

	/**
	 * Setter for <code>farm.virtual_batch_validity.location_id</code>.
	 */
	public void setLocationId(Long value) {
		setValue(8, value);
	}

	/**
	 * Getter for <code>farm.virtual_batch_validity.location_id</code>.
	 */
	public Long getLocationId() {
		return (Long) getValue(8);
	}

	/**
	 * Setter for <code>farm.virtual_batch_validity.valid_from</code>.
	 */
	public void setValidFrom(Date value) {
		setValue(9, value);
	}

	/**
	 * Getter for <code>farm.virtual_batch_validity.valid_from</code>.
	 */
	public Date getValidFrom() {
		return (Date) getValue(9);
	}

	/**
	 * Setter for <code>farm.virtual_batch_validity.valid_to</code>.
	 */
	public void setValidTo(Date value) {
		setValue(10, value);
	}

	/**
	 * Getter for <code>farm.virtual_batch_validity.valid_to</code>.
	 */
	public Date getValidTo() {
		return (Date) getValue(10);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record1<Long> key() {
		return (Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record11 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row11<Timestamp, Timestamp, Long, Long, String, String, Long, Long, Long, Date, Date> fieldsRow() {
		return (Row11) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row11<Timestamp, Timestamp, Long, Long, String, String, Long, Long, Long, Date, Date> valuesRow() {
		return (Row11) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field1() {
		return VirtualBatchValidity.VIRTUAL_BATCH_VALIDITY.CREATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field2() {
		return VirtualBatchValidity.VIRTUAL_BATCH_VALIDITY.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field3() {
		return VirtualBatchValidity.VIRTUAL_BATCH_VALIDITY.CREATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field4() {
		return VirtualBatchValidity.VIRTUAL_BATCH_VALIDITY.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field5() {
		return VirtualBatchValidity.VIRTUAL_BATCH_VALIDITY.CREATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field6() {
		return VirtualBatchValidity.VIRTUAL_BATCH_VALIDITY.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field7() {
		return VirtualBatchValidity.VIRTUAL_BATCH_VALIDITY.ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field8() {
		return VirtualBatchValidity.VIRTUAL_BATCH_VALIDITY.VIRTUAL_BATCH_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field9() {
		return VirtualBatchValidity.VIRTUAL_BATCH_VALIDITY.LOCATION_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Date> field10() {
		return VirtualBatchValidity.VIRTUAL_BATCH_VALIDITY.VALID_FROM;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Date> field11() {
		return VirtualBatchValidity.VIRTUAL_BATCH_VALIDITY.VALID_TO;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value1() {
		return getCreateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value2() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value3() {
		return getCreateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value4() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value5() {
		return getCreateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value6() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value7() {
		return getId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value8() {
		return getVirtualBatchId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value9() {
		return getLocationId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Date value10() {
		return getValidFrom();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Date value11() {
		return getValidTo();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public VirtualBatchValidityRecord value1(Timestamp value) {
		setCreateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public VirtualBatchValidityRecord value2(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public VirtualBatchValidityRecord value3(Long value) {
		setCreateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public VirtualBatchValidityRecord value4(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public VirtualBatchValidityRecord value5(String value) {
		setCreateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public VirtualBatchValidityRecord value6(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public VirtualBatchValidityRecord value7(Long value) {
		setId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public VirtualBatchValidityRecord value8(Long value) {
		setVirtualBatchId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public VirtualBatchValidityRecord value9(Long value) {
		setLocationId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public VirtualBatchValidityRecord value10(Date value) {
		setValidFrom(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public VirtualBatchValidityRecord value11(Date value) {
		setValidTo(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public VirtualBatchValidityRecord values(Timestamp value1, Timestamp value2, Long value3, Long value4, String value5, String value6, Long value7, Long value8, Long value9, Date value10, Date value11) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		value6(value6);
		value7(value7);
		value8(value8);
		value9(value9);
		value10(value10);
		value11(value11);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached VirtualBatchValidityRecord
	 */
	public VirtualBatchValidityRecord() {
		super(VirtualBatchValidity.VIRTUAL_BATCH_VALIDITY);
	}

	/**
	 * Create a detached, initialised VirtualBatchValidityRecord
	 */
	public VirtualBatchValidityRecord(Timestamp createDate, Timestamp updateDate, Long createAccountId, Long updateAccountId, String createUi, String updateUi, Long id, Long virtualBatchId, Long locationId, Date validFrom, Date validTo) {
		super(VirtualBatchValidity.VIRTUAL_BATCH_VALIDITY);

		setValue(0, createDate);
		setValue(1, updateDate);
		setValue(2, createAccountId);
		setValue(3, updateAccountId);
		setValue(4, createUi);
		setValue(5, updateUi);
		setValue(6, id);
		setValue(7, virtualBatchId);
		setValue(8, locationId);
		setValue(9, validFrom);
		setValue(10, validTo);
	}
}
