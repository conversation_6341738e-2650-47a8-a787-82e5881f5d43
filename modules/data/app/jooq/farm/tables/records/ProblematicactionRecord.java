/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.tables.Problematicaction;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record18;
import org.jooq.Row18;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ProblematicactionRecord extends UpdatableRecordImpl<ProblematicactionRecord> implements Record18<Timestamp, Timestamp, Long, Long, String, String, Long, String, Long, Long, Timestamp, Boolean, Boolean, String, String, Integer, Boolean, Integer> {

	private static final long serialVersionUID = 1214258463;

	/**
	 * Setter for <code>farm.problematicaction.create_date</code>.
	 */
	public void setCreateDate(Timestamp value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.create_date</code>.
	 */
	public Timestamp getCreateDate() {
		return (Timestamp) getValue(0);
	}

	/**
	 * Setter for <code>farm.problematicaction.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(1);
	}

	/**
	 * Setter for <code>farm.problematicaction.create_account_id</code>.
	 */
	public void setCreateAccountId(Long value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.create_account_id</code>.
	 */
	public Long getCreateAccountId() {
		return (Long) getValue(2);
	}

	/**
	 * Setter for <code>farm.problematicaction.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(3);
	}

	/**
	 * Setter for <code>farm.problematicaction.create_ui</code>.
	 */
	public void setCreateUi(String value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.create_ui</code>.
	 */
	public String getCreateUi() {
		return (String) getValue(4);
	}

	/**
	 * Setter for <code>farm.problematicaction.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(5, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(5);
	}

	/**
	 * Setter for <code>farm.problematicaction.id</code>.
	 */
	public void setId(Long value) {
		setValue(6, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.id</code>.
	 */
	public Long getId() {
		return (Long) getValue(6);
	}

	/**
	 * Setter for <code>farm.problematicaction.actiontype</code>.
	 */
	public void setActiontype(String value) {
		setValue(7, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.actiontype</code>.
	 */
	public String getActiontype() {
		return (String) getValue(7);
	}

	/**
	 * Setter for <code>farm.problematicaction.device_id</code>.
	 */
	public void setDeviceId(Long value) {
		setValue(8, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.device_id</code>.
	 */
	public Long getDeviceId() {
		return (Long) getValue(8);
	}

	/**
	 * Setter for <code>farm.problematicaction.actor_id</code>.
	 */
	public void setActorId(Long value) {
		setValue(9, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.actor_id</code>.
	 */
	public Long getActorId() {
		return (Long) getValue(9);
	}

	/**
	 * Setter for <code>farm.problematicaction.actor_date</code>.
	 */
	public void setActorDate(Timestamp value) {
		setValue(10, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.actor_date</code>.
	 */
	public Timestamp getActorDate() {
		return (Timestamp) getValue(10);
	}

	/**
	 * Setter for <code>farm.problematicaction.rejected</code>.
	 */
	public void setRejected(Boolean value) {
		setValue(11, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.rejected</code>.
	 */
	public Boolean getRejected() {
		return (Boolean) getValue(11);
	}

	/**
	 * Setter for <code>farm.problematicaction.reviewed</code>.
	 */
	public void setReviewed(Boolean value) {
		setValue(12, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.reviewed</code>.
	 */
	public Boolean getReviewed() {
		return (Boolean) getValue(12);
	}

	/**
	 * Setter for <code>farm.problematicaction.dto</code>.
	 */
	public void setDto(String value) {
		setValue(13, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.dto</code>.
	 */
	public String getDto() {
		return (String) getValue(13);
	}

	/**
	 * Setter for <code>farm.problematicaction.reason</code>.
	 */
	public void setReason(String value) {
		setValue(14, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.reason</code>.
	 */
	public String getReason() {
		return (String) getValue(14);
	}

	/**
	 * Setter for <code>farm.problematicaction.appversion</code>.
	 */
	public void setAppversion(Integer value) {
		setValue(15, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.appversion</code>.
	 */
	public Integer getAppversion() {
		return (Integer) getValue(15);
	}

	/**
	 * Setter for <code>farm.problematicaction.witheventnum</code>.
	 */
	public void setWitheventnum(Boolean value) {
		setValue(16, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.witheventnum</code>.
	 */
	public Boolean getWitheventnum() {
		return (Boolean) getValue(16);
	}

	/**
	 * Setter for <code>farm.problematicaction.file_id</code>.
	 */
	public void setFileId(Integer value) {
		setValue(17, value);
	}

	/**
	 * Getter for <code>farm.problematicaction.file_id</code>.
	 */
	public Integer getFileId() {
		return (Integer) getValue(17);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record1<Long> key() {
		return (Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record18 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row18<Timestamp, Timestamp, Long, Long, String, String, Long, String, Long, Long, Timestamp, Boolean, Boolean, String, String, Integer, Boolean, Integer> fieldsRow() {
		return (Row18) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row18<Timestamp, Timestamp, Long, Long, String, String, Long, String, Long, Long, Timestamp, Boolean, Boolean, String, String, Integer, Boolean, Integer> valuesRow() {
		return (Row18) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field1() {
		return Problematicaction.PROBLEMATICACTION.CREATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field2() {
		return Problematicaction.PROBLEMATICACTION.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field3() {
		return Problematicaction.PROBLEMATICACTION.CREATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field4() {
		return Problematicaction.PROBLEMATICACTION.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field5() {
		return Problematicaction.PROBLEMATICACTION.CREATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field6() {
		return Problematicaction.PROBLEMATICACTION.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field7() {
		return Problematicaction.PROBLEMATICACTION.ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field8() {
		return Problematicaction.PROBLEMATICACTION.ACTIONTYPE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field9() {
		return Problematicaction.PROBLEMATICACTION.DEVICE_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field10() {
		return Problematicaction.PROBLEMATICACTION.ACTOR_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field11() {
		return Problematicaction.PROBLEMATICACTION.ACTOR_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Boolean> field12() {
		return Problematicaction.PROBLEMATICACTION.REJECTED;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Boolean> field13() {
		return Problematicaction.PROBLEMATICACTION.REVIEWED;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field14() {
		return Problematicaction.PROBLEMATICACTION.DTO;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field15() {
		return Problematicaction.PROBLEMATICACTION.REASON;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Integer> field16() {
		return Problematicaction.PROBLEMATICACTION.APPVERSION;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Boolean> field17() {
		return Problematicaction.PROBLEMATICACTION.WITHEVENTNUM;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Integer> field18() {
		return Problematicaction.PROBLEMATICACTION.FILE_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value1() {
		return getCreateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value2() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value3() {
		return getCreateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value4() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value5() {
		return getCreateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value6() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value7() {
		return getId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value8() {
		return getActiontype();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value9() {
		return getDeviceId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value10() {
		return getActorId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value11() {
		return getActorDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Boolean value12() {
		return getRejected();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Boolean value13() {
		return getReviewed();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value14() {
		return getDto();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value15() {
		return getReason();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Integer value16() {
		return getAppversion();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Boolean value17() {
		return getWitheventnum();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Integer value18() {
		return getFileId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value1(Timestamp value) {
		setCreateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value2(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value3(Long value) {
		setCreateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value4(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value5(String value) {
		setCreateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value6(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value7(Long value) {
		setId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value8(String value) {
		setActiontype(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value9(Long value) {
		setDeviceId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value10(Long value) {
		setActorId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value11(Timestamp value) {
		setActorDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value12(Boolean value) {
		setRejected(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value13(Boolean value) {
		setReviewed(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value14(String value) {
		setDto(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value15(String value) {
		setReason(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value16(Integer value) {
		setAppversion(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value17(Boolean value) {
		setWitheventnum(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord value18(Integer value) {
		setFileId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public ProblematicactionRecord values(Timestamp value1, Timestamp value2, Long value3, Long value4, String value5, String value6, Long value7, String value8, Long value9, Long value10, Timestamp value11, Boolean value12, Boolean value13, String value14, String value15, Integer value16, Boolean value17, Integer value18) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		value6(value6);
		value7(value7);
		value8(value8);
		value9(value9);
		value10(value10);
		value11(value11);
		value12(value12);
		value13(value13);
		value14(value14);
		value15(value15);
		value16(value16);
		value17(value17);
		value18(value18);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached ProblematicactionRecord
	 */
	public ProblematicactionRecord() {
		super(Problematicaction.PROBLEMATICACTION);
	}

	/**
	 * Create a detached, initialised ProblematicactionRecord
	 */
	public ProblematicactionRecord(Timestamp createDate, Timestamp updateDate, Long createAccountId, Long updateAccountId, String createUi, String updateUi, Long id, String actiontype, Long deviceId, Long actorId, Timestamp actorDate, Boolean rejected, Boolean reviewed, String dto, String reason, Integer appversion, Boolean witheventnum, Integer fileId) {
		super(Problematicaction.PROBLEMATICACTION);

		setValue(0, createDate);
		setValue(1, updateDate);
		setValue(2, createAccountId);
		setValue(3, updateAccountId);
		setValue(4, createUi);
		setValue(5, updateUi);
		setValue(6, id);
		setValue(7, actiontype);
		setValue(8, deviceId);
		setValue(9, actorId);
		setValue(10, actorDate);
		setValue(11, rejected);
		setValue(12, reviewed);
		setValue(13, dto);
		setValue(14, reason);
		setValue(15, appversion);
		setValue(16, witheventnum);
		setValue(17, fileId);
	}
}
