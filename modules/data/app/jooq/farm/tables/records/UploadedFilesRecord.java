/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import com.fasterxml.jackson.databind.JsonNode;

import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.tables.UploadedFiles;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record15;
import org.jooq.Row15;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class UploadedFilesRecord extends UpdatableRecordImpl<UploadedFilesRecord> implements Record15<Integer, String, Long, String, String, String, String, Long, Timestamp, Timestamp, Long, Long, String, String, JsonNode> {

	private static final long serialVersionUID = -498878032;

	/**
	 * Setter for <code>farm.uploaded_files.id</code>.
	 */
	public void setId(Integer value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.id</code>.
	 */
	public Integer getId() {
		return (Integer) getValue(0);
	}

	/**
	 * Setter for <code>farm.uploaded_files.filename</code>.
	 */
	public void setFilename(String value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.filename</code>.
	 */
	public String getFilename() {
		return (String) getValue(1);
	}

	/**
	 * Setter for <code>farm.uploaded_files.filesize</code>.
	 */
	public void setFilesize(Long value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.filesize</code>.
	 */
	public Long getFilesize() {
		return (Long) getValue(2);
	}

	/**
	 * Setter for <code>farm.uploaded_files.filepath</code>.
	 */
	public void setFilepath(String value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.filepath</code>.
	 */
	public String getFilepath() {
		return (String) getValue(3);
	}

	/**
	 * Setter for <code>farm.uploaded_files.checksum</code>.
	 */
	public void setChecksum(String value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.checksum</code>.
	 */
	public String getChecksum() {
		return (String) getValue(4);
	}

	/**
	 * Setter for <code>farm.uploaded_files.storage_type</code>.
	 */
	public void setStorageType(String value) {
		setValue(5, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.storage_type</code>.
	 */
	public String getStorageType() {
		return (String) getValue(5);
	}

	/**
	 * Setter for <code>farm.uploaded_files.source_type</code>.
	 */
	public void setSourceType(String value) {
		setValue(6, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.source_type</code>.
	 */
	public String getSourceType() {
		return (String) getValue(6);
	}

	/**
	 * Setter for <code>farm.uploaded_files.actor_id</code>.
	 */
	public void setActorId(Long value) {
		setValue(7, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.actor_id</code>.
	 */
	public Long getActorId() {
		return (Long) getValue(7);
	}

	/**
	 * Setter for <code>farm.uploaded_files.create_date</code>.
	 */
	public void setCreateDate(Timestamp value) {
		setValue(8, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.create_date</code>.
	 */
	public Timestamp getCreateDate() {
		return (Timestamp) getValue(8);
	}

	/**
	 * Setter for <code>farm.uploaded_files.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(9, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(9);
	}

	/**
	 * Setter for <code>farm.uploaded_files.create_account_id</code>.
	 */
	public void setCreateAccountId(Long value) {
		setValue(10, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.create_account_id</code>.
	 */
	public Long getCreateAccountId() {
		return (Long) getValue(10);
	}

	/**
	 * Setter for <code>farm.uploaded_files.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(11, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(11);
	}

	/**
	 * Setter for <code>farm.uploaded_files.create_ui</code>.
	 */
	public void setCreateUi(String value) {
		setValue(12, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.create_ui</code>.
	 */
	public String getCreateUi() {
		return (String) getValue(12);
	}

	/**
	 * Setter for <code>farm.uploaded_files.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(13, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(13);
	}

	/**
	 * Setter for <code>farm.uploaded_files.metadata</code>.
	 */
	public void setMetadata(JsonNode value) {
		setValue(14, value);
	}

	/**
	 * Getter for <code>farm.uploaded_files.metadata</code>.
	 */
	public JsonNode getMetadata() {
		return (JsonNode) getValue(14);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record1<Integer> key() {
		return (Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record15 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row15<Integer, String, Long, String, String, String, String, Long, Timestamp, Timestamp, Long, Long, String, String, JsonNode> fieldsRow() {
		return (Row15) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row15<Integer, String, Long, String, String, String, String, Long, Timestamp, Timestamp, Long, Long, String, String, JsonNode> valuesRow() {
		return (Row15) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Integer> field1() {
		return UploadedFiles.UPLOADED_FILES.ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field2() {
		return UploadedFiles.UPLOADED_FILES.FILENAME;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field3() {
		return UploadedFiles.UPLOADED_FILES.FILESIZE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field4() {
		return UploadedFiles.UPLOADED_FILES.FILEPATH;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field5() {
		return UploadedFiles.UPLOADED_FILES.CHECKSUM;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field6() {
		return UploadedFiles.UPLOADED_FILES.STORAGE_TYPE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field7() {
		return UploadedFiles.UPLOADED_FILES.SOURCE_TYPE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field8() {
		return UploadedFiles.UPLOADED_FILES.ACTOR_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field9() {
		return UploadedFiles.UPLOADED_FILES.CREATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field10() {
		return UploadedFiles.UPLOADED_FILES.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field11() {
		return UploadedFiles.UPLOADED_FILES.CREATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field12() {
		return UploadedFiles.UPLOADED_FILES.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field13() {
		return UploadedFiles.UPLOADED_FILES.CREATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field14() {
		return UploadedFiles.UPLOADED_FILES.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<JsonNode> field15() {
		return UploadedFiles.UPLOADED_FILES.METADATA;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Integer value1() {
		return getId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value2() {
		return getFilename();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value3() {
		return getFilesize();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value4() {
		return getFilepath();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value5() {
		return getChecksum();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value6() {
		return getStorageType();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value7() {
		return getSourceType();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value8() {
		return getActorId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value9() {
		return getCreateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value10() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value11() {
		return getCreateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value12() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value13() {
		return getCreateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value14() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public JsonNode value15() {
		return getMetadata();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value1(Integer value) {
		setId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value2(String value) {
		setFilename(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value3(Long value) {
		setFilesize(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value4(String value) {
		setFilepath(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value5(String value) {
		setChecksum(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value6(String value) {
		setStorageType(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value7(String value) {
		setSourceType(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value8(Long value) {
		setActorId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value9(Timestamp value) {
		setCreateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value10(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value11(Long value) {
		setCreateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value12(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value13(String value) {
		setCreateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value14(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord value15(JsonNode value) {
		setMetadata(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UploadedFilesRecord values(Integer value1, String value2, Long value3, String value4, String value5, String value6, String value7, Long value8, Timestamp value9, Timestamp value10, Long value11, Long value12, String value13, String value14, JsonNode value15) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		value6(value6);
		value7(value7);
		value8(value8);
		value9(value9);
		value10(value10);
		value11(value11);
		value12(value12);
		value13(value13);
		value14(value14);
		value15(value15);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached UploadedFilesRecord
	 */
	public UploadedFilesRecord() {
		super(UploadedFiles.UPLOADED_FILES);
	}

	/**
	 * Create a detached, initialised UploadedFilesRecord
	 */
	public UploadedFilesRecord(Integer id, String filename, Long filesize, String filepath, String checksum, String storageType, String sourceType, Long actorId, Timestamp createDate, Timestamp updateDate, Long createAccountId, Long updateAccountId, String createUi, String updateUi, JsonNode metadata) {
		super(UploadedFiles.UPLOADED_FILES);

		setValue(0, id);
		setValue(1, filename);
		setValue(2, filesize);
		setValue(3, filepath);
		setValue(4, checksum);
		setValue(5, storageType);
		setValue(6, sourceType);
		setValue(7, actorId);
		setValue(8, createDate);
		setValue(9, updateDate);
		setValue(10, createAccountId);
		setValue(11, updateAccountId);
		setValue(12, createUi);
		setValue(13, updateUi);
		setValue(14, metadata);
	}
}
