/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import java.sql.Date;

import javax.annotation.Generated;

import jooq.farm.tables.BreedRuleView;

import org.jooq.Field;
import org.jooq.Record13;
import org.jooq.Row13;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class BreedRuleViewRecord extends TableRecordImpl<BreedRuleViewRecord> implements Record13<String, String, String, <PERSON><PERSON>, String, In<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ole<PERSON>, Integer, Integer, Date, Short, String> {

	private static final long serialVersionUID = 1293446238;

	/**
	 * Setter for <code>farm.breed_rule_view.sire_breed</code>.
	 */
	public void setSireBreed(String value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.breed_rule_view.sire_breed</code>.
	 */
	public String getSireBreed() {
		return (String) getValue(0);
	}

	/**
	 * Setter for <code>farm.breed_rule_view.dam_breed</code>.
	 */
	public void setDamBreed(String value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.breed_rule_view.dam_breed</code>.
	 */
	public String getDamBreed() {
		return (String) getValue(1);
	}

	/**
	 * Setter for <code>farm.breed_rule_view.progeny_breed</code>.
	 */
	public void setProgenyBreed(String value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.breed_rule_view.progeny_breed</code>.
	 */
	public String getProgenyBreed() {
		return (String) getValue(2);
	}

	/**
	 * Setter for <code>farm.breed_rule_view.pure_breed</code>.
	 */
	public void setPureBreed(Boolean value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.breed_rule_view.pure_breed</code>.
	 */
	public Boolean getPureBreed() {
		return (Boolean) getValue(3);
	}

	/**
	 * Setter for <code>farm.breed_rule_view.farm_number</code>.
	 */
	public void setFarmNumber(String value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>farm.breed_rule_view.farm_number</code>.
	 */
	public String getFarmNumber() {
		return (String) getValue(4);
	}

	/**
	 * Setter for <code>farm.breed_rule_view.enforce_breeding_entry</code>.
	 */
	public void setEnforceBreedingEntry(Integer value) {
		setValue(5, value);
	}

	/**
	 * Getter for <code>farm.breed_rule_view.enforce_breeding_entry</code>.
	 */
	public Integer getEnforceBreedingEntry() {
		return (Integer) getValue(5);
	}

	/**
	 * Setter for <code>farm.breed_rule_view.send_danavl</code>.
	 */
	public void setSendDanavl(Boolean value) {
		setValue(6, value);
	}

	/**
	 * Getter for <code>farm.breed_rule_view.send_danavl</code>.
	 */
	public Boolean getSendDanavl() {
		return (Boolean) getValue(6);
	}

	/**
	 * Setter for <code>farm.breed_rule_view.send_danavl_reproduction</code>.
	 */
	public void setSendDanavlReproduction(Boolean value) {
		setValue(7, value);
	}

	/**
	 * Getter for <code>farm.breed_rule_view.send_danavl_reproduction</code>.
	 */
	public Boolean getSendDanavlReproduction() {
		return (Boolean) getValue(7);
	}

	/**
	 * Setter for <code>farm.breed_rule_view.small_number_to</code>.
	 */
	public void setSmallNumberTo(Integer value) {
		setValue(8, value);
	}

	/**
	 * Getter for <code>farm.breed_rule_view.small_number_to</code>.
	 */
	public Integer getSmallNumberTo() {
		return (Integer) getValue(8);
	}

	/**
	 * Setter for <code>farm.breed_rule_view.small_number_from</code>.
	 */
	public void setSmallNumberFrom(Integer value) {
		setValue(9, value);
	}

	/**
	 * Getter for <code>farm.breed_rule_view.small_number_from</code>.
	 */
	public Integer getSmallNumberFrom() {
		return (Integer) getValue(9);
	}

	/**
	 * Setter for <code>farm.breed_rule_view.write_access</code>.
	 */
	public void setWriteAccess(Date value) {
		setValue(10, value);
	}

	/**
	 * Getter for <code>farm.breed_rule_view.write_access</code>.
	 */
	public Date getWriteAccess() {
		return (Date) getValue(10);
	}

	/**
	 * Setter for <code>farm.breed_rule_view.usecode</code>.
	 */
	public void setUsecode(Short value) {
		setValue(11, value);
	}

	/**
	 * Getter for <code>farm.breed_rule_view.usecode</code>.
	 */
	public Short getUsecode() {
		return (Short) getValue(11);
	}

	/**
	 * Setter for <code>farm.breed_rule_view.id_farm_number</code>.
	 */
	public void setIdFarmNumber(String value) {
		setValue(12, value);
	}

	/**
	 * Getter for <code>farm.breed_rule_view.id_farm_number</code>.
	 */
	public String getIdFarmNumber() {
		return (String) getValue(12);
	}

	// -------------------------------------------------------------------------
	// Record13 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row13<String, String, String, Boolean, String, Integer, Boolean, Boolean, Integer, Integer, Date, Short, String> fieldsRow() {
		return (Row13) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row13<String, String, String, Boolean, String, Integer, Boolean, Boolean, Integer, Integer, Date, Short, String> valuesRow() {
		return (Row13) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field1() {
		return BreedRuleView.BREED_RULE_VIEW.SIRE_BREED;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field2() {
		return BreedRuleView.BREED_RULE_VIEW.DAM_BREED;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field3() {
		return BreedRuleView.BREED_RULE_VIEW.PROGENY_BREED;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Boolean> field4() {
		return BreedRuleView.BREED_RULE_VIEW.PURE_BREED;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field5() {
		return BreedRuleView.BREED_RULE_VIEW.FARM_NUMBER;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Integer> field6() {
		return BreedRuleView.BREED_RULE_VIEW.ENFORCE_BREEDING_ENTRY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Boolean> field7() {
		return BreedRuleView.BREED_RULE_VIEW.SEND_DANAVL;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Boolean> field8() {
		return BreedRuleView.BREED_RULE_VIEW.SEND_DANAVL_REPRODUCTION;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Integer> field9() {
		return BreedRuleView.BREED_RULE_VIEW.SMALL_NUMBER_TO;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Integer> field10() {
		return BreedRuleView.BREED_RULE_VIEW.SMALL_NUMBER_FROM;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Date> field11() {
		return BreedRuleView.BREED_RULE_VIEW.WRITE_ACCESS;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Short> field12() {
		return BreedRuleView.BREED_RULE_VIEW.USECODE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field13() {
		return BreedRuleView.BREED_RULE_VIEW.ID_FARM_NUMBER;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value1() {
		return getSireBreed();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value2() {
		return getDamBreed();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value3() {
		return getProgenyBreed();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Boolean value4() {
		return getPureBreed();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value5() {
		return getFarmNumber();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Integer value6() {
		return getEnforceBreedingEntry();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Boolean value7() {
		return getSendDanavl();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Boolean value8() {
		return getSendDanavlReproduction();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Integer value9() {
		return getSmallNumberTo();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Integer value10() {
		return getSmallNumberFrom();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Date value11() {
		return getWriteAccess();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Short value12() {
		return getUsecode();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value13() {
		return getIdFarmNumber();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord value1(String value) {
		setSireBreed(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord value2(String value) {
		setDamBreed(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord value3(String value) {
		setProgenyBreed(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord value4(Boolean value) {
		setPureBreed(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord value5(String value) {
		setFarmNumber(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord value6(Integer value) {
		setEnforceBreedingEntry(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord value7(Boolean value) {
		setSendDanavl(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord value8(Boolean value) {
		setSendDanavlReproduction(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord value9(Integer value) {
		setSmallNumberTo(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord value10(Integer value) {
		setSmallNumberFrom(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord value11(Date value) {
		setWriteAccess(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord value12(Short value) {
		setUsecode(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord value13(String value) {
		setIdFarmNumber(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BreedRuleViewRecord values(String value1, String value2, String value3, Boolean value4, String value5, Integer value6, Boolean value7, Boolean value8, Integer value9, Integer value10, Date value11, Short value12, String value13) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		value6(value6);
		value7(value7);
		value8(value8);
		value9(value9);
		value10(value10);
		value11(value11);
		value12(value12);
		value13(value13);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached BreedRuleViewRecord
	 */
	public BreedRuleViewRecord() {
		super(BreedRuleView.BREED_RULE_VIEW);
	}

	/**
	 * Create a detached, initialised BreedRuleViewRecord
	 */
	public BreedRuleViewRecord(String sireBreed, String damBreed, String progenyBreed, Boolean pureBreed, String farmNumber, Integer enforceBreedingEntry, Boolean sendDanavl, Boolean sendDanavlReproduction, Integer smallNumberTo, Integer smallNumberFrom, Date writeAccess, Short usecode, String idFarmNumber) {
		super(BreedRuleView.BREED_RULE_VIEW);

		setValue(0, sireBreed);
		setValue(1, damBreed);
		setValue(2, progenyBreed);
		setValue(3, pureBreed);
		setValue(4, farmNumber);
		setValue(5, enforceBreedingEntry);
		setValue(6, sendDanavl);
		setValue(7, sendDanavlReproduction);
		setValue(8, smallNumberTo);
		setValue(9, smallNumberFrom);
		setValue(10, writeAccess);
		setValue(11, usecode);
		setValue(12, idFarmNumber);
	}
}
