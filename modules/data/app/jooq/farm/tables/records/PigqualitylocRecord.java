/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import java.math.BigDecimal;
import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.tables.Pigqualityloc;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record21;
import org.jooq.Row21;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * Contains all the quality of the pigs (weaners/fatteners)
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class PigqualitylocRecord extends UpdatableRecordImpl<PigqualitylocRecord> implements Record21<Timestamp, Timestamp, Long, Long, String, String, Long, Long, String, Integer, BigDecimal, BigDecimal, BigDecimal, String, Integer, String, BigDecimal, <PERSON>, <PERSON>, BigDecimal, Integer> {

	private static final long serialVersionUID = 1219330954;

	/**
	 * Setter for <code>farm.pigqualityloc.create_date</code>.
	 */
	public void setCreateDate(Timestamp value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.create_date</code>.
	 */
	public Timestamp getCreateDate() {
		return (Timestamp) getValue(0);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(1);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.create_account_id</code>.
	 */
	public void setCreateAccountId(Long value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.create_account_id</code>.
	 */
	public Long getCreateAccountId() {
		return (Long) getValue(2);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(3);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.create_ui</code>.
	 */
	public void setCreateUi(String value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.create_ui</code>.
	 */
	public String getCreateUi() {
		return (String) getValue(4);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(5, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(5);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.id</code>.
	 */
	public void setId(Long value) {
		setValue(6, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.id</code>.
	 */
	public Long getId() {
		return (Long) getValue(6);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.transferloc_id</code>.
	 */
	public void setTransferlocId(Long value) {
		setValue(7, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.transferloc_id</code>.
	 */
	public Long getTransferlocId() {
		return (Long) getValue(7);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.pigqualitytype_code</code>.
	 */
	public void setPigqualitytypeCode(String value) {
		setValue(8, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.pigqualitytype_code</code>.
	 */
	public String getPigqualitytypeCode() {
		return (String) getValue(8);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.pigamount</code>. Number of the pigs transfered
	 */
	public void setPigamount(Integer value) {
		setValue(9, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.pigamount</code>. Number of the pigs transfered
	 */
	public Integer getPigamount() {
		return (Integer) getValue(9);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.pigweight</code>. Weight of the pigs transfered
	 */
	public void setPigweight(BigDecimal value) {
		setValue(10, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.pigweight</code>. Weight of the pigs transfered
	 */
	public BigDecimal getPigweight() {
		return (BigDecimal) getValue(10);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.pigliveweight</code>.
	 */
	public void setPigliveweight(BigDecimal value) {
		setValue(11, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.pigliveweight</code>.
	 */
	public BigDecimal getPigliveweight() {
		return (BigDecimal) getValue(11);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.leanpercent</code>.
	 */
	public void setLeanpercent(BigDecimal value) {
		setValue(12, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.leanpercent</code>.
	 */
	public BigDecimal getLeanpercent() {
		return (BigDecimal) getValue(12);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.comment</code>.
	 */
	public void setComment(String value) {
		setValue(13, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.comment</code>.
	 */
	public String getComment() {
		return (String) getValue(13);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.deadamount</code>. Weight of rejected pigs in a transfer. For example pig dead on the way
	 */
	public void setDeadamount(Integer value) {
		setValue(14, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.deadamount</code>. Weight of rejected pigs in a transfer. For example pig dead on the way
	 */
	public Integer getDeadamount() {
		return (Integer) getValue(14);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.pig_centralrn</code>.
	 */
	public void setPigCentralrn(String value) {
		setValue(15, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.pig_centralrn</code>.
	 */
	public String getPigCentralrn() {
		return (String) getValue(15);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.deadweight</code>.
	 */
	public void setDeadweight(BigDecimal value) {
		setValue(16, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.deadweight</code>.
	 */
	public BigDecimal getDeadweight() {
		return (BigDecimal) getValue(16);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.fromlocation_id</code>.
	 */
	public void setFromlocationId(Long value) {
		setValue(17, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.fromlocation_id</code>.
	 */
	public Long getFromlocationId() {
		return (Long) getValue(17);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.tolocation_id</code>.
	 */
	public void setTolocationId(Long value) {
		setValue(18, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.tolocation_id</code>.
	 */
	public Long getTolocationId() {
		return (Long) getValue(18);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.price</code>.
	 */
	public void setPrice(BigDecimal value) {
		setValue(19, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.price</code>.
	 */
	public BigDecimal getPrice() {
		return (BigDecimal) getValue(19);
	}

	/**
	 * Setter for <code>farm.pigqualityloc.age</code>.
	 */
	public void setAge(Integer value) {
		setValue(20, value);
	}

	/**
	 * Getter for <code>farm.pigqualityloc.age</code>.
	 */
	public Integer getAge() {
		return (Integer) getValue(20);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record1<Long> key() {
		return (Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record21 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row21<Timestamp, Timestamp, Long, Long, String, String, Long, Long, String, Integer, BigDecimal, BigDecimal, BigDecimal, String, Integer, String, BigDecimal, Long, Long, BigDecimal, Integer> fieldsRow() {
		return (Row21) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row21<Timestamp, Timestamp, Long, Long, String, String, Long, Long, String, Integer, BigDecimal, BigDecimal, BigDecimal, String, Integer, String, BigDecimal, Long, Long, BigDecimal, Integer> valuesRow() {
		return (Row21) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field1() {
		return Pigqualityloc.PIGQUALITYLOC.CREATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field2() {
		return Pigqualityloc.PIGQUALITYLOC.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field3() {
		return Pigqualityloc.PIGQUALITYLOC.CREATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field4() {
		return Pigqualityloc.PIGQUALITYLOC.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field5() {
		return Pigqualityloc.PIGQUALITYLOC.CREATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field6() {
		return Pigqualityloc.PIGQUALITYLOC.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field7() {
		return Pigqualityloc.PIGQUALITYLOC.ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field8() {
		return Pigqualityloc.PIGQUALITYLOC.TRANSFERLOC_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field9() {
		return Pigqualityloc.PIGQUALITYLOC.PIGQUALITYTYPE_CODE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Integer> field10() {
		return Pigqualityloc.PIGQUALITYLOC.PIGAMOUNT;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<BigDecimal> field11() {
		return Pigqualityloc.PIGQUALITYLOC.PIGWEIGHT;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<BigDecimal> field12() {
		return Pigqualityloc.PIGQUALITYLOC.PIGLIVEWEIGHT;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<BigDecimal> field13() {
		return Pigqualityloc.PIGQUALITYLOC.LEANPERCENT;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field14() {
		return Pigqualityloc.PIGQUALITYLOC.COMMENT;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Integer> field15() {
		return Pigqualityloc.PIGQUALITYLOC.DEADAMOUNT;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field16() {
		return Pigqualityloc.PIGQUALITYLOC.PIG_CENTRALRN;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<BigDecimal> field17() {
		return Pigqualityloc.PIGQUALITYLOC.DEADWEIGHT;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field18() {
		return Pigqualityloc.PIGQUALITYLOC.FROMLOCATION_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field19() {
		return Pigqualityloc.PIGQUALITYLOC.TOLOCATION_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<BigDecimal> field20() {
		return Pigqualityloc.PIGQUALITYLOC.PRICE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Integer> field21() {
		return Pigqualityloc.PIGQUALITYLOC.AGE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value1() {
		return getCreateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value2() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value3() {
		return getCreateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value4() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value5() {
		return getCreateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value6() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value7() {
		return getId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value8() {
		return getTransferlocId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value9() {
		return getPigqualitytypeCode();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Integer value10() {
		return getPigamount();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BigDecimal value11() {
		return getPigweight();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BigDecimal value12() {
		return getPigliveweight();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BigDecimal value13() {
		return getLeanpercent();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value14() {
		return getComment();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Integer value15() {
		return getDeadamount();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value16() {
		return getPigCentralrn();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BigDecimal value17() {
		return getDeadweight();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value18() {
		return getFromlocationId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value19() {
		return getTolocationId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BigDecimal value20() {
		return getPrice();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Integer value21() {
		return getAge();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value1(Timestamp value) {
		setCreateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value2(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value3(Long value) {
		setCreateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value4(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value5(String value) {
		setCreateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value6(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value7(Long value) {
		setId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value8(Long value) {
		setTransferlocId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value9(String value) {
		setPigqualitytypeCode(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value10(Integer value) {
		setPigamount(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value11(BigDecimal value) {
		setPigweight(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value12(BigDecimal value) {
		setPigliveweight(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value13(BigDecimal value) {
		setLeanpercent(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value14(String value) {
		setComment(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value15(Integer value) {
		setDeadamount(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value16(String value) {
		setPigCentralrn(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value17(BigDecimal value) {
		setDeadweight(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value18(Long value) {
		setFromlocationId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value19(Long value) {
		setTolocationId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value20(BigDecimal value) {
		setPrice(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord value21(Integer value) {
		setAge(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public PigqualitylocRecord values(Timestamp value1, Timestamp value2, Long value3, Long value4, String value5, String value6, Long value7, Long value8, String value9, Integer value10, BigDecimal value11, BigDecimal value12, BigDecimal value13, String value14, Integer value15, String value16, BigDecimal value17, Long value18, Long value19, BigDecimal value20, Integer value21) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		value6(value6);
		value7(value7);
		value8(value8);
		value9(value9);
		value10(value10);
		value11(value11);
		value12(value12);
		value13(value13);
		value14(value14);
		value15(value15);
		value16(value16);
		value17(value17);
		value18(value18);
		value19(value19);
		value20(value20);
		value21(value21);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached PigqualitylocRecord
	 */
	public PigqualitylocRecord() {
		super(Pigqualityloc.PIGQUALITYLOC);
	}

	/**
	 * Create a detached, initialised PigqualitylocRecord
	 */
	public PigqualitylocRecord(Timestamp createDate, Timestamp updateDate, Long createAccountId, Long updateAccountId, String createUi, String updateUi, Long id, Long transferlocId, String pigqualitytypeCode, Integer pigamount, BigDecimal pigweight, BigDecimal pigliveweight, BigDecimal leanpercent, String comment, Integer deadamount, String pigCentralrn, BigDecimal deadweight, Long fromlocationId, Long tolocationId, BigDecimal price, Integer age) {
		super(Pigqualityloc.PIGQUALITYLOC);

		setValue(0, createDate);
		setValue(1, updateDate);
		setValue(2, createAccountId);
		setValue(3, updateAccountId);
		setValue(4, createUi);
		setValue(5, updateUi);
		setValue(6, id);
		setValue(7, transferlocId);
		setValue(8, pigqualitytypeCode);
		setValue(9, pigamount);
		setValue(10, pigweight);
		setValue(11, pigliveweight);
		setValue(12, leanpercent);
		setValue(13, comment);
		setValue(14, deadamount);
		setValue(15, pigCentralrn);
		setValue(16, deadweight);
		setValue(17, fromlocationId);
		setValue(18, tolocationId);
		setValue(19, price);
		setValue(20, age);
	}
}
