/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.tables.JbsImport;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class JbsImportRecord extends UpdatableRecordImpl<JbsImportRecord> {

	private static final long serialVersionUID = -479611792;

	/**
	 * Setter for <code>farm.jbs_import.id</code>.
	 */
	public void setId(Integer value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.id</code>.
	 */
	public Integer getId() {
		return (Integer) getValue(0);
	}

	/**
	 * Setter for <code>farm.jbs_import.message</code>.
	 */
	public void setMessage(String value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.message</code>.
	 */
	public String getMessage() {
		return (String) getValue(1);
	}

	/**
	 * Setter for <code>farm.jbs_import.farrowing_total</code>.
	 */
	public void setFarrowingTotal(Integer value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.farrowing_total</code>.
	 */
	public Integer getFarrowingTotal() {
		return (Integer) getValue(2);
	}

	/**
	 * Setter for <code>farm.jbs_import.farrowing_rejected</code>.
	 */
	public void setFarrowingRejected(Integer value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.farrowing_rejected</code>.
	 */
	public Integer getFarrowingRejected() {
		return (Integer) getValue(3);
	}

	/**
	 * Setter for <code>farm.jbs_import.weaning_total</code>.
	 */
	public void setWeaningTotal(Integer value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.weaning_total</code>.
	 */
	public Integer getWeaningTotal() {
		return (Integer) getValue(4);
	}

	/**
	 * Setter for <code>farm.jbs_import.weaning_rejected</code>.
	 */
	public void setWeaningRejected(Integer value) {
		setValue(5, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.weaning_rejected</code>.
	 */
	public Integer getWeaningRejected() {
		return (Integer) getValue(5);
	}

	/**
	 * Setter for <code>farm.jbs_import.serving_total</code>.
	 */
	public void setServingTotal(Integer value) {
		setValue(6, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.serving_total</code>.
	 */
	public Integer getServingTotal() {
		return (Integer) getValue(6);
	}

	/**
	 * Setter for <code>farm.jbs_import.serving_rejected</code>.
	 */
	public void setServingRejected(Integer value) {
		setValue(7, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.serving_rejected</code>.
	 */
	public Integer getServingRejected() {
		return (Integer) getValue(7);
	}

	/**
	 * Setter for <code>farm.jbs_import.deaths_total</code>.
	 */
	public void setDeathsTotal(Integer value) {
		setValue(8, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.deaths_total</code>.
	 */
	public Integer getDeathsTotal() {
		return (Integer) getValue(8);
	}

	/**
	 * Setter for <code>farm.jbs_import.deaths_rejected</code>.
	 */
	public void setDeathsRejected(Integer value) {
		setValue(9, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.deaths_rejected</code>.
	 */
	public Integer getDeathsRejected() {
		return (Integer) getValue(9);
	}

	/**
	 * Setter for <code>farm.jbs_import.create_date</code>.
	 */
	public void setCreateDate(Timestamp value) {
		setValue(10, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.create_date</code>.
	 */
	public Timestamp getCreateDate() {
		return (Timestamp) getValue(10);
	}

	/**
	 * Setter for <code>farm.jbs_import.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(11, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(11);
	}

	/**
	 * Setter for <code>farm.jbs_import.create_account_id</code>.
	 */
	public void setCreateAccountId(Long value) {
		setValue(12, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.create_account_id</code>.
	 */
	public Long getCreateAccountId() {
		return (Long) getValue(12);
	}

	/**
	 * Setter for <code>farm.jbs_import.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(13, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(13);
	}

	/**
	 * Setter for <code>farm.jbs_import.create_ui</code>.
	 */
	public void setCreateUi(String value) {
		setValue(14, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.create_ui</code>.
	 */
	public String getCreateUi() {
		return (String) getValue(14);
	}

	/**
	 * Setter for <code>farm.jbs_import.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(15, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(15);
	}

	/**
	 * Setter for <code>farm.jbs_import.fostering_total</code>.
	 */
	public void setFosteringTotal(Integer value) {
		setValue(16, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.fostering_total</code>.
	 */
	public Integer getFosteringTotal() {
		return (Integer) getValue(16);
	}

	/**
	 * Setter for <code>farm.jbs_import.fostering_rejected</code>.
	 */
	public void setFosteringRejected(Integer value) {
		setValue(17, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.fostering_rejected</code>.
	 */
	public Integer getFosteringRejected() {
		return (Integer) getValue(17);
	}

	/**
	 * Setter for <code>farm.jbs_import.file_id</code>.
	 */
	public void setFileId(Integer value) {
		setValue(18, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.file_id</code>.
	 */
	public Integer getFileId() {
		return (Integer) getValue(18);
	}

	/**
	 * Setter for <code>farm.jbs_import.status</code>.
	 */
	public void setStatus(Short value) {
		setValue(19, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.status</code>.
	 */
	public Short getStatus() {
		return (Short) getValue(19);
	}

	/**
	 * Setter for <code>farm.jbs_import.sales_total</code>.
	 */
	public void setSalesTotal(Integer value) {
		setValue(20, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.sales_total</code>.
	 */
	public Integer getSalesTotal() {
		return (Integer) getValue(20);
	}

	/**
	 * Setter for <code>farm.jbs_import.sales_rejected</code>.
	 */
	public void setSalesRejected(Integer value) {
		setValue(21, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.sales_rejected</code>.
	 */
	public Integer getSalesRejected() {
		return (Integer) getValue(21);
	}

	/**
	 * Setter for <code>farm.jbs_import.annotated_hash</code>.
	 */
	public void setAnnotatedHash(String value) {
		setValue(22, value);
	}

	/**
	 * Getter for <code>farm.jbs_import.annotated_hash</code>.
	 */
	public String getAnnotatedHash() {
		return (String) getValue(22);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record1<Integer> key() {
		return (Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached JbsImportRecord
	 */
	public JbsImportRecord() {
		super(JbsImport.JBS_IMPORT);
	}

	/**
	 * Create a detached, initialised JbsImportRecord
	 */
	public JbsImportRecord(Integer id, String message, Integer farrowingTotal, Integer farrowingRejected, Integer weaningTotal, Integer weaningRejected, Integer servingTotal, Integer servingRejected, Integer deathsTotal, Integer deathsRejected, Timestamp createDate, Timestamp updateDate, Long createAccountId, Long updateAccountId, String createUi, String updateUi, Integer fosteringTotal, Integer fosteringRejected, Integer fileId, Short status, Integer salesTotal, Integer salesRejected, String annotatedHash) {
		super(JbsImport.JBS_IMPORT);

		setValue(0, id);
		setValue(1, message);
		setValue(2, farrowingTotal);
		setValue(3, farrowingRejected);
		setValue(4, weaningTotal);
		setValue(5, weaningRejected);
		setValue(6, servingTotal);
		setValue(7, servingRejected);
		setValue(8, deathsTotal);
		setValue(9, deathsRejected);
		setValue(10, createDate);
		setValue(11, updateDate);
		setValue(12, createAccountId);
		setValue(13, updateAccountId);
		setValue(14, createUi);
		setValue(15, updateUi);
		setValue(16, fosteringTotal);
		setValue(17, fosteringRejected);
		setValue(18, fileId);
		setValue(19, status);
		setValue(20, salesTotal);
		setValue(21, salesRejected);
		setValue(22, annotatedHash);
	}
}
