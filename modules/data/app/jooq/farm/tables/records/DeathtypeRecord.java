/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.tables.Deathtype;

import org.jooq.Field;
import org.jooq.Record14;
import org.jooq.Row14;
import org.jooq.impl.TableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DeathtypeRecord extends TableRecordImpl<DeathtypeRecord> implements Record14<Timestamp, Timestamp, Long, Long, String, String, String, String, <PERSON>an, Bo<PERSON>an, Bo<PERSON>an, String, <PERSON>, Boolean> {

	private static final long serialVersionUID = 1241479193;

	/**
	 * Setter for <code>farm.deathtype.create_date</code>.
	 */
	public void setCreateDate(Timestamp value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.deathtype.create_date</code>.
	 */
	public Timestamp getCreateDate() {
		return (Timestamp) getValue(0);
	}

	/**
	 * Setter for <code>farm.deathtype.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.deathtype.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(1);
	}

	/**
	 * Setter for <code>farm.deathtype.create_account_id</code>.
	 */
	public void setCreateAccountId(Long value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.deathtype.create_account_id</code>.
	 */
	public Long getCreateAccountId() {
		return (Long) getValue(2);
	}

	/**
	 * Setter for <code>farm.deathtype.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.deathtype.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(3);
	}

	/**
	 * Setter for <code>farm.deathtype.create_ui</code>.
	 */
	public void setCreateUi(String value) {
		setValue(4, value);
	}

	/**
	 * Getter for <code>farm.deathtype.create_ui</code>.
	 */
	public String getCreateUi() {
		return (String) getValue(4);
	}

	/**
	 * Setter for <code>farm.deathtype.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(5, value);
	}

	/**
	 * Getter for <code>farm.deathtype.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(5);
	}

	/**
	 * Setter for <code>farm.deathtype.code</code>.
	 */
	public void setCode(String value) {
		setValue(6, value);
	}

	/**
	 * Getter for <code>farm.deathtype.code</code>.
	 */
	public String getCode() {
		return (String) getValue(6);
	}

	/**
	 * Setter for <code>farm.deathtype.description</code>.
	 */
	public void setDescription(String value) {
		setValue(7, value);
	}

	/**
	 * Getter for <code>farm.deathtype.description</code>.
	 */
	public String getDescription() {
		return (String) getValue(7);
	}

	/**
	 * Setter for <code>farm.deathtype.stolen</code>.
	 */
	public void setStolen(Boolean value) {
		setValue(8, value);
	}

	/**
	 * Getter for <code>farm.deathtype.stolen</code>.
	 */
	public Boolean getStolen() {
		return (Boolean) getValue(8);
	}

	/**
	 * Setter for <code>farm.deathtype.domestic_slaughter</code>.
	 */
	public void setDomesticSlaughter(Boolean value) {
		setValue(9, value);
	}

	/**
	 * Getter for <code>farm.deathtype.domestic_slaughter</code>.
	 */
	public Boolean getDomesticSlaughter() {
		return (Boolean) getValue(9);
	}

	/**
	 * Setter for <code>farm.deathtype.domestic_disposal</code>.
	 */
	public void setDomesticDisposal(Boolean value) {
		setValue(10, value);
	}

	/**
	 * Getter for <code>farm.deathtype.domestic_disposal</code>.
	 */
	public Boolean getDomesticDisposal() {
		return (Boolean) getValue(10);
	}

	/**
	 * Setter for <code>farm.deathtype.externalcode</code>.
	 */
	public void setExternalcode(String value) {
		setValue(11, value);
	}

	/**
	 * Getter for <code>farm.deathtype.externalcode</code>.
	 */
	public String getExternalcode() {
		return (String) getValue(11);
	}

	/**
	 * Setter for <code>farm.deathtype.euthanasia</code>.
	 */
	public void setEuthanasia(Boolean value) {
		setValue(12, value);
	}

	/**
	 * Getter for <code>farm.deathtype.euthanasia</code>.
	 */
	public Boolean getEuthanasia() {
		return (Boolean) getValue(12);
	}

	/**
	 * Setter for <code>farm.deathtype.disabled</code>.
	 */
	public void setDisabled(Boolean value) {
		setValue(13, value);
	}

	/**
	 * Getter for <code>farm.deathtype.disabled</code>.
	 */
	public Boolean getDisabled() {
		return (Boolean) getValue(13);
	}

	// -------------------------------------------------------------------------
	// Record14 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row14<Timestamp, Timestamp, Long, Long, String, String, String, String, Boolean, Boolean, Boolean, String, Boolean, Boolean> fieldsRow() {
		return (Row14) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row14<Timestamp, Timestamp, Long, Long, String, String, String, String, Boolean, Boolean, Boolean, String, Boolean, Boolean> valuesRow() {
		return (Row14) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field1() {
		return Deathtype.DEATHTYPE.CREATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field2() {
		return Deathtype.DEATHTYPE.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field3() {
		return Deathtype.DEATHTYPE.CREATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field4() {
		return Deathtype.DEATHTYPE.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field5() {
		return Deathtype.DEATHTYPE.CREATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field6() {
		return Deathtype.DEATHTYPE.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field7() {
		return Deathtype.DEATHTYPE.CODE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field8() {
		return Deathtype.DEATHTYPE.DESCRIPTION;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Boolean> field9() {
		return Deathtype.DEATHTYPE.STOLEN;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Boolean> field10() {
		return Deathtype.DEATHTYPE.DOMESTIC_SLAUGHTER;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Boolean> field11() {
		return Deathtype.DEATHTYPE.DOMESTIC_DISPOSAL;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field12() {
		return Deathtype.DEATHTYPE.EXTERNALCODE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Boolean> field13() {
		return Deathtype.DEATHTYPE.EUTHANASIA;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Boolean> field14() {
		return Deathtype.DEATHTYPE.DISABLED;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value1() {
		return getCreateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value2() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value3() {
		return getCreateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value4() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value5() {
		return getCreateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value6() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value7() {
		return getCode();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value8() {
		return getDescription();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Boolean value9() {
		return getStolen();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Boolean value10() {
		return getDomesticSlaughter();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Boolean value11() {
		return getDomesticDisposal();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value12() {
		return getExternalcode();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Boolean value13() {
		return getEuthanasia();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Boolean value14() {
		return getDisabled();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value1(Timestamp value) {
		setCreateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value2(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value3(Long value) {
		setCreateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value4(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value5(String value) {
		setCreateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value6(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value7(String value) {
		setCode(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value8(String value) {
		setDescription(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value9(Boolean value) {
		setStolen(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value10(Boolean value) {
		setDomesticSlaughter(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value11(Boolean value) {
		setDomesticDisposal(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value12(String value) {
		setExternalcode(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value13(Boolean value) {
		setEuthanasia(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord value14(Boolean value) {
		setDisabled(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public DeathtypeRecord values(Timestamp value1, Timestamp value2, Long value3, Long value4, String value5, String value6, String value7, String value8, Boolean value9, Boolean value10, Boolean value11, String value12, Boolean value13, Boolean value14) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		value5(value5);
		value6(value6);
		value7(value7);
		value8(value8);
		value9(value9);
		value10(value10);
		value11(value11);
		value12(value12);
		value13(value13);
		value14(value14);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached DeathtypeRecord
	 */
	public DeathtypeRecord() {
		super(Deathtype.DEATHTYPE);
	}

	/**
	 * Create a detached, initialised DeathtypeRecord
	 */
	public DeathtypeRecord(Timestamp createDate, Timestamp updateDate, Long createAccountId, Long updateAccountId, String createUi, String updateUi, String code, String description, Boolean stolen, Boolean domesticSlaughter, Boolean domesticDisposal, String externalcode, Boolean euthanasia, Boolean disabled) {
		super(Deathtype.DEATHTYPE);

		setValue(0, createDate);
		setValue(1, updateDate);
		setValue(2, createAccountId);
		setValue(3, updateAccountId);
		setValue(4, createUi);
		setValue(5, updateUi);
		setValue(6, code);
		setValue(7, description);
		setValue(8, stolen);
		setValue(9, domesticSlaughter);
		setValue(10, domesticDisposal);
		setValue(11, externalcode);
		setValue(12, euthanasia);
		setValue(13, disabled);
	}
}
