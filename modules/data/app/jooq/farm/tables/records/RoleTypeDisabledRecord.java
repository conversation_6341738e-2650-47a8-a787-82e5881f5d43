/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables.records;


import java.sql.Timestamp;

import javax.annotation.Generated;

import jooq.farm.tables.RoleTypeDisabled;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record4;
import org.jooq.Row4;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class RoleTypeDisabledRecord extends UpdatableRecordImpl<RoleTypeDisabledRecord> implements Record4<String, Timestamp, Long, String> {

	private static final long serialVersionUID = -644553680;

	/**
	 * Setter for <code>farm.role_type_disabled.code</code>.
	 */
	public void setCode(String value) {
		setValue(0, value);
	}

	/**
	 * Getter for <code>farm.role_type_disabled.code</code>.
	 */
	public String getCode() {
		return (String) getValue(0);
	}

	/**
	 * Setter for <code>farm.role_type_disabled.update_date</code>.
	 */
	public void setUpdateDate(Timestamp value) {
		setValue(1, value);
	}

	/**
	 * Getter for <code>farm.role_type_disabled.update_date</code>.
	 */
	public Timestamp getUpdateDate() {
		return (Timestamp) getValue(1);
	}

	/**
	 * Setter for <code>farm.role_type_disabled.update_account_id</code>.
	 */
	public void setUpdateAccountId(Long value) {
		setValue(2, value);
	}

	/**
	 * Getter for <code>farm.role_type_disabled.update_account_id</code>.
	 */
	public Long getUpdateAccountId() {
		return (Long) getValue(2);
	}

	/**
	 * Setter for <code>farm.role_type_disabled.update_ui</code>.
	 */
	public void setUpdateUi(String value) {
		setValue(3, value);
	}

	/**
	 * Getter for <code>farm.role_type_disabled.update_ui</code>.
	 */
	public String getUpdateUi() {
		return (String) getValue(3);
	}

	// -------------------------------------------------------------------------
	// Primary key information
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Record1<String> key() {
		return (Record1) super.key();
	}

	// -------------------------------------------------------------------------
	// Record4 type implementation
	// -------------------------------------------------------------------------

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row4<String, Timestamp, Long, String> fieldsRow() {
		return (Row4) super.fieldsRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Row4<String, Timestamp, Long, String> valuesRow() {
		return (Row4) super.valuesRow();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field1() {
		return RoleTypeDisabled.ROLE_TYPE_DISABLED.CODE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Timestamp> field2() {
		return RoleTypeDisabled.ROLE_TYPE_DISABLED.UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<Long> field3() {
		return RoleTypeDisabled.ROLE_TYPE_DISABLED.UPDATE_ACCOUNT_ID;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Field<String> field4() {
		return RoleTypeDisabled.ROLE_TYPE_DISABLED.UPDATE_UI;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value1() {
		return getCode();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Timestamp value2() {
		return getUpdateDate();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Long value3() {
		return getUpdateAccountId();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public String value4() {
		return getUpdateUi();
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public RoleTypeDisabledRecord value1(String value) {
		setCode(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public RoleTypeDisabledRecord value2(Timestamp value) {
		setUpdateDate(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public RoleTypeDisabledRecord value3(Long value) {
		setUpdateAccountId(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public RoleTypeDisabledRecord value4(String value) {
		setUpdateUi(value);
		return this;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public RoleTypeDisabledRecord values(String value1, Timestamp value2, Long value3, String value4) {
		value1(value1);
		value2(value2);
		value3(value3);
		value4(value4);
		return this;
	}

	// -------------------------------------------------------------------------
	// Constructors
	// -------------------------------------------------------------------------

	/**
	 * Create a detached RoleTypeDisabledRecord
	 */
	public RoleTypeDisabledRecord() {
		super(RoleTypeDisabled.ROLE_TYPE_DISABLED);
	}

	/**
	 * Create a detached, initialised RoleTypeDisabledRecord
	 */
	public RoleTypeDisabledRecord(String code, Timestamp updateDate, Long updateAccountId, String updateUi) {
		super(RoleTypeDisabled.ROLE_TYPE_DISABLED);

		setValue(0, code);
		setValue(1, updateDate);
		setValue(2, updateAccountId);
		setValue(3, updateUi);
	}
}
