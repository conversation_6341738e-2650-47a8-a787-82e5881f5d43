/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.EmptyLocationDeletedRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class EmptyLocationDeleted extends TableImpl<EmptyLocationDeletedRecord> {

	private static final long serialVersionUID = 478223018;

	/**
	 * The reference instance of <code>farm.empty_location_deleted</code>
	 */
	public static final EmptyLocationDeleted EMPTY_LOCATION_DELETED = new EmptyLocationDeleted();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<EmptyLocationDeletedRecord> getRecordType() {
		return EmptyLocationDeletedRecord.class;
	}

	/**
	 * The column <code>farm.empty_location_deleted.id</code>.
	 */
	public final TableField<EmptyLocationDeletedRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

	/**
	 * The column <code>farm.empty_location_deleted.update_date</code>.
	 */
	public final TableField<EmptyLocationDeletedRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.empty_location_deleted.update_account_id</code>.
	 */
	public final TableField<EmptyLocationDeletedRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.empty_location_deleted.update_ui</code>.
	 */
	public final TableField<EmptyLocationDeletedRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * Create a <code>farm.empty_location_deleted</code> table reference
	 */
	public EmptyLocationDeleted() {
		this("empty_location_deleted", null);
	}

	/**
	 * Create an aliased <code>farm.empty_location_deleted</code> table reference
	 */
	public EmptyLocationDeleted(String alias) {
		this(alias, EMPTY_LOCATION_DELETED);
	}

	private EmptyLocationDeleted(String alias, Table<EmptyLocationDeletedRecord> aliased) {
		this(alias, aliased, null);
	}

	private EmptyLocationDeleted(String alias, Table<EmptyLocationDeletedRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<EmptyLocationDeletedRecord> getPrimaryKey() {
		return Keys.EMPTY_LOCATION_DELETED_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<EmptyLocationDeletedRecord>> getKeys() {
		return Arrays.<UniqueKey<EmptyLocationDeletedRecord>>asList(Keys.EMPTY_LOCATION_DELETED_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TableField<EmptyLocationDeletedRecord, Timestamp> getRecordTimestamp() {
		return UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public EmptyLocationDeleted as(String alias) {
		return new EmptyLocationDeleted(alias, this);
	}

	/**
	 * Rename this table
	 */
	public EmptyLocationDeleted rename(String name) {
		return new EmptyLocationDeleted(name, null);
	}
}
