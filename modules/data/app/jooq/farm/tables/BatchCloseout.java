/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.BatchCloseoutRecord;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class BatchCloseout extends TableImpl<BatchCloseoutRecord> {

	private static final long serialVersionUID = -887157081;

	/**
	 * The reference instance of <code>farm.batch_closeout</code>
	 */
	public static final BatchCloseout BATCH_CLOSEOUT = new BatchCloseout();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<BatchCloseoutRecord> getRecordType() {
		return BatchCloseoutRecord.class;
	}

	/**
	 * The column <code>farm.batch_closeout.location_id</code>.
	 */
	public final TableField<BatchCloseoutRecord, Long> LOCATION_ID = createField("location_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

	/**
	 * The column <code>farm.batch_closeout.origin_sow_farm</code>.
	 */
	public final TableField<BatchCloseoutRecord, String> ORIGIN_SOW_FARM = createField("origin_sow_farm", org.jooq.impl.SQLDataType.VARCHAR.length(50), this, "");

	/**
	 * The column <code>farm.batch_closeout.source_farm</code>.
	 */
	public final TableField<BatchCloseoutRecord, String> SOURCE_FARM = createField("source_farm", org.jooq.impl.SQLDataType.VARCHAR.length(80), this, "");

	/**
	 * Create a <code>farm.batch_closeout</code> table reference
	 */
	public BatchCloseout() {
		this("batch_closeout", null);
	}

	/**
	 * Create an aliased <code>farm.batch_closeout</code> table reference
	 */
	public BatchCloseout(String alias) {
		this(alias, BATCH_CLOSEOUT);
	}

	private BatchCloseout(String alias, Table<BatchCloseoutRecord> aliased) {
		this(alias, aliased, null);
	}

	private BatchCloseout(String alias, Table<BatchCloseoutRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<BatchCloseoutRecord> getPrimaryKey() {
		return Keys.BATCH_CLOSEOUT_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<BatchCloseoutRecord>> getKeys() {
		return Arrays.<UniqueKey<BatchCloseoutRecord>>asList(Keys.BATCH_CLOSEOUT_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<ForeignKey<BatchCloseoutRecord, ?>> getReferences() {
		return Arrays.<ForeignKey<BatchCloseoutRecord, ?>>asList(Keys.BATCH_CLOSEOUT__LOCATION_FK);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public BatchCloseout as(String alias) {
		return new BatchCloseout(alias, this);
	}

	/**
	 * Rename this table
	 */
	public BatchCloseout rename(String name) {
		return new BatchCloseout(name, null);
	}
}
