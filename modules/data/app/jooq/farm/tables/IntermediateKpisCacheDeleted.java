/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.IntermediateKpisCacheDeletedRecord;

import org.jooq.Field;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IntermediateKpisCacheDeleted extends TableImpl<IntermediateKpisCacheDeletedRecord> {

	private static final long serialVersionUID = -307337482;

	/**
	 * The reference instance of <code>farm.intermediate_kpis_cache_deleted</code>
	 */
	public static final IntermediateKpisCacheDeleted INTERMEDIATE_KPIS_CACHE_DELETED = new IntermediateKpisCacheDeleted();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<IntermediateKpisCacheDeletedRecord> getRecordType() {
		return IntermediateKpisCacheDeletedRecord.class;
	}

	/**
	 * The column <code>farm.intermediate_kpis_cache_deleted.id</code>.
	 */
	public final TableField<IntermediateKpisCacheDeletedRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

	/**
	 * The column <code>farm.intermediate_kpis_cache_deleted.update_date</code>.
	 */
	public final TableField<IntermediateKpisCacheDeletedRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.intermediate_kpis_cache_deleted.update_account_id</code>.
	 */
	public final TableField<IntermediateKpisCacheDeletedRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.intermediate_kpis_cache_deleted.update_ui</code>.
	 */
	public final TableField<IntermediateKpisCacheDeletedRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * Create a <code>farm.intermediate_kpis_cache_deleted</code> table reference
	 */
	public IntermediateKpisCacheDeleted() {
		this("intermediate_kpis_cache_deleted", null);
	}

	/**
	 * Create an aliased <code>farm.intermediate_kpis_cache_deleted</code> table reference
	 */
	public IntermediateKpisCacheDeleted(String alias) {
		this(alias, INTERMEDIATE_KPIS_CACHE_DELETED);
	}

	private IntermediateKpisCacheDeleted(String alias, Table<IntermediateKpisCacheDeletedRecord> aliased) {
		this(alias, aliased, null);
	}

	private IntermediateKpisCacheDeleted(String alias, Table<IntermediateKpisCacheDeletedRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<IntermediateKpisCacheDeletedRecord> getPrimaryKey() {
		return Keys.INTERMEDIATE_KPIS_CACHE_DELETED_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<IntermediateKpisCacheDeletedRecord>> getKeys() {
		return Arrays.<UniqueKey<IntermediateKpisCacheDeletedRecord>>asList(Keys.INTERMEDIATE_KPIS_CACHE_DELETED_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TableField<IntermediateKpisCacheDeletedRecord, Timestamp> getRecordTimestamp() {
		return UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public IntermediateKpisCacheDeleted as(String alias) {
		return new IntermediateKpisCacheDeleted(alias, this);
	}

	/**
	 * Rename this table
	 */
	public IntermediateKpisCacheDeleted rename(String name) {
		return new IntermediateKpisCacheDeleted(name, null);
	}
}
