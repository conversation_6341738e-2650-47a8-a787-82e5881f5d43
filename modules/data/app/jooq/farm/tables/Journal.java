/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.math.BigDecimal;
import java.sql.Date;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.JournalRecord;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Identity;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Journal extends TableImpl<JournalRecord> {

	private static final long serialVersionUID = 1865635660;

	/**
	 * The reference instance of <code>farm.journal</code>
	 */
	public static final Journal JOURNAL = new Journal();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<JournalRecord> getRecordType() {
		return JournalRecord.class;
	}

	/**
	 * The column <code>farm.journal.id</code>.
	 */
	public final TableField<JournalRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.journal.location_id</code>.
	 */
	public final TableField<JournalRecord, Long> LOCATION_ID = createField("location_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false), this, "");

	/**
	 * The column <code>farm.journal.inhabitanttype_code</code>.
	 */
	public final TableField<JournalRecord, String> INHABITANTTYPE_CODE = createField("inhabitanttype_code", org.jooq.impl.SQLDataType.VARCHAR.length(15), this, "");

	/**
	 * The column <code>farm.journal.actor_date</code>.
	 */
	public final TableField<JournalRecord, Date> ACTOR_DATE = createField("actor_date", org.jooq.impl.SQLDataType.DATE.nullable(false), this, "");

	/**
	 * The column <code>farm.journal.amount</code>.
	 */
	public final TableField<JournalRecord, Integer> AMOUNT = createField("amount", org.jooq.impl.SQLDataType.INTEGER, this, "");

	/**
	 * The column <code>farm.journal.saldoamount</code>.
	 */
	public final TableField<JournalRecord, Integer> SALDOAMOUNT = createField("saldoamount", org.jooq.impl.SQLDataType.INTEGER, this, "");

	/**
	 * The column <code>farm.journal.weight</code>.
	 */
	public final TableField<JournalRecord, BigDecimal> WEIGHT = createField("weight", org.jooq.impl.SQLDataType.NUMERIC.precision(15, 5), this, "");

	/**
	 * The column <code>farm.journal.stocktaking</code>.
	 */
	public final TableField<JournalRecord, Short> STOCKTAKING = createField("stocktaking", org.jooq.impl.SQLDataType.SMALLINT.nullable(false).defaulted(true), this, "");

	/**
	 * Create a <code>farm.journal</code> table reference
	 */
	public Journal() {
		this("journal", null);
	}

	/**
	 * Create an aliased <code>farm.journal</code> table reference
	 */
	public Journal(String alias) {
		this(alias, JOURNAL);
	}

	private Journal(String alias, Table<JournalRecord> aliased) {
		this(alias, aliased, null);
	}

	private Journal(String alias, Table<JournalRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Identity<JournalRecord, Long> getIdentity() {
		return Keys.IDENTITY_JOURNAL;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<JournalRecord> getPrimaryKey() {
		return Keys.JOURNAL_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<JournalRecord>> getKeys() {
		return Arrays.<UniqueKey<JournalRecord>>asList(Keys.JOURNAL_PKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<ForeignKey<JournalRecord, ?>> getReferences() {
		return Arrays.<ForeignKey<JournalRecord, ?>>asList(Keys.JOURNAL__JOURNAL_LOCATION_ID_FKEY, Keys.JOURNAL__JOURNAL_INHABITANTTYPE_CODE_FKEY);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Journal as(String alias) {
		return new Journal(alias, this);
	}

	/**
	 * Rename this table
	 */
	public Journal rename(String name) {
		return new Journal(name, null);
	}
}
