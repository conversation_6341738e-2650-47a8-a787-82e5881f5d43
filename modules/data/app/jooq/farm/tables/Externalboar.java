/**
 * This class is generated by jOOQ
 */
package jooq.farm.tables;


import java.sql.Date;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import jooq.farm.Farm;
import jooq.farm.Keys;
import jooq.farm.tables.records.ExternalboarRecord;

import org.jooq.Field;
import org.jooq.Identity;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
	value = {
		"http://www.jooq.org",
		"jOOQ version:3.6.2"
	},
	comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Externalboar extends TableImpl<ExternalboarRecord> {

	private static final long serialVersionUID = 1691101537;

	/**
	 * The reference instance of <code>farm.externalboar</code>
	 */
	public static final Externalboar EXTERNALBOAR = new Externalboar();

	/**
	 * The class holding records for this type
	 */
	@Override
	public Class<ExternalboarRecord> getRecordType() {
		return ExternalboarRecord.class;
	}

	/**
	 * The column <code>farm.externalboar.create_date</code>.
	 */
	public final TableField<ExternalboarRecord, Timestamp> CREATE_DATE = createField("create_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.externalboar.update_date</code>.
	 */
	public final TableField<ExternalboarRecord, Timestamp> UPDATE_DATE = createField("update_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.externalboar.create_account_id</code>.
	 */
	public final TableField<ExternalboarRecord, Long> CREATE_ACCOUNT_ID = createField("create_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.externalboar.update_account_id</code>.
	 */
	public final TableField<ExternalboarRecord, Long> UPDATE_ACCOUNT_ID = createField("update_account_id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.externalboar.create_ui</code>.
	 */
	public final TableField<ExternalboarRecord, String> CREATE_UI = createField("create_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * The column <code>farm.externalboar.update_ui</code>.
	 */
	public final TableField<ExternalboarRecord, String> UPDATE_UI = createField("update_ui", org.jooq.impl.SQLDataType.CHAR.length(1).defaulted(true), this, "");

	/**
	 * The column <code>farm.externalboar.id</code>.
	 */
	public final TableField<ExternalboarRecord, Long> ID = createField("id", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaulted(true), this, "");

	/**
	 * The column <code>farm.externalboar.boarnumber</code>.
	 */
	public final TableField<ExternalboarRecord, String> BOARNUMBER = createField("boarnumber", org.jooq.impl.SQLDataType.VARCHAR.length(30).nullable(false), this, "");

	/**
	 * The column <code>farm.externalboar.animalid</code>.
	 */
	public final TableField<ExternalboarRecord, String> ANIMALID = createField("animalid", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * The column <code>farm.externalboar.boardate</code>.
	 */
	public final TableField<ExternalboarRecord, Timestamp> BOARDATE = createField("boardate", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false), this, "");

	/**
	 * The column <code>farm.externalboar.usableuntil</code>.
	 */
	public final TableField<ExternalboarRecord, Timestamp> USABLEUNTIL = createField("usableuntil", org.jooq.impl.SQLDataType.TIMESTAMP, this, "");

	/**
	 * The column <code>farm.externalboar.peer_farm_id</code>.
	 */
	public final TableField<ExternalboarRecord, Long> PEER_FARM_ID = createField("peer_farm_id", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.externalboar.peer_boar_id</code>.
	 */
	public final TableField<ExternalboarRecord, Long> PEER_BOAR_ID = createField("peer_boar_id", org.jooq.impl.SQLDataType.BIGINT, this, "");

	/**
	 * The column <code>farm.externalboar.planned_tapping</code>.
	 */
	public final TableField<ExternalboarRecord, Date> PLANNED_TAPPING = createField("planned_tapping", org.jooq.impl.SQLDataType.DATE, this, "");

	/**
	 * The column <code>farm.externalboar.nationalid</code>.
	 */
	public final TableField<ExternalboarRecord, String> NATIONALID = createField("nationalid", org.jooq.impl.SQLDataType.VARCHAR.length(30), this, "");

	/**
	 * Create a <code>farm.externalboar</code> table reference
	 */
	public Externalboar() {
		this("externalboar", null);
	}

	/**
	 * Create an aliased <code>farm.externalboar</code> table reference
	 */
	public Externalboar(String alias) {
		this(alias, EXTERNALBOAR);
	}

	private Externalboar(String alias, Table<ExternalboarRecord> aliased) {
		this(alias, aliased, null);
	}

	private Externalboar(String alias, Table<ExternalboarRecord> aliased, Field<?>[] parameters) {
		super(alias, Farm.FARM, aliased, parameters, "");
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Identity<ExternalboarRecord, Long> getIdentity() {
		return Keys.IDENTITY_EXTERNALBOAR;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public UniqueKey<ExternalboarRecord> getPrimaryKey() {
		return Keys.EXTERNALBOAR_PKEY;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public List<UniqueKey<ExternalboarRecord>> getKeys() {
		return Arrays.<UniqueKey<ExternalboarRecord>>asList(Keys.EXTERNALBOAR_PKEY, Keys.EXTERNALBOAR_PEER_UI);
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public TableField<ExternalboarRecord, Timestamp> getRecordTimestamp() {
		return UPDATE_DATE;
	}

	/**
	 * {@inheritDoc}
	 */
	@Override
	public Externalboar as(String alias) {
		return new Externalboar(alias, this);
	}

	/**
	 * Rename this table
	 */
	public Externalboar rename(String name) {
		return new Externalboar(name, null);
	}
}
