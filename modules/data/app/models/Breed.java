package models;

import com.cloudfarms.pigs.sync.entities.BreedDTO;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import generators.Display;

import jakarta.persistence.*;

import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

@Entity(name = "Breed")
@JsonFilter("BreedFilter")
@JsonIgnoreProperties(ignoreUnknown = true)

@NamedNativeQueries(
        {@NamedNativeQuery(name = "breed.newanimals.danavl.byCreateDate.byLastIndexUpdate",
                query = "with new_without_index as (\n" +
                        "    select\n" +
                        "      b.animalid, b.create_date, last_index_update_date, index from\n" +
                        "      breed b\n" +
                        "    where (b.index is null) and b.create_date > #createDate :: timestamp order by b.update_date desc\n" +
                        "), transfered_animalids as (\n" +
                        "    select case\n" +
                        "           when ti.sow_id is not null then s.animalid\n" +
                        "           when ti.boar_id is not null then b.animalid\n" +
                        "           when ti.gilt_id is not null then g.animalid\n" +
                        "           end as ti_animalid\n" +
                        "    from transferindividualin ti\n" +
                        "      left join sow s on s.id = ti.sow_id\n" +
                        "      left join gilt g on g.id = ti.gilt_id\n" +
                        "      left join boar b on b.id = ti.boar_id\n" +
                        ")\n" +
                        "select b.animalid as animalid from transfered_animalids as ti join new_without_index b on ti.ti_animalid = b.animalid;"),

                @NamedNativeQuery(name = "breed.4gens.updated.after",
                        query =
                                "with recursive pedigree as (\n" +
                                        "               select distinct b.animalid, b.birthdate, b.breed, b.dambreed, b.damid, b.damnumber, b.farmnumber, b.index, b.sex, b.sirebreed, b.sireid, b.sirenumber, 0 depth, b.update_date\n" +
                                        "               from breed b join gilt g on g.animalid = b.animalid and g.active\n" +
                                        "                   union\n" +
                                        "                   select distinct b.animalid, b.birthdate, b.breed, b.dambreed, b.damid, b.damnumber, b.farmnumber, b.index, b.sex, b.sirebreed, b.sireid, b.sirenumber, p.depth + 1 depth, b.update_date\n" +
                                        "                   from breed b join pedigree p on (p.damid = b.animalid or p.sireid = b.animalid) and p.depth < 5\n" +
                                        "               ) select distinct on (animalid) animalid, birthdate, breed, dambreed, damid, damnumber, farmnumber, index, sex, sirebreed, sireid, sirenumber from pedigree where update_date > ?;\n"),

                @NamedNativeQuery(name = "breed.deleted.after", query = "select animalid from breed_deleted where update_date >= ?")

        }
)
public class Breed extends Base {
    public static final String MALE = "male";
    public static final String FEMALE = "female";
    private Business business;
    private String animalId;
    private String oldAnimalId;
    private String farmNumber;
    private Date birthDate;
    private Date danavlbirthDate;
    private Date lastIndexUpdateDate;
    private Timestamp lastIndexDownloadDate;
    private String breed;
    private String sex;
    private String danavlsex;
    private Integer teats;
    private BigDecimal index;
    private String damId;
    private String damNumber;
    private String damBreed;
    private String sireId;
    private String sireNumber;
    private String sireBreed;
    private BigDecimal maleIndex;
    private BigDecimal femaleIndex;
    private BigDecimal dailyGainSmall;//T1: Daglig tilvækst 1 - 30 kg.
    private BigDecimal dailyGainLarge;//Dgl: Daglig tilvækst 30 - 100 kg.
    private BigDecimal feedUnits;//Fe: Foder forbrug.
    private BigDecimal leanMeatPercentage;//K%: Kød procent.
    private BigDecimal litterSize;//Fgk: Fødte grise pr. kuld.
    private BigDecimal sustainability;//Hbh: Holdbarhed.
    private BigDecimal slaughterLoss;//Sv: Slagte svind.
    private BigDecimal strength;//Sty: Styrke
    private String indexType;
    private String comment;
    private Short useCode;
    private Short danavlStatus;
    private String exitType;
    private String exitCode;
    private Date exitDate;
    private Date entryDate;
    private String danavlName;
    private String danavlFarmNumber;
    private String danavlHerdHistory;
    private Short teatsLeft;
    private Short teatsRight;
    private String sireLine;
    private String damLine;
    private Date f4date;
    private String f4state;

    private BigDecimal dailyGainSmallInTest;
    private BigDecimal dailyGainLargeInTest;
    private BigDecimal leanMeatInTest;
    private String dnaTestId;
    private Date dnaTestDate;
    private Long dnaTestActorId;

    private BigDecimal backfat;
    private BigDecimal lp5;

    private BigDecimal lp1;
    private BigDecimal pigletSurvival;
    private BigDecimal longevity;
    private BigDecimal boarFertility;

    // TEMPORARY SPECIAL COLUMNS FOR DANISH GENETICS {
    private BigDecimal dgi_adg_large;
    private BigDecimal dgi_adg_small_dir;
    private BigDecimal dgi_adg_small_mat;
    private BigDecimal dgi_backfat;
    private BigDecimal dgi_logevity;
    private BigDecimal dgi_lp5;
    private BigDecimal dgi_index_scaled;
    private BigDecimal dgi_male_fertility;
    // } TEMPORARY SPECIAL COLUMNS FOR DANISH GENETICS


    public BigDecimal getLp1() {
        return lp1;
    }

    public void setLp1(BigDecimal lp1) {
        this.lp1 = lp1;
    }

    @Column(name = "piglet_survival")
    public BigDecimal getPigletSurvival() {
        return pigletSurvival;
    }

    public void setPigletSurvival(BigDecimal pigletSurvival) {
        this.pigletSurvival = pigletSurvival;
    }

    public BigDecimal getLongevity() {
        return longevity;
    }

    public void setLongevity(BigDecimal longevity) {
        this.longevity = longevity;
    }

    @Column(name = "boar_fertility")
    public BigDecimal getBoarFertility() {
        return boarFertility;
    }

    public void setBoarFertility(BigDecimal boarFertility) {
        this.boarFertility = boarFertility;
    }

    public BigDecimal getBackfat() {
        return backfat;
    }

    public void setBackfat(BigDecimal backfat) {
        this.backfat = backfat;
    }

    public BigDecimal getLp5() {
        return lp5;
    }

    public void setLp5(BigDecimal lp5) {
        this.lp5 = lp5;
    }

    @Transient
    public BigDecimal getDgi_adg_large() {
        return dgi_adg_large;
    }

    public void setDgi_adg_large(BigDecimal dgi_adg_large) {
        this.dgi_adg_large = dgi_adg_large;
    }

    @Transient
    public BigDecimal getDgi_adg_small_dir() {
        return dgi_adg_small_dir;
    }

    public void setDgi_adg_small_dir(BigDecimal dgi_adg_small_dir) {
        this.dgi_adg_small_dir = dgi_adg_small_dir;
    }

    @Transient
    public BigDecimal getDgi_adg_small_mat() {
        return dgi_adg_small_mat;
    }

    public void setDgi_adg_small_mat(BigDecimal dgi_adg_small_mat) {
        this.dgi_adg_small_mat = dgi_adg_small_mat;
    }

    @Transient
    public BigDecimal getDgi_backfat() {
        return dgi_backfat;
    }

    public void setDgi_backfat(BigDecimal dgi_backfat) {
        this.dgi_backfat = dgi_backfat;
    }

    @Transient
    public BigDecimal getDgi_logevity() {
        return dgi_logevity;
    }

    public void setDgi_logevity(BigDecimal dgi_logevity) {
        this.dgi_logevity = dgi_logevity;
    }

    @Transient
    public BigDecimal getDgi_lp5() {
        return dgi_lp5;
    }

    public void setDgi_lp5(BigDecimal dgi_lp5) {
        this.dgi_lp5 = dgi_lp5;
    }

    @Transient
    public BigDecimal getDgi_index_scaled() {
        return dgi_index_scaled;
    }

    public void setDgi_index_scaled(BigDecimal dgi_index_scaled) {
        this.dgi_index_scaled = dgi_index_scaled;
    }

    @Transient
    public BigDecimal getDgi_male_fertility() {
        return dgi_male_fertility;
    }

    public void setDgi_male_fertility(BigDecimal dgi_male_fertility) {
        this.dgi_male_fertility = dgi_male_fertility;
    }

    @Column(name = "dna_test_id")
    public String getDnaTestId() {
        return dnaTestId;
    }

    public void setDnaTestId(String dnaTestId) {
        this.dnaTestId = dnaTestId;
    }

    @Column(name = "dna_test_date")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getDnaTestDate() {
        return dnaTestDate;
    }

    public void setDnaTestDate(Date dnaTestDate) {
        this.dnaTestDate = dnaTestDate;
    }

    @Column(name = "dna_test_actor_id")
    public Long getDnaTestActorId() {
        return dnaTestActorId;
    }

    public void setDnaTestActorId(Long dnaTestActorId) {
        this.dnaTestActorId = dnaTestActorId;
    }

    @Transient
    public BigDecimal getDailyGainSmallInTest() {
        return dailyGainSmallInTest;
    }

    public void setDailyGainSmallInTest(BigDecimal dailyGainSmallInTest) {
        this.dailyGainSmallInTest = dailyGainSmallInTest;
    }

    @Transient
    public BigDecimal getDailyGainLargeInTest() {
        return dailyGainLargeInTest;
    }

    public void setDailyGainLargeInTest(BigDecimal dailyGainLargeInTest) {
        this.dailyGainLargeInTest = dailyGainLargeInTest;
    }

    @Transient
    public BigDecimal getLeanMeatInTest() {
        return leanMeatInTest;
    }

    public void setLeanMeatInTest(BigDecimal leanMeatInTest) {
        this.leanMeatInTest = leanMeatInTest;
    }

    /* Additional from danavl
        brugskode
        navn
        AgansDato
        AfgangsType
        AfgangsKode
        Last index calculation
        */

    private Sow sow;
    private Boar boar;
    private Timestamp updateDate;

    @Column(name = "danavlbirthdate")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    public Date getDanavlbirthDate() {
        return danavlbirthDate;
    }

    public void setDanavlbirthDate(Date danavlbirthDate) {
        this.danavlbirthDate = danavlbirthDate;
    }

    @Column(name = "danavlsex")
    public String getDanavlsex() {
        return danavlsex;
    }

    public void setDanavlsex(String danavlsex) {
        this.danavlsex = danavlsex;
    }

    @Column(name = "danavlname")
    public String getDanavlName() {
        return danavlName;
    }

    public void setDanavlName(String danavlName) {
        this.danavlName = danavlName;
    }

    @Column(name = "danavl_herd_history")
    public String getDanavlHerdHistory() {
        return danavlHerdHistory;
    }

    public void setDanavlHerdHistory(String danavlHerdHistory) {
        this.danavlHerdHistory = danavlHerdHistory;
    }

    @Column(name = "exittype")
    public String getExitType() {
        return exitType;
    }

    public void setExitType(String exitType) {
        this.exitType = exitType;
    }

    @Column(name = "exitcode")
    public String getExitCode() {
        return exitCode;
    }

    public void setExitCode(String exitCode) {
        this.exitCode = exitCode;
    }

    @Column(name = "exitdate")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    public Date getExitDate() {
        return exitDate;
    }

    public void setExitDate(Date exitDate) {
        this.exitDate = exitDate;
    }

    @Column(name = "entrydate")
    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    public Date getEntryDate() {
        return entryDate;
    }

    public void setEntryDate(Date entryDate) {
        this.entryDate = entryDate;
    }

    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    public Date getF4date() {
        return f4date;
    }

    public void setF4date(Date f4date) {
        this.f4date = f4date;
    }

    public String getF4state() {
        return f4state;
    }

    public void setF4state(String f4state) {
        this.f4state = f4state;
    }

    @Column(name = "usecode")
    public Short getUseCode() {
        return useCode;
    }

    public void setUseCode(Short useCode) {
        this.useCode = useCode;
    }

    @Column(name = "danavl_status")
    public Short getDanavlStatus() {
        return danavlStatus;
    }

    public void setDanavlStatus(Short danavlStatus) {
        this.danavlStatus = danavlStatus;
    }

    @JsonIgnore
    @JoinColumn(name = "animalId", insertable = false, updatable = false)
    @OneToOne(fetch = FetchType.LAZY, mappedBy = "breedInfo")
    public Boar getBoar() {
        return boar;
    }

    private void setBoar(Boar boar) {
        this.boar = boar;
    }

    @JsonIgnore
    @JoinColumn(name = "animalId", insertable = false, updatable = false)
    @OneToOne(fetch = FetchType.LAZY, mappedBy = "breedInfo")
    public Sow getSow() {
        return sow;
    }

    private void setSow(Sow sow) {
        this.sow = sow;
    }


    @Transient
//    @JsonIgnore
//    @JsonView(JsonLevel.Detailed.class)
    public String getAnimalType() {
        if (sow != null) {
            return "sow";
        } else if (boar != null) {
            return "boar";
        } else {
            return MALE.equals(sex) ? "boar" : "gilt";      // 'sex' is  used to handle External semen from unknown boar
        }
    }

    @Transient
//    @JsonView(JsonLevel.Detailed.class)
//    @JsonIgnore
    public String getAnimalName() {
        if (sow != null) {
            return sow.getSowNumber();
        } else if (boar != null) {
            return boar.getBoarNumber();
        } else {
            return null;
        }
    }

    @Override
    public Object toDTO() {
        BreedDTO dto = new BreedDTO();
        dto.setAnimalId(getAnimalId());
        dto.setBirthDate(getBirthDate());
        dto.setBreed(getBreed());
        dto.setDamBreed(getDamBreed());
        dto.setDamId(getDamId());
        dto.setDamNumber(getDamNumber());
        dto.setFarmNumber(getFarmNumber());
        dto.setIndex(doubleValue(getIndex()));
        dto.setSex(getSex());
        dto.setSireBreed(getSireBreed());
        dto.setSireId(getSireId());
        dto.setSireNumber(getSireNumber());
        return dto;
    }

    @JsonIgnore
    @ManyToOne
    public Business getBusiness() {
        return business;
    }

    public void setBusiness(Business business) {
        this.business = business;
    }

    @Id
    @Size(max = 30)
    public String getAnimalId() {
        return animalId;
    }

    public void setAnimalId(String animalId) {
        this.animalId = animalId;
    }

    @Column(name = "old_animalid")
    public String getOldAnimalId() {
        return oldAnimalId;
    }

    public void setOldAnimalId(String oldAnimalId) {
        this.oldAnimalId = oldAnimalId;
    }

    @Column(name = "danavlfarmnumber")
    public String getDanavlFarmNumber() {
        return danavlFarmNumber;
    }

    public void setDanavlFarmNumber(String danavlFarmNumber) {
        this.danavlFarmNumber = danavlFarmNumber;
    }

    public String getFarmNumber() {
        return farmNumber;
    }

    public void setFarmNumber(String farmNumber) {
        this.farmNumber = farmNumber;
    }

    @Temporal(TemporalType.DATE)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    public Date getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(Date birthDate) {
        this.birthDate = birthDate;
    }


    public String getBreed() {
        return breed;
    }

    public void setBreed(String breed) {
        this.breed = breed;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public Integer getTeats() {
        return teats;
    }

    public void setTeats(Integer teats) {
        this.teats = teats;
    }

    public BigDecimal getIndex() {
        return index;
    }

    public void setIndex(BigDecimal index) {
        this.index = index;
    }

    public String getDamId() {
        return damId;
    }

    public void setDamId(String damId) {
        this.damId = damId;
    }

    public String getDamNumber() {
        return damNumber;
    }

    public void setDamNumber(String damNumber) {
        this.damNumber = damNumber;
    }

    public String getDamBreed() {
        return damBreed;
    }

    public void setDamBreed(String damBreed) {
        this.damBreed = damBreed;
    }

    public String getSireId() {
        return sireId;
    }

    public void setSireId(String sireId) {
        this.sireId = sireId;
    }

    public String getSireNumber() {
        return sireNumber;
    }

    public void setSireNumber(String sireNumber) {
        this.sireNumber = sireNumber;
    }

    public String getSireBreed() {
        return sireBreed;
    }

    public void setSireBreed(String sireBreed) {
        this.sireBreed = sireBreed;
    }

    @Column(name = "MALE_INDEX")
    public BigDecimal getMaleIndex() {
        return maleIndex;
    }

    public void setMaleIndex(BigDecimal maleIndex) {
        this.maleIndex = maleIndex;
    }

    @Column(name = "FEMALE_INDEX")
    public BigDecimal getFemaleIndex() {
        return femaleIndex;
    }

    public void setFemaleIndex(BigDecimal femaleIndex) {
        this.femaleIndex = femaleIndex;
    }

    @Column(name = "DAILY_GAIN_SMALL")
    public BigDecimal getDailyGainSmall() {
        return dailyGainSmall;
    }

    public void setDailyGainSmall(BigDecimal dailyGainSmall) {
        this.dailyGainSmall = dailyGainSmall;
    }

    @Column(name = "DAILY_GAIN_LARGE")
    public BigDecimal getDailyGainLarge() {
        return dailyGainLarge;
    }

    public void setDailyGainLarge(BigDecimal dailyGainLarge) {
        this.dailyGainLarge = dailyGainLarge;
    }

    @Column(name = "FEED_UNITS")
    public BigDecimal getFeedUnits() {
        return feedUnits;
    }

    public void setFeedUnits(BigDecimal feedUnits) {
        this.feedUnits = feedUnits;
    }

    @Column(name = "LEAN_MEAT_PERCENTAGE")
    public BigDecimal getLeanMeatPercentage() {
        return leanMeatPercentage;
    }

    public void setLeanMeatPercentage(BigDecimal leanMeatPercentage) {
        this.leanMeatPercentage = leanMeatPercentage;
    }

    @Column(name = "LITTER_SIZE")
    public BigDecimal getLitterSize() {
        return litterSize;
    }

    public void setLitterSize(BigDecimal litterSize) {
        this.litterSize = litterSize;
    }

    public BigDecimal getSustainability() {
        return sustainability;
    }

    public void setSustainability(BigDecimal sustainability) {
        this.sustainability = sustainability;
    }

    @Column(name = "SLAUGHTER_LOSS")
    public BigDecimal getSlaughterLoss() {
        return slaughterLoss;
    }

    public void setSlaughterLoss(BigDecimal slaughterLoss) {
        this.slaughterLoss = slaughterLoss;
    }

    @Column(name = "STRENGTH")
    public BigDecimal getStrength() {
        return strength;
    }

    public void setStrength(BigDecimal strength) {
        this.strength = strength;
    }

    @Column(name = "INDEX_TYPE")
    public String getIndexType() {
        return indexType;
    }

    public void setIndexType(String indexType) {
        this.indexType = indexType;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "LAST_INDEX_UPDATE_DATE")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    public Date getLastIndexUpdateDate() {
        return lastIndexUpdateDate;
    }

    public void setLastIndexUpdateDate(Date lastIndexUpdateDate) {
        this.lastIndexUpdateDate = lastIndexUpdateDate;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Column(name = "update_date")
    @Version
    @Display(filters = "timestamp")
    public Timestamp getUpdateDate() {
        return updateDate;
    }

    protected void setUpdateDate(Timestamp updateDate) {
        this.updateDate = updateDate;
    }

    @Column(name = "last_index_download_date")
    @Display(filters = "timestamp")
    public Timestamp getLastIndexDownloadDate() {
        return lastIndexDownloadDate;
    }

    public void setLastIndexDownloadDate(Timestamp lastIndexDownloadDate) {
        this.lastIndexDownloadDate = lastIndexDownloadDate;
    }

    @Column(name = "TEATSLEFT")
    public Short getTeatsLeft() {
        return teatsLeft;
    }

    public void setTeatsLeft(Short teatsLeft) {
        this.teatsLeft = teatsLeft;
    }

    @Column(name = "TEATSRIGHT")
    public Short getTeatsRight() {
        return teatsRight;
    }

    public void setTeatsRight(Short teatsRight) {
        this.teatsRight = teatsRight;
    }

    @Column(name = "sire_line")
    public String getSireLine() {
        return sireLine;
    }

    public void setSireLine(String sireLine) {
        this.sireLine = sireLine;
    }

    @Column(name = "dam_line")
    public String getDamLine() {
        return damLine;
    }

    public void setDamLine(String damLine) {
        this.damLine = damLine;
    }
}
