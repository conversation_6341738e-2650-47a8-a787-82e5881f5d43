package models;

import security.CloudfarmsModule;

import jakarta.persistence.*;

/**
 * Created by grayman on 07.07.14.
 */
@Entity(name="SystemModuleGrant")
@Table(name="system_module")
@NamedQuery(name="systemModuleGrant.all", query = "select m from SystemModuleGrant m")
public class SystemModuleGrant extends ModuleGrant {
    private CloudfarmsModule module;

    @Id
    @Override
    @Column(name="cfmodule_id")
    public CloudfarmsModule getModule() {
        return module;
    }

    protected void setModule(CloudfarmsModule module) {
        this.module = module;
    }


}
