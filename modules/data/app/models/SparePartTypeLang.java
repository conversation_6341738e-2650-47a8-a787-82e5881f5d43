package models;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.eclipse.persistence.annotations.CacheType;
import org.eclipse.persistence.config.HintValues;
import org.eclipse.persistence.config.QueryHints;

import jakarta.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * Created by <PERSON><PERSON> on 14.7.2014.
 */
@Entity(name="SparePartTypeLang")
@org.eclipse.persistence.annotations.Cache(type= CacheType.SOFT)
@Table(name="SPARE_PART_TYPE_LANG_X")
@JsonIgnoreProperties({"code"})

@NamedNativeQueries({
    @NamedNativeQuery(name="sparepart_type_lang.all", query = "select code, language_id, name from spare_part_type_lang", hints = {@QueryHint(name= QueryHints.CURSOR, value=HintValues.TRUE)}),
    @NamedNativeQuery(name="sparepart_type_lang.updated.after", query = "select code, language_id, name from spare_part_type_lang where update_date > ?", hints = {@QueryHint(name= QueryHints.CURSOR, value=HintValues.TRUE)}),
    @NamedNativeQuery(name="sparepart_type_lang.deleted.after", query = "select code, language_id from spare_part_type_lang_deleted where update_date > ?", hints = {@QueryHint(name= QueryHints.CURSOR, value=HintValues.TRUE)})
})

public class SparePartTypeLang extends TranslationOf<SparePartType> {

    private String name;
    private SparePartType sparePartType;

    @NotNull
    @MapsId("code")
    @OneToOne(fetch= FetchType.LAZY)
    @JoinColumn(name="CODE")
    @JsonBackReference
    public SparePartType getParent() {
        return sparePartType;
    }
    public void setParent(SparePartType sparePartType) {
        this.sparePartType = sparePartType;
    }

    @Override
    public void setCode(String code) {
        super.setCode(code);
    }

    @Size(max=30)
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }

}
