package models;

import javax.annotation.Generated;
import jakarta.persistence.metamodel.SingularAttribute;
import jakarta.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(IssueType.class)
public abstract class IssueType_ extends models.TranslatedCodeTable_ {

	public static volatile SingularAttribute<IssueType, String> name;
	public static volatile SingularAttribute<IssueType, String> description;
	public static volatile SingularAttribute<IssueType, IssueTypeLang> translated;

}

