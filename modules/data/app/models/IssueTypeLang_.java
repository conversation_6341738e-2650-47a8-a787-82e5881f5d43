package models;

import javax.annotation.Generated;
import jakarta.persistence.metamodel.SingularAttribute;
import jakarta.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(IssueTypeLang.class)
public abstract class IssueTypeLang_ extends models.TranslationOf_ {

	public static volatile SingularAttribute<IssueTypeLang, IssueType> parent;
	public static volatile SingularAttribute<IssueTypeLang, String> name;
	public static volatile SingularAttribute<IssueTypeLang, String> description;

}

