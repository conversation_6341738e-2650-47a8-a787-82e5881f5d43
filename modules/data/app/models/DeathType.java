package models;

import com.cloudfarms.pigs.sync.entities.DeathTypeDTO;
import com.cloudfarms.pigs.sync.entities.IllnessTypeDTO;
import com.fasterxml.jackson.annotation.JsonFilter;
import org.eclipse.persistence.annotations.Cache;
import org.eclipse.persistence.annotations.CacheType;
import org.eclipse.persistence.config.HintValues;
import org.eclipse.persistence.config.QueryHints;
import play.data.validation.Constraints;

import jakarta.persistence.*;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Entity(name = "DeathType")
@NamedQueries({
    @NamedQuery(name = "CodeType_deathtype.all", query = "select a from DeathType a order by a.code", hints = {
        @QueryHint(name = QueryHints.LEFT_FETCH, value = "a.translated"),
        @QueryHint(name = QueryHints.QUERY_TYPE, value = "ReadAll") }),
    @NamedQuery(name = "deathtype.updated.after", query = "select a from DeathType a where a.updateDate > :after")
} )

@NamedNativeQuery(name="deathtype.deleted.after", query = "select code from deathtype_all_deleted where update_date > ?", hints = {@QueryHint(name=QueryHints.CURSOR, value= HintValues.TRUE)})
@JsonFilter("DeathTypeFilter")
public class DeathType extends TranslatedCodeTable<DeathType> implements WithCodeDescription {

    final public static String TYPE_NAME = "DeathTypes";

    private String description;
    private String externalCode;
    private DeathTypeLang translated;
    private boolean domesticSlaughter;
    private boolean domesticDisposal;

    @Column(name = "stolen")
    public boolean isStolen() {
        return stolen;
    }

    public void setStolen(boolean stolen) {
        this.stolen = stolen;
    }

    private boolean stolen;

    @Column(name = "domestic_slaughter")
    public boolean isDomesticSlaughter() {
        return domesticSlaughter;
    }

    public void setDomesticSlaughter(boolean domesticSlaughter) {
        this.domesticSlaughter = domesticSlaughter;
    }

    @Column(name = "domestic_disposal")
    public boolean isDomesticDisposal() {
        return domesticDisposal;
    }

    public void setDomesticDisposal(boolean domesticDisposal) {
        this.domesticDisposal = domesticDisposal;
    }

    private boolean euthanasia;

    public boolean isEuthanasia() {
        return euthanasia;
    }

    public void setEuthanasia(boolean euthanasia) {
        this.euthanasia = euthanasia;
    }

    @OneToOne(mappedBy = "parent", fetch = FetchType.LAZY, optional = true, cascade = CascadeType.ALL)
    public DeathTypeLang getTranslated() {
        return translated;
    }
    public void setTranslated(DeathTypeLang translated) {
        this.translated = translated;
    }

    @Size(max = 15)
    public String getExternalCode() {
        return externalCode;
    }
    public void setExternalCode(String externalCode) {
        this.externalCode = externalCode;
    }

    @Size(max = 300)
    public String getDescription() {
        return description;
    }
    public void setDescription(String description) {
        this.description = description;
    }



    public DeathTypeDTO toDTO() {
        DeathTypeDTO dto = new DeathTypeDTO();
        dto.setCode(getCode());
        dto.setDescription(getDescription());
        dto.setDisabled(isDisabled());
        return dto;
    }

    @Override
    public String codeDescription() {
        return getDescription();
    }
}
