package models;

/**
* Created by <PERSON><PERSON> on 12.5.2014.
*/

import com.cloudfarms.pigs.sync.entities.UnitDTO;
import org.eclipse.persistence.config.QueryHints;

import jakarta.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@NamedQueries({
        @NamedQuery(name = "Unit.all", query = "select u from Unit u order by u.name")
})
@Entity(name = "Unit")
public class Unit {
    final public static String TYPE_NAME = "Units";

    private String name;
    private String description;
    private String unit;
    private boolean medicine;

    @NotNull
    public boolean isMedicine() {
        return medicine;
    }
    public void setMedicine(boolean medicine) {
        this.medicine = medicine;
    }

    @Size(max = 30)
    @NotNull
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }

    @Id
    @Size(max = 15)
    @NotNull
    public String getUnit() {
        return unit;
    }
    public void setUnit(String unit) {
        this.unit = unit;
    }

    @Size(max = 300)
    public String getDescription() {
        return description;
    }
    public void setDescription(String description) {
        this.description = description;
    }

    public UnitDTO toDto() {
        UnitDTO dto = new UnitDTO();
        dto.setName(name);
        dto.setDescription(description);
        dto.setUnit(unit);
        dto.setMedicine(medicine);

        return dto;
    }
}
