package models;

import jakarta.persistence.*;

/**
 * Created by Milan Satala
 * Date: 7/13/17
 * Time: 2:43 PM
 */
@MappedSuperclass
@IdClass(KpiLangId.class)
public abstract class KpiDefinitionLangBase extends Base  {
    private String code;
    private String languageId;
    private String name;
    private String description;

    @Id
    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Id
    @Column(name = "language_id")
    public String getLanguageId() {
        return languageId;
    }

    public void setLanguageId(String languageId) {
        this.languageId = languageId;
    }

    @Basic
    @Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

}
