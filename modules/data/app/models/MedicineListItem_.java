package models;

import javax.annotation.Generated;
import jakarta.persistence.metamodel.SingularAttribute;
import jakarta.persistence.metamodel.StaticMetamodel;

@Generated(value = "org.hibernate.jpamodelgen.JPAMetaModelEntityProcessor")
@StaticMetamodel(MedicineListItem.class)
public abstract class MedicineListItem_ {

	public static volatile SingularAttribute<MedicineListItem, MedicineListItemId> id;

}

