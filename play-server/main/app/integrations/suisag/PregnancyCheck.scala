package integrations.suisag

import com.cloudfarms.cfzio.DatabaseProvider
import com.cloudfarms.cfzio.context.FarmUserContext
import scalikejdbc._
import zio.ZIO
import zio.json._

import java.time.LocalDate

@jsonMemberNames(SnakeCase)
final case class PregnancyCheck(
  servingId: Long,
  pregnant: Option[Boolean],
)

object PregnancyCheck {
  implicit val codec: JsonCodec[PregnancyCheck] = DeriveJsonCodec.gen[PregnancyCheck]

  implicit val pregnancyCheckWrapper: IntegrationWrapper[PregnancyCheck] = new IntegrationWrapper[PregnancyCheck] {

    type Key = Long
    override val urlEnd: String = "traechtigkeit"

    override def parseDataFromSuisdata(data: String): Either[String, SuisagData] =
      data.fromJson[PregnancyCheckReportingRequest].map(r => SuisagData(Nil, r.pregnancyChecks.upsert))

    override def wrapAsRequestBody(farm: String, deletes: List[Long], upserts: List[PregnancyCheck]): String = {
      PregnancyCheckReportingRequest(
        farm,
        PregnancyCheckReporting(
          upserts,
        ),
      ).toJsonPretty
    }

    override def getId(e: PregnancyCheck): Option[Long] = Some(e.servingId)

    override def idToString(id: Long): String = id.toString

    override def fetchDataFromDb(
      startSyncDate: LocalDate,
      maxSyncPeriod: Int,
    ): ZIO[FarmUserContext with DatabaseProvider, Throwable, (List[(PregnancyCheck, IntegrationState)], List[Long])] = {
      import com.cloudfarms.cfzio.context.FarmUserContext
      ZIO.scoped(
        for {
          context <- ZIO.service[FarmUserContext]
          dataSource <- DatabaseProvider.getTenantDataSource(context)
          res <- ZIO.acquireReleaseWith(ZIO.attemptBlocking(dataSource.getConnection)) { connection =>
            ZIO.succeed(connection.close())
          } { connection =>
            ZIO.attemptBlocking {
              DB(connection).readOnly { implicit session =>
                val upserts = {
                  sql"""
                       |select i.id,
                       |       i.pregnant,
                       |       i.state
                       |from suisag_pregnancycheck i
                       |left join servingevent e on i.id = e.id
                       |where i.state in ('new', 'changed', 'rejected_retry', 'change_rejected_retry')
                       |and (e.id is null or
                       |  e.actor_date >= $startSyncDate and e.actor_date >= current_date - $maxSyncPeriod
                       |)
                       |order by i.id desc
                       |""".stripMargin.map(rs => {
                    PregnancyCheck(
                      servingId = rs.long("id"),
                      pregnant = rs.booleanOpt("pregnant"),
                    ) -> IntegrationState.withName(rs.string("state"))
                  }).list.apply()
                }

                val deletes = Nil
                (upserts, deletes)
              }
            }
          }
        } yield res,
      )
    }

    def updateStatusInDb(statusValues: Seq[(Long, IntegrationState, Option[String])])(implicit dbSession: DBSession): Unit = {
      sql"""
           |with x(id, state, error) as (values ${statusValues.map(s => sqls"(${s._1}, ${s._2.entryName}, ${s._3})").reduce((a, b) =>
        sqls"$a, $b",
      )})
           |update suisag_pregnancycheck i set state = x.state::event_integration_state, error = x.error, upload_date = current_timestamp from x where i.id = x.id
           |""".stripMargin.execute.apply()
    }

    override def saveDownloadedEvents(data: SuisagData)(implicit dbSession: DBSession): Unit = {
      // Empty implementation sice Suisdata does not send the pregnancy check data to Cloudfarms
      ()
    }
  }
}

@jsonMemberNames(SnakeCase)
final case class PregnancyCheckReporting(
  upsert: List[PregnancyCheck],
)

object PregnancyCheckReporting {
  implicit val codec: JsonCodec[PregnancyCheckReporting] = DeriveJsonCodec.gen
}

@jsonMemberNames(SnakeCase)
final case class PregnancyCheckReportingRequest(
  farm: String,
  pregnancyChecks: PregnancyCheckReporting,
)

object PregnancyCheckReportingRequest {
  implicit val codec: JsonCodec[PregnancyCheckReportingRequest] = DeriveJsonCodec.gen
}
