/**
 * AgView Manager API
 *  Welcome to the API documentation for AgView Manager  The `swagger-ui` view can be found [here](/api-docs/swagger/). The `ReDoc` view can be found [here](/api-docs/redoc/). The swagger YAML document can be found [here](/api-docs/swagger.yaml).  To try the API on this page follow the steps below: 1. Execute the `/auth/org-token/` endpoint found under the Authentication section 2. Copy the JWT access token from the response 3. Click the Authorize green button found below these instructions 4. In the value field, type `Bearer` followed by a space and then paste the JWT access token 5. Click the Authorize green button found in the pop up 6. Execute the desired endpoint
 *
 * The version of the OpenAPI document: 1.0.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package integrations.agview.client

import integrations.agview.model.{ IncidentEnums, InlineObjectEnums, PremiseEnums, SwaggerMovementSerializerEnums }
import io.circe.{ Decoder, Encoder }
import io.circe.generic.AutoDerivation
import sttp.client3.circe.SttpCirceApi

object JsonSupport extends SttpCirceApi with AutoDerivation with DateSerializers {

  implicit val IncidentStatusDecoder: Decoder[IncidentEnums.Status] = Decoder.decodeEnumeration(IncidentEnums.Status)
  implicit val IncidentStatusEncoder: Encoder[IncidentEnums.Status] = Encoder.encodeEnumeration(IncidentEnums.Status)

  implicit val IncidentEmrsSharingStatusDecoder: Decoder[IncidentEnums.EmrsSharingStatus] =
    Decoder.decodeEnumeration(IncidentEnums.EmrsSharingStatus)
  implicit val IncidentEmrsSharingStatusEncoder: Encoder[IncidentEnums.EmrsSharingStatus] =
    Encoder.encodeEnumeration(IncidentEnums.EmrsSharingStatus)

  implicit val InlineObjectMovementTypeDecoder: Decoder[InlineObjectEnums.MovementType] =
    Decoder.decodeEnumeration(InlineObjectEnums.MovementType)
  implicit val InlineObjectMovementTypeEncoder: Encoder[InlineObjectEnums.MovementType] =
    Encoder.encodeEnumeration(InlineObjectEnums.MovementType)

  implicit val PremiseLocationTypeDecoder: Decoder[PremiseEnums.LocationType] = Decoder.decodeEnumeration(PremiseEnums.LocationType)
  implicit val PremiseLocationTypeEncoder: Encoder[PremiseEnums.LocationType] = Encoder.encodeEnumeration(PremiseEnums.LocationType)

  implicit val PremiseCreatedByOrgTypeDecoder: Decoder[PremiseEnums.CreatedByOrgType] =
    Decoder.decodeEnumeration(PremiseEnums.CreatedByOrgType)
  implicit val PremiseCreatedByOrgTypeEncoder: Encoder[PremiseEnums.CreatedByOrgType] =
    Encoder.encodeEnumeration(PremiseEnums.CreatedByOrgType)

  implicit val SwaggerMovementSerializerMovementTypeDecoder: Decoder[SwaggerMovementSerializerEnums.MovementType] =
    Decoder.decodeEnumeration(SwaggerMovementSerializerEnums.MovementType)
  implicit val SwaggerMovementSerializerMovementTypeEncoder: Encoder[SwaggerMovementSerializerEnums.MovementType] =
    Encoder.encodeEnumeration(SwaggerMovementSerializerEnums.MovementType)

  implicit val SwaggerMovementSerializerStatusDecoder: Decoder[SwaggerMovementSerializerEnums.Status] =
    Decoder.decodeEnumeration(SwaggerMovementSerializerEnums.Status)
  implicit val SwaggerMovementSerializerStatusEncoder: Encoder[SwaggerMovementSerializerEnums.Status] =
    Encoder.encodeEnumeration(SwaggerMovementSerializerEnums.Status)

}
