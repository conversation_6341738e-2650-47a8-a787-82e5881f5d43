package controllers.ws.feed.nedap

import javax.inject.Inject

import akka.actor.{ Actor, ActorRef }
import akka.event.LoggingReceive
import com.cloudfarms.integrator.integration.{ CommonResponsesTaskResult, GeneralError<PERSON><PERSON>ult, GeneralOKResult, ResponseFromIntegrator }
import com.google.inject.assistedinject.Assisted
import controllers.ws.feed.ActorWithRestartLogging
import play.api.libs.concurrent.InjectedActorSupport

import scala.collection.immutable.Set

/**
  * Created by <PERSON> on 17.10.2016.
  */
class NedapTakeOverTransponderActor @Inject() (
  val nedapDbUtisl: NedapDbUtils,
  val nedapGetAnimalDataActorFactory: NedapGetAnimalDataActor.Factory,
  @Assisted nedapOAuth2Actor: ActorRef,
  @Assisted nedapRequestContext: NedapRequestContext,
) extends ActorWithRestartLogging with InjectedActorSupport {

  self ! Start

  override def preRestart(reason: Throwable, message: Option[Any]): Unit = {
    log.error(reason, "Unexpected error when processing take over transponder from Nedap to handle request {}", nedapRequestContext)
    self ! Failed
    super.preRestart(reason, message)
  }

  def handleFailure: Receive = LoggingReceive {
    case Failed =>
      context.parent ! ResponseFromIntegrator(
        nedapRequestContext.requestId,
        CommonResponsesTaskResult.toExecuteActionResponseWrapper(GeneralErrorResult(
          "takeOverTransponder.failed",
          s"Take over animals' transponder numbers process failed.",
        )),
      )
      context stop self
  }

  override def receive: Receive = {
    (LoggingReceive {
      case Start =>
        var processingIds = Set.empty[AnimalId]

        nedapDbUtisl.getActiveAnimals(nedapRequestContext) match {
          case Nil =>
            context.parent ! ResponseFromIntegrator(
              nedapRequestContext.requestId,
              CommonResponsesTaskResult.toExecuteActionResponseWrapper(GeneralOKResult()),
            )
            context stop self
          case ids =>
            val id = ids.head
            injectedChild(nedapGetAnimalDataActorFactory(id, nedapOAuth2Actor, nedapRequestContext), s"nedap-get-animal-${id.cfId}")
            processingIds = processingIds + id

            context become processNextOne(ids drop 1, processingIds)
        }
    }: Receive) orElse handleFailure
  }

  def processNextOne(ids: Seq[AnimalId], processingIds: Set[AnimalId]): Receive = {
    (LoggingReceive {
      case NedapGetAnimalDataActor.AnimalFromNedap(animalId, optionalAnimalData) =>
        optionalAnimalData match {
          case Some(animalData) =>
            animalData.responder1.foreach { transponder =>
              if (animalId.transponder.map(_ != transponder).getOrElse(true)) {
                log.info("Update transponder for animal {} to value from Nedap {}", animalId, transponder)
                nedapDbUtisl.updateTransponderNumberFromNedap(animalId, transponder, nedapRequestContext)
              }
            }
          case None =>
            log.warning(
              "No data found for animal {} in Nedap when taking over transponder (requestContext is {})",
              animalId,
              nedapRequestContext,
            )
        }

        ids match {
          case head +: tail =>
            log.debug(s"Going to get Nedap data for animal ${head}. Still have to be processed ${tail.size} animal.")
            injectedChild(nedapGetAnimalDataActorFactory(head, nedapOAuth2Actor, nedapRequestContext), s"nedap-get-animal-${head.cfId}")
            context become processNextOne(tail, processingIds - animalId + head)
          case Nil =>
            (processingIds - animalId).toSeq match {
              case Nil =>
                log.debug(s"Animals processed. Finish the take over locations action.")
                context.parent ! ResponseFromIntegrator(
                  nedapRequestContext.requestId,
                  CommonResponsesTaskResult.toExecuteActionResponseWrapper(GeneralOKResult()),
                )
                context stop self
              case idsNotFinishedYet =>
                log.debug(s"Going to process the last next ${idsNotFinishedYet.size} animals")
                context become processNextOne(ids, idsNotFinishedYet.toSet)
            }
        }
    }: Receive) orElse handleFailure
  }
}

object NedapTakeOverTransponderActor {
  trait Factory {
    def apply(nedapOAuth2Actor: ActorRef, nedapRequestContext: NedapRequestContext): Actor
  }
}
