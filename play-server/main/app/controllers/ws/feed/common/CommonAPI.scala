package controllers.ws.feed.common

import akka.stream.Materializer
import play.api.libs.ws.{ WSRequest, WSResponse }
import play.api.libs.ws.ahc.AhcWSClient
import reactapp.shared.integration.feedsystem.common.RequestInfo

import scala.async.Async.{ async, await }
import scala.concurrent.{ ExecutionContext, Future }
import scala.language.implicitConversions

trait CommonAPI {

  import controllers.ws.feed.common.CommonAPI._

  implicit protected val materializer: Materializer
  private val client: AhcWSClient = AhcWSClient()

  protected trait Request {
    def make(): (WSRequest, RequestInfo) = (
      client.url(s"$apiURL/$endpoint")
        .withMethod(method)
        .withQueryStringParameters(params: _*)
        .withHttpHeaders(headers: _*)
        .withBody(body),
      RequestInfo(s"$apiURL/$endpoint", method, params, headers, body),
    )

    def apiURL: String
    def method: Method
    def endpoint: String = ""
    def params: Seq[(String, String)] = Seq.empty
    def headers: Seq[(String, String)] = Seq.empty
    def body: String = ""
  }

  protected trait AuthInParams extends Request {
    def password: String
    override def params: Seq[(String, String)] = Seq("password" -> password)
  }

  protected trait OAuth2 extends Request {
    def bearerToken: String
    override def headers: Seq[(String, String)] = super.headers :+ "Authorization" -> s"Bearer $bearerToken"
  }

  protected trait BasicAuth extends Request {
    def basicToken: String
    override def headers: Seq[(String, String)] = super.headers :+ "Authorization" -> s"Basic $basicToken"
  }

}

object CommonAPI {

  implicit class AugmentedWSResponse(val wsResponse: WSResponse) {
    def isNotFound: Boolean = wsResponse.status == 404
    def isUnauthorized: Boolean = wsResponse.status == 401
    def isForbidden: Boolean = wsResponse.status == 403
    def isBadRequest: Boolean = wsResponse.status == 400
    def isServerError: Boolean = wsResponse.status == 500
    def isOK: Boolean = wsResponse.status >= 200 && wsResponse.status < 300
    def isOverRateLimit: Option[Int] = if (wsResponse.status == 429) wsResponse.header("Retry-After").map(_.toInt) else None
  }

  sealed trait Method { val name: String }
  object Method {
    case object GET extends Method { override val name = "GET" }
    case object POST extends Method { override val name = "POST" }
    case object PUT extends Method { override val name = "PUT" }
    case object DELETE extends Method { override val name = "DELETE" }

    implicit def MethodToString(method: Method): String = method.name
  }

  sealed trait PredefinedHeader { val header: (String, String) }
  object PredefinedHeader {
    case object AcceptJson extends PredefinedHeader {
      override val header: (String, String) = "Accept" -> "application/json"
    }
    case object ContentTypeJson extends PredefinedHeader {
      override val header: (String, String) = "Content-type" -> "application/json"
    }
  }

  trait APIServiceWTokenRefresh[API, TOKEN] {
    implicit val ec: ExecutionContext

    protected var apiInternal: API

    protected def getToken: TOKEN
    protected def renewToken: Future[API]

    def api: API = apiInternal

    def execute(apiCall: TOKEN => (WSRequest, RequestInfo)): Future[(WSResponse, RequestInfo)] = async {
      val (call: WSRequest, info: RequestInfo) = apiCall(getToken)
      await(call.execute()) match {
        case response if response.isUnauthorized =>
          apiInternal = await(renewToken)
          val (call: WSRequest, info: RequestInfo) = apiCall(getToken)
          (await(call.execute()), info)
        case response => (response, info)
      }
    }

  }

}
