package controllers.ws.feed.jyga

import java.time.Instant
import _root_.pushka.json._
import _root_.pushka.Ast
import com.google.common.io.BaseEncoding
import controllers.ws.feed.jyga.JygaClient._
import play.api.libs.ws.{ WSRequest, WSResponse }
import pushka.annotation._
import sjsgrid.shared.common.UnexpectedMatch

import java.nio.charset.StandardCharsets
import scala.async.Async.{ async, await }
import scala.concurrent.{ ExecutionContext, Future }

case class JygaClient(token: String, logFunction: PersistRequest => Unit, jygaWS: JygaWS)(implicit ec: ExecutionContext) {

  val encodedToken: String = BaseEncoding.base64().encode(s"$token:".getBytes(StandardCharsets.UTF_8))

  private def successStatusHandler[T](f: WSResponse => T): PartialFunction[WSResponse, T] = {
    case r: WSResponse if r.status <= 204 => f(r)
  }

  private def errorStatusHandler[T](requestHolder: WSRequest): PartialFunction[WSResponse, T] = {
    case r: WSResponse if r.status == 401 => throw JygaAuthorizationException
    case r: WSResponse                    => throw JygaGeneralException("Status: " + r.status + "\nBody: " + r.body)
  }

  case class RequestData(
    url: String,
    method: String = "GET",
    bodyOpt: Option[String] = None,
    requestTime: Instant = Instant.now,
  )

  private def writeRequestReplyToDB(response: WSResponse, requestData: RequestData): WSResponse = {
    logFunction(PersistRequest(
      requestData.requestTime,
      requestData.url,
      requestData.method,
      "",
      requestData.bodyOpt.getOrElse(""),
      Instant.now,
      response.status,
      response.body,
    ))
    response
  }

  private def createRequest(requestData: RequestData): WSRequest = {
    val request = jygaWS.client.url(requestData.url).withHttpHeaders(
      ("Authorization", s"Basic $encodedToken"),
      ("Content-Type", "application/json"),
    ).withMethod(requestData.method)

    requestData.bodyOpt match {
      case Some(body) => request.withBody(body)
      case _          => request
    }
  }

  private def executeRequest[A](requestData: RequestData, responseTranslator: String => A): Future[A] = {
    val request = createRequest(requestData)
    request.execute().map(response => writeRequestReplyToDB(response, requestData)).map {
      successStatusHandler[A](response => responseTranslator(response.body)).orElse {
        errorStatusHandler(request)
      }
    }
  }

  /**
   * Read the data from the Feed System. These are the "live" data the system works with
   *
   * @param cfAnimals Sequence of animals in Cloudfarms we are searching for in JYGA
   * @return Sequence of animals found in JYGA's system
   */
  def getActiveJygaSows(cfAnimals: Seq[SimplifiedAnimal]): Future[JygaDataRead] = {
    getJygaSowsRecursive(
      cfAnimals,
      None,
      Seq.empty,
      (pageNumberIn, perPageIn, cfAnimalsIn) => {
        getJygaSowsCall(
          pageNumber = pageNumberIn,
          perPage = perPageIn,
          cfAnimals = cfAnimalsIn,
        ).map(result => (result.data, result.meta))
      },
    ).map(
      JygaDataRead,
    )
  }

  /**
   * Getter function that points to sows - the data farmer actually works with (read-only to us)
   *
   * @param cfAnimals  Sequence of animals in Cloudfarms we are searching for in JYGA
   * @param pageNumber current page number
   * @return List of (up to) 100 sows
   */
  private def getJygaSowsCall(
    pageNumber: Int,
    perPage: Int,
    cfAnimals: Seq[SimplifiedAnimal],
    notRetired: Boolean = true,
  ): Future[SowDataStruct] = {
    val requestData = RequestData(
      s"https://api.gestal.cloud/integration/sows?per_page=$perPage&page=$pageNumber",
      "GET",
      Some(s"""{"query": {"where": "${formatJygaFilter(cfAnimals, notRetired)}"}}"""),
    )
    executeRequest(requestData, response => read[SowDataStruct](response))
  }

  /**
   * Reads data of active sows that we entered into Feed System, but the changes haven't been approved yet
   *
   * @param cfAnimals Sequence of animals in Cloudfarms we are searching for in JYGA
   * @return Sequence of all mirror sows
   */
  def getJygaMirrorSows(cfAnimals: Seq[SimplifiedAnimal], onlyActive: Boolean = true): Future[Seq[SowMirrorData]] = {
    getJygaSowsRecursive(
      cfAnimals,
      None,
      Seq.empty,
      (pageNumberIn, perPageIn, cfAnimalsIn) => {
        getJygaSowMirrorsCall(
          pageNumber = pageNumberIn,
          perPage = perPageIn,
          cfAnimals = cfAnimalsIn,
          onlyActive = onlyActive,
        ).map(result => (result.data, result.meta))
      },
    )
  }

  /**
   * Getter function that points to mirror-sows - the data we can change and farmer can approve the changes
   *
   * @param cfAnimals  Sequence of animals in Cloudfarms we are searching for in JYGA
   * @param pageNumber Current page number
   * @param onlyActive Check only active sows or all
   * @return List of (up to) 100 sows
   */
  private def getJygaSowMirrorsCall(
    cfAnimals: Seq[SimplifiedAnimal],
    pageNumber: Int,
    perPage: Int,
    onlyActive: Boolean,
  ): Future[SowMirrorDataStruct] = {
    val requestData = RequestData(
      s"https://api.gestal.cloud/integration/sow-mirrors?per_page=$perPage&page=$pageNumber",
      "GET",
      Some(s"""{"query": {"where": "${formatJygaFilter(cfAnimals, onlyActive)}"}}"""),
    )
    executeRequest(
      requestData,
      response => {
        try
          read[SowMirrorDataStruct](response)
        catch {
          case ex: Throwable =>
            println(s"Exception while parsing response into SowMirrorDataStruct - ${ex.getMessage}")

            SowMirrorDataStruct(
              Seq.empty,
              MetaData(
                total = 0,
                pageTotal = 0,
                pages = 1,
                perPage = 0,
                currentPage = 1,
                nextPage = false,
                previousPage = false,
                firstPage = true,
                lastPage = true,
                outOfRange = false,
              ),
            )
        }
      },
    )
  }

  /**
   * Common method to retrieve sows or mirror-sow. @dataGetter function is used to target the correct API in Jyga
   * Since the results are paged, metadata is passed between passes to page correctly.
   * This method calls itself recursively until all data is acquired.
   *
   * @param cfAnimals   List of animal from CF we are searching for in Jyga
   * @param metaDataOpt metadata from previous run
   * @param result      result from previous runs - the current results are appended to it
   * @param dataGetter  dataGetter function -> points to either sows or mirror-sow
   * @tparam A SowDataStruct or SowMirrorDataStruct
   * @return Sequence of [A], wrapped in a Future
   */
  private def getJygaSowsRecursive[A](
    cfAnimals: Seq[SimplifiedAnimal],
    metaDataOpt: Option[MetaData],
    result: Seq[A],
    dataGetter: (Int, Int, Seq[SimplifiedAnimal]) => Future[(Seq[A], MetaData)],
  ): Future[Seq[A]] = {
    async {
      val metadata = metaDataOpt.getOrElse(
        MetaData(
          total = 0,
          pageTotal = 1,
          pages = 100,
          perPage = 1000,
          currentPage = 0,
          nextPage = true,
          previousPage = false,
          firstPage = true,
          lastPage = false,
          outOfRange = false,
        ),
      )

      if (!metadata.lastPage && !metadata.outOfRange) {
        await {
          println(s"Querying page: ${metadata.currentPage + 1}")
          println(s"Pagination: ${metadata.currentPage + 1}/${metadata.pages}")
          if (metadata.outOfRange) {
            println(s"Warning: Out of range")
          }

          val (newData, newMetaData) = await(dataGetter(metadata.currentPage + 1, metadata.perPage, cfAnimals))
          getJygaSowsRecursive(cfAnimals, Some(newMetaData), result ++ newData, dataGetter)
        }
      } else {
        println(s"End of pagination")

        result
      }
    }
  }

  /**
   * Formats the WHERE clause in JYGA's format
   *
   * @param cfAnimals  Sequence of animals we are looking for in JYGA
   * @param onlyActive Search only active or all animals
   * @return WHERE clause for JYGA query
   */
  private def formatJygaFilter(cfAnimals: Seq[SimplifiedAnimal], onlyActive: Boolean): String = {
    if (onlyActive)
      "state!=retired"
    else
      ""
  }

  /**
   * Writes into mirror-sows database, from which the Feed System asks the farmer to manually approve the changes.
   *
   * @param jygaDataWrite Mirror Sow data List
   * @return Sequence of Tuples [Sow ID, Error message if there's a problem with this sow]
   */
  def writeJygaData(jygaDataWrite: JygaDataWrite): Future[Seq[(Option[Long], String)]] = {
    jygaDataWrite.jygaSows.foldLeft(
      Future(
        Seq.empty[(Option[Long], String)],
      ),
    ) {
      case (previousErrors, (cfIdOpt, sowMirrorData)) =>
        for {
          // This first part forwards previous errors to next fold
          // Also runs the upsert on next animal and creates errors (if needed)
          errors <- previousErrors
          errorOpt <- upsertSow(sowMirrorData).map(_ => None: Option[String]).recover {
            case JygaAuthorizationException => Some("AuthorizationError")
            case JygaGeneralException(text) => Some(text)
          }
        } yield {
          // If there was no problem, just forward previous errors
          // If there was a problem, append it to previous error with Cloudfarms's ID
          errorOpt match {
            case None        => errors
            case Some(error) => errors ++ Seq(cfIdOpt -> error)
          }
        }
    }
  }

  /**
   * Creates or updates the sow in mirror-sows
   *
   * @param sowData Sow mirror object - data in Jyga's sow format
   * @return
   */
  private def upsertSow(sowData: SowMirrorData): Future[Unit] = {
    val requestData = {
      if (sowData.cloudId == "") {
        RequestData(
          s"https://api.gestal.cloud/integration/sow-mirrors",
          "POST",
          Some(write(sowData)),
        )
      } else {
        RequestData(
          s"https://api.gestal.cloud/integration/sow-mirrors/${sowData.cloudId}",
          "PATCH",
          Some(write(sowData)),
        )
      }
    }

    executeRequest(requestData, _ => ())
  }

  /**
   * Sequentially call delete function on each sow in list.
   * @param animalsToRetire Sequence of animals to delete
   * @return
   */
  def deleteMirrorSows(animalsToRetire: Seq[SowMirrorData]): Future[Seq[String]] = {
    animalsToRetire.foldLeft(
      // initialize empty error sequence
      Future(Seq.empty[String]),
    ) {
      case (previousErrors, animal) =>
        // This first part forwards previous errors to next fold
        // Also runs the request for next animal and creates errors (if needed)
        previousErrors.flatMap {
          errors =>
            {
              // If there was no problem, just forward previous errors
              sendDeleteMirrorSowRequest(animal.cloudId).map {
                _ => errors
              }.recover {
                // If there was a problem, format the text to String and it appends to previous errors
                case JygaAuthorizationException => errors :+ "AuthorizationError"
                case JygaGeneralException(text) => errors :+ text
                case e                          => errors :+ e.toString
              }
            }
        }
    }
  }

  /**
   * Deletes one mirror sow
   * @param cloudId Internal Jyga ID of a sow
   * @return
   */
  private def sendDeleteMirrorSowRequest(cloudId: String): Future[Unit] = {
    val requestData = RequestData(
      s"https://api.gestal.cloud/integration/sow-mirrors/$cloudId",
      "DELETE",
    )
    executeRequest(requestData, _ => ())
  }
}

object JygaClient {
  implicit val localDateRW: pushka.RW[Instant] = {
    new pushka.RW[Instant] {
      def read(value: Ast): Instant = value match {
        case x: Ast.Str => Instant.parse(x.value) match {
            case i: Instant => i
            case unexpected => UnexpectedMatch(unexpected)
          }
        case unexpected => UnexpectedMatch(unexpected)
      }

      def write(i: Instant): Ast.Str = Ast.Str(i.toString)
    }
  }

  /**
   * Return the current state of animal and date, when the change happened.
   * State is a enum used by Jyga - here we translate from our system to theirs
   *
   * @param cfAnimal Animal in Cloudfarms
   * @return Tuple(state_of_the_animal, date_when_the_change_happened)
   */
  private def getAnimalStatusForJyga(cfAnimal: CfAnimal): JygaAnimalStatus = {
    (cfAnimal.servingDateOpt, cfAnimal.farrowingDateOpt, cfAnimal.weaningDateOpt) match {
      case (None, None, None) =>
        JygaAnimalStatus("gilt", cfAnimal.birthdateOpt.getOrElse(Instant.MIN), "breeding")
      case (Some(servingDate), farrowingDateOpt, weaningDateOpt)
          if Seq(Some(servingDate), farrowingDateOpt, weaningDateOpt).flatten.max == servingDate =>
        JygaAnimalStatus("gestating", servingDate, "gestation")
      case (servingDateOpt, Some(farrowingDate), weaningDateOpt)
          if Seq(servingDateOpt, Some(farrowingDate), weaningDateOpt).flatten.max == farrowingDate =>
        JygaAnimalStatus("farrowed", farrowingDate, "lactation")
      case (servingDateOpt, farrowingDateOpt, Some(weaningDate))
          if Seq(servingDateOpt, farrowingDateOpt, Some(weaningDate)).flatten.max == weaningDate =>
        JygaAnimalStatus("weaned", weaningDate, "breeding")
      case (_, _, _) =>
        JygaAnimalStatus("unknown", Instant.now(), "unknown") // never should get here, but not having a "Catch-all" justo be sure
    }
  }

  /**
   * @param jygaSows Sequence of Jyga sow objects
   */
  case class JygaDataRead(
    jygaSows: Seq[SowData],
  )

  /**
   * @param jygaSows Sequence of ID in Cloudfarms - Jyga's mirror sow object
   */
  case class JygaDataWrite(
    jygaSows: Seq[(Option[Long], SowMirrorData)] = Seq.empty,
  )

  case class JygaError(generalError: String)

  @pushka
  case class SowDataStruct(data: Seq[SowData], meta: MetaData)

  @pushka
  case class SowMirrorDataStruct(data: Seq[SowMirrorData], meta: MetaData)

  /** Represents the Sow from Jyga
   *
   * @param cloudId Unique identifier used to update the animal
   * @param gestalId Unique identified set by Jyga
   * @param pinTag Represents Sow name in Cloudfarms
   * @param rfid1 Represents RFID-LF in Cloudfarms
   * @param rfid2 Represents RFID-UHF in Cloudfarms
   * @param state Represents current state of animal. Can be "gilt", "gestating", "farrowed", "weaned", "unknown" or "retired"
   * @param stateUpdatedAt Represents the timestamp of last change of state
   */
  @pushka
  case class SowData(
    @key("cloud_id") cloudId: Option[String],
    @key("gestal_id") gestalId: Option[Long],
    @key("pin_tag") pinTag: Option[String],
    rfid1: Option[Long],
    rfid2: Option[Long],
    state: String,
    @key("state_updated_at") stateUpdatedAt: Option[Instant],
    @key("last_location") lastLocation: Option[String],
    @key("location_updated_at") locationUpdatedAt: Option[Instant],
    section: Option[String],
    parity: Option[Long],
    genetic: Option[String],
    @key("birth_at") birthAt: Option[Instant],
    @key("entry_at") entryAt: Option[Instant],
    tattoo: Option[String],
    dam: Option[String],
    sire: Option[String],
    adapted: Option[Boolean],
    accepted: Option[Boolean],
    batch: Option[String],
    notes: Option[String],
    @key("created_at") createdAt: Instant,
    @key("updated_at") updatedAt: Instant,
  ) {
    def markAnimalAsRetired(): SowMirrorData = {
      SowMirrorData(
        cloudId.getOrElse(""),
        cloudId.getOrElse(""),
        gestalId,
        pinTag,
        rfid1,
        rfid2,
        "retired",
        Some(Instant.now),
        lastLocation,
        locationUpdatedAt,
        section,
        parity,
        genetic,
        birthAt,
        entryAt,
        tattoo,
        dam,
        sire,
        adapted,
        accepted,
        batch,
        notes,
      )
    }

    def createSimplified(): SimplifiedAnimal = {
      SimplifiedAnimal(
        pinTag.getOrElse(""),
        cloudId,
        rfid1.flatMap(value => if (value == 0) None else Some(value.toString)),
        rfid2.flatMap(value => if (value == 0) None else Some(value.toString)),
      )
    }
  }

  /** Represents the Sow Mirror from Jyga
   *
   * @param cloudId Unique identifier used to update the animal
   * @param vendorId Unique identifier set by Cloudfarms
   * @param gestalIdOpt Unique identified set by Jyga
   * @param pinTag Represents Sow name in Cloudfarms
   * @param rfid1 Represents RFID-LF in Cloudfarms
   * @param rfid2 Represents RFID-UHF in Cloudfarms
   * @param state Represents current state of animal. Can be "gilt", "gestating", "farrowed", "weaned", "unknown" or "retired"
   * @param stateUpdatedAt Represents the timestamp of last change of state
   */
  @pushka
  case class SowMirrorData(
    @key("cloud_id") cloudId: String,
    @key("vendor_id") vendorId: String,
    @key("gestal_id") gestalIdOpt: Option[Long],
    @key("pin_tag") pinTag: Option[String],
    rfid1: Option[Long],
    rfid2: Option[Long],
    state: String,
    @key("state_updated_at") stateUpdatedAt: Option[Instant],
    @key("last_location") lastLocation: Option[String],
    @key("location_updated_at") locationUpdatedAt: Option[Instant],
    section: Option[String],
    parity: Option[Long],
    genetic: Option[String],
    @key("birth_at") birthAt: Option[Instant],
    @key("entry_at") entryAt: Option[Instant],
    tattoo: Option[String],
    dam: Option[String],
    sire: Option[String],
    adapted: Option[Boolean],
    accepted: Option[Boolean],
    batch: Option[String],
    notes: Option[String],
  ) {

    def isSameAnimal(cfAnimal: CfAnimal): Boolean =
      (cfAnimal.cloudId.nonEmpty && vendorId == cfAnimal.cloudId.get) || (pinTag.nonEmpty && pinTag.get == cfAnimal.animalNumber)

    /**
     * Compares Jyga animal against animal data in Cloudfarms.
     * We only compare data that can be updated by Integration.
     *
     * @param cfAnimal same animal in Cloudfarms database
     * @return true if it's the same, false if it has differences
     */
    def isTheSame(cfAnimal: CfAnimal): Boolean = {
      val animalStatus = getAnimalStatusForJyga(cfAnimal)

      (pinTag.isEmpty || pinTag.get == cfAnimal.animalNumber) &&
      rfid1.contains(cfAnimal.transponder) && (
        rfid2 == cfAnimal.transponder2Opt || (
          rfid2.contains(0.toLong) &&
            cfAnimal.transponder2Opt.isEmpty
        )
      ) && (cfAnimal.location.isEmpty || lastLocation == cfAnimal.location) &&
      state.contains(animalStatus.state)
    }

    def combineWithCfAnimal(cfAnimal: CfAnimal): SowMirrorData = {
      val animalStatus = getAnimalStatusForJyga(cfAnimal)

      // This came really handy when debugging issues
      // I wrote this kind of code and deleted it three times -> so now it stays
//      if (pinTag.isEmpty || pinTag.get == cfAnimal.animalNumber) {
//        println("pinTags are a match")
//      } else {
//        println(s"$pinTag -> ${cfAnimal.animalNumber}")
//      }
//
//      if (rfid1.contains(cfAnimal.transponder)) {
//        println("rfid1 is a match")
//      } else {
//        println(s"$pinTag -> ${cfAnimal.animalNumber}")
//      }
//
//      if (rfid2 == cfAnimal.transponder2Opt) {
//        println("rfid2 is a match")
//      } else {
//        println(s"$pinTag -> ${cfAnimal.animalNumber}")
//      }
//
//      if (rfid2.contains(0.toLong) && cfAnimal.transponder2Opt.isEmpty) {
//        println("rfid2 was empty")
//      } else {
//        println(s"$pinTag -> ${cfAnimal.animalNumber}")
//      }
//
//      if (cfAnimal.location.isEmpty || lastLocation == cfAnimal.location) {
//        println("location is fine")
//      } else {
//        println(s"$pinTag -> ${cfAnimal.animalNumber}")
//      }
//
//      if (state.contains(animalStatus.state)) {
//        println("state is fine")
//      } else {
//        println(s"$pinTag -> ${cfAnimal.animalNumber}")
//      }

      SowMirrorData(
        cloudId,
        cloudId,
        gestalIdOpt,
        Some(cfAnimal.animalNumber),
        Some(cfAnimal.transponder),
        cfAnimal.transponder2Opt,
        animalStatus.state,
        Some(animalStatus.stateChanged),
        cfAnimal.location,
        if (cfAnimal.location.nonEmpty) Seq(cfAnimal.locationUpdatedAtOpt, locationUpdatedAt).flatten.headOption else None,
        Some(animalStatus.section),
        Some(cfAnimal.parity.toLong),
        genetic,
        cfAnimal.birthdateOpt,
        entryAt,
        tattoo,
        dam,
        sire,
        adapted,
        accepted,
        batch,
        notes,
      )
    }

    def markAnimalAsRetired(): SowMirrorData =
      this.copy(state = "retired", stateUpdatedAt = Some(Instant.now()))

    override def toString: String = {
      "SowMirrorData(" + "cloudId:" + cloudId + ", vendorId:" + vendorId + ", gestalId:" + gestalIdOpt +
        ", pinTag:" + pinTag + ", rfid1:" + rfid1 + ", rfid2:" + rfid2 +
        ", state:" + state + ", stateUpdatedAt:" + stateUpdatedAt +
        ", lastLocation:" + lastLocation + ", locationUpdatedAt:" +
        locationUpdatedAt + ", section:" + section + ", parity:" + parity +
        ", genetic:" + genetic + ", birthAt:" + birthAt + ", entryAt:" + entryAt +
        ", tattoo:" + tattoo + ", dam:" + dam + ", sire:" + sire + ", adapted:" +
        adapted + ", accepted:" + accepted + ", batch:" + batch + ", notes:" +
        notes + ")\n"
    }
  }

  object SowMirrorData {
    def createFromCfAnimal(cfAnimal: CfAnimal, gestalIdOpt: Option[Long] = None): SowMirrorData = {
      val animalStatus = getAnimalStatusForJyga(cfAnimal)

      SowMirrorData(
        cloudId = "",
        // vendor_id unique in Jyga's database; By using sow number here, we'll avoid duplicities in data we create
        vendorId = cfAnimal.animalNumber,
        gestalIdOpt = gestalIdOpt,
        pinTag = Some(cfAnimal.animalNumber),
        rfid1 = Some(cfAnimal.transponder),
        rfid2 = cfAnimal.transponder2Opt,
        state = animalStatus.state,
        stateUpdatedAt = Some(animalStatus.stateChanged),
        lastLocation = cfAnimal.location,
        locationUpdatedAt = if (cfAnimal.location.nonEmpty) cfAnimal.locationUpdatedAtOpt else None,
        section = Some(animalStatus.section),
        parity = Some(cfAnimal.parity.toLong),
        genetic = None,
        birthAt = cfAnimal.birthdateOpt,
        entryAt = None,
        tattoo = None,
        dam = None,
        sire = None,
        adapted = None,
        accepted = None,
        batch = None,
        notes = None,
      )
    }
  }

  @pushka
  case class MetaData(
    total: Int,
    @key("page_total") pageTotal: Int,
    pages: Int,
    @key("per_page") perPage: Int,
    @key("current_page") currentPage: Int,
    @key("next_page") nextPage: Boolean,
    @key("previous_page") previousPage: Boolean,
    @key("first_page") firstPage: Boolean,
    @key("last_page") lastPage: Boolean,
    @key("out_of_range") outOfRange: Boolean,
  )
}
