package controllers.ext;

import controllers.SellingTransferController;
import controllers.reports.JasperController;
import dashboards.ShortUrlEncoded;
import com.cloudfarms.settings.Setting;
import play.i18n.*;
import play.libs.ws.WSClient;
import play.mvc.*;
import security.*;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.concurrent.CompletableFuture;

/**
 * Created by grayman on 30/10/2015.
 */
@Singleton
public class DirectLinksController extends Controller {


    private final SellingTransferController sellingTransferController;
    private final ApplicationUserHandler applicationUserHandler;
    private final JasperController jasperController;
    private final MessagesApi messagesApi;
    private final Setting setting;
    private final WSClient wsClient;

    @Inject
    public DirectLinksController(ApplicationUserHandler applicationUserHandler, SellingTransferController sellingTransferController, JasperController jasperController, MessagesApi messagesApi, Setting setting, WSClient wsClient) {
        this.applicationUserHandler = applicationUserHandler;
        this.sellingTransferController = sellingTransferController;
        this.jasperController = jasperController;
        this.messagesApi = messagesApi;
        this.setting = setting;
        this.wsClient = wsClient;
    }


    public CompletableFuture<Result> shippingListExternal(String farmIdCode, String sellingIdCode, String checkCode, Http.Request request) throws Exception {
        final Long farmId = ShortUrlEncoded.decode(farmIdCode);
        final Long sellingId = ShortUrlEncoded.decode(sellingIdCode);
        String checkCodeCheck = ShortUrlEncoded.apply(com.cloudfarms.pigs.sync.Utils.shortHash(farmIdCode + "*" + sellingIdCode));
        if (farmId == null || sellingId == null || !checkCodeCheck.equals(checkCode)) return CompletableFuture.completedFuture(notFound());

        if (setting.getFarm(farmId).isDefined()) {
            final long theirRootId = setting.getFarm(farmId).get().rootId();
            final ApplicationUser currentUser = applicationUserHandler.current(request);
            final boolean sameHolding = currentUser != null && theirRootId == currentUser.getRootId();
            return CompletableFuture.completedFuture(applicationUserHandler.doAsSystem(request, farmId, (r) -> sellingTransferController.shippingListJson(sellingId, r, sameHolding)));
        } else {
            String server = setting.getFarmServerUrlJava(farmId);
            if (server == null) return CompletableFuture.completedFuture(notFound());
            String nextUrl = server + "/api/export-gilts-json/" + farmIdCode + "/" + sellingIdCode + "/" + checkCode;
            return wsClient.url(nextUrl).get().toCompletableFuture().handle((resp, err) -> {
                if (err != null) {
                    return internalServerError(err.getMessage());
                }
                if (resp.getStatus() < 300) {
                    return ok(resp.asJson());
                } else {
                    return status(resp.getStatus());
                }
            });
        }
    }


    public Result shippingListExternalCsv(String farmIdCode, String sellingIdCode, String checkCode, Http.Request request) throws Exception {
        final Long farmId = ShortUrlEncoded.decode(farmIdCode);
        final Long sellingId = ShortUrlEncoded.decode(sellingIdCode);
        String checkCodeCheck = ShortUrlEncoded.apply(com.cloudfarms.pigs.sync.Utils.shortHash(farmIdCode + "*" + sellingIdCode));
        if (farmId == null || sellingId == null || !checkCodeCheck.equals(checkCode)) return notFound();

        return applicationUserHandler.doAsSystem(request, farmId, (r) -> sellingTransferController.shippingListCsv(sellingId, r));
    }


    public Result shippingListExternalTxt(String farmIdCode, String sellingIdCode, String checkCode, Http.Request request) throws Exception {
        final Long farmId = ShortUrlEncoded.decode(farmIdCode);
        final Long sellingId = ShortUrlEncoded.decode(sellingIdCode);
        String checkCodeCheck = ShortUrlEncoded.apply(com.cloudfarms.pigs.sync.Utils.shortHash(farmIdCode + "*" + sellingIdCode));
        if (farmId == null || sellingId == null || !checkCodeCheck.equals(checkCode)) return notFound();

        return applicationUserHandler.doAsSystem(request, farmId, (r) -> sellingTransferController.shippingListTxt(sellingId, r));
    }


    public Result shippingListExternalVendor(String farmIdCode, String sellingIdCode, String checkCode, Http.Request request) throws Exception {
        final Long farmId = ShortUrlEncoded.decode(farmIdCode);
        final Long sellingId = ShortUrlEncoded.decode(sellingIdCode);
        String checkCodeCheck = ShortUrlEncoded.apply(com.cloudfarms.pigs.sync.Utils.shortHash(farmIdCode + "*" + sellingIdCode));
        if (farmId == null || sellingId == null || !checkCodeCheck.equals(checkCode)) return notFound();

        return applicationUserHandler.doAsSystem(request, farmId, (r) -> sellingTransferController.shippingListVendor(sellingId, r));
    }

    public Result shippingListExternalPdf(String farmIdCode, String sellingIdCode, String checkCode, Http.Request firstRequest) throws Exception {
        final Long farmId = ShortUrlEncoded.decode(farmIdCode);
        final Long sellingId = ShortUrlEncoded.decode(sellingIdCode);
        String checkCodeCheck = ShortUrlEncoded.apply(com.cloudfarms.pigs.sync.Utils.shortHash(farmIdCode + "*" + sellingIdCode));
        if (farmId == null || sellingId == null || !checkCodeCheck.equals(checkCode)) return notFound();

        return applicationUserHandler.doSystemTransactionInFarm(firstRequest, farmId, (request, em) -> {
            final ApplicationUser user = applicationUserHandler.current(request);
            return jasperController.shippingList(request, user, em, sellingId);
        });
    }


    public Result shippingListExternalWelcome(String farmIdCode, String sellingIdCode, String checkCode, Http.Request request) throws Exception {
        final Long farmId = ShortUrlEncoded.decode(farmIdCode);
        final Long sellingId = ShortUrlEncoded.decode(sellingIdCode);
        String checkCodeCheck = ShortUrlEncoded.apply(com.cloudfarms.pigs.sync.Utils.shortHash(farmIdCode + "*" + sellingIdCode));
        if (farmId == null || sellingId == null || !checkCodeCheck.equals(checkCode)) return notFound();
        final Messages messages = messagesApi.preferred(request);
        return applicationUserHandler.doAsSystem(request, farmId, (r) -> sellingTransferController.shippingListHtml(sellingId, farmIdCode, sellingIdCode, checkCode, r));
    }


}
