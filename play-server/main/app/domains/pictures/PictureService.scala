package domains.pictures

import akka.pattern.Patterns.ask
import background.PictureActors
import javax.inject.Inject
import models.handlers.PictureHandler
import play.api.libs.Files.TemporaryFile
import play.api.mvc.MultipartFormData
import scalikejdbc.DBSession

import java.io.File
import scala.concurrent.{ ExecutionContext, Future }

class PictureService @Inject() (
  pictureHandler: PictureHandler,
  pictureRepository: PictureRepository,
  pictureActors: PictureActors,
)(implicit ec: ExecutionContext) {

  def upload(farmId: Long, image: MultipartFormData.FilePart[TemporaryFile])(implicit dbSession: DBSession): Unit = {
    val pictureId = pictureRepository.insert
    val baseFile = pictureHandler.picturePath(farmId, pictureId)
    baseFile.getParentFile.mkdirs()
    image.ref.copyTo(baseFile, replace = true)
    pictureActors.ACTOR_REF.tell(new PictureActors.Transfer(farmId, pictureId, false), null)
  }

  def getPicture(farmId: Long, id: Long)(implicit dbSession: DBSession): Future[Option[File]] = {
    ask(pictureActors.ACTOR_REF, new PictureActors.Transfer(farmId, id, true), 10000).map { (o: Any) =>
      val picture = o.asInstanceOf[File]
      if (!picture.exists || !picture.isFile)
        None
      else
        Some(picture)
    }
  }
}
