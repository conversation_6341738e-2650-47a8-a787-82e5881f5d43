package domains.kpi.reports

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ObjectNode
import domains.kpi.RawKpiProviderSource
import domains.kpi.calculator.KpiCalculatorService
import models.handlers.SettingsHandler
import play.api.libs.json._
import reactapp.shared.kpi.{ Kpi, KpiConversions }
import reports.ReportConstants.{ KPI_CODE, LINE_ID, REPORT_SECTION_ID }
import reports.dataprovider.json.IReportJsonDataProvider
import tenant.AccountFarmLangSession

import java.util.Date
import scala.math.BigDecimal.RoundingMode.HALF_UP

/**
  * Created by <PERSON> on 02.03.2017.
  */
class ColumnBasedJsonDataProvider(
  rawKpiProviderSource: RawKpiProviderSource,
  reportDefinition: Seq[(String, Seq[Kpi])],
  columns: Seq[Column],
  contextData: Map[String, JsValue],
)(implicit tenantSession: AccountFarmLangSession, kpiCalculatorService: KpiCalculatorService, settingsHandler: SettingsHandler)
    extends IReportJsonDataProvider {

  import tenantSession.implicits._

  private val kpiCalculator = new kpiCalculatorService.KpiCalculator(
    rawKpiProviderSource,
    contextData.filter(_._2.isInstanceOf[JsBoolean]).map { case (key, JsBoolean(value)) => key -> value },
  )

  override def provide(): JsonNode = {
    def jsonKpiValue(index: Int, kpi: Kpi): JsValue = {
      val kpiUnitOpt = kpi.kpiUnit
      val orgWeightUnit = user.farmSettings().weight
      kpiCalculator.calculate(kpi, index).map(v =>
        JsNumber(BigDecimal(KpiConversions.convertToOrgUnit(kpiUnitOpt, orgWeightUnit)(v)).setScale(kpi.decimalPlaces, HALF_UP)),
      ).getOrElse(JsNull)
    }

    val global = JsObject(contextData) +
      ("calculationDate" -> Json.toJson(new Date())) +
      ("columns" -> JsArray(columns.map(c => {
        JsObject(
          Seq("id" -> Json.toJson(c.id), "name" -> Json.toJson(c.name)) ++ Seq(
            c.fromOpt.map("from" -> Json.toJson(_)),
            c.toOpt.map("to" -> Json.toJson(_)),
          ).flatten,
        )
      })))

    val data: JsArray = JsArray(
      reportDefinition.flatMap {
        case (sectionId, kpis) =>
          kpis.map { kpi: Kpi =>
            JsObject(
              Seq(
                REPORT_SECTION_ID -> Json.toJson(sectionId),
                KPI_CODE -> Json.toJson(kpi.code),
                LINE_ID -> Json.toJson(kpi.name),
                "decimalPlaces" -> Json.toJson(kpi.decimalPlaces),
              ) ++
                columns.map(c => c.id -> jsonKpiValue(c.index, kpi)),
            )
          }
      },
    )

    JsObject(Seq(
      "version" -> JsNumber(2),
      "global" -> global,
      "repeating" -> data,
    )).as[ObjectNode]
  }
}
