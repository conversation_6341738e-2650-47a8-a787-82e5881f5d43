package domains.boar

import reactapp.shared.boar.BoarApi.{ BoarUpdateR<PERSON>ult, PreciseBoarId }
import reactapp.shared.boar.{ BoarApi, BoarGridColumn }
import reactapp.shared.grid.Field
import sjsgrid.shared.common.ModelType
import sjsgrid.shared.grid.dto.RowModel

import java.time.Instant

/**
  * Created by <PERSON>
  * Date: 11/08/2020
  * Time: 15:28
  */
case class BoarModel(boarData: BoarUpdateResult) {

  val boarNumber: String =
    getValue(BoarGridColumn.BoarNumber).getOrElse("") // this should not happen as BoarGridColumn.BoarNumber is Field.Mandatory
  val boarDate: Instant = getValue(BoarGridColumn.BoarDate).getOrElse(Instant.MIN)

  def id: PreciseBoarId = boarData.id // TODO: is this rowId or ID from boar.id?
  def model: RowModel[BoarGridColumn] = boarData.row
  def isUsed = boarData.isUsed

  def isExternal: Boolean = model.getValue(BoarGridColumn.External)
    .getOrElse(false) // interpretation of this value is irrelevant, as the value should be present always

  def getValue[M](column: BoarGridColumn with ModelType[M] with Field.AlwaysFetch): Option[M] = model.getValue(column)

  def isBlank[M](column: BoarGridColumn with ModelType[M] with Field.AlwaysFetch): Boolean = !isPresent(column)
  def isPresent[M](column: BoarGridColumn with ModelType[M] with Field.AlwaysFetch): Boolean = {
    model.getValue(column) match {
      case Some(string: String) => string.nonEmpty
      case Some(_)              => true
      case None                 => false
    }
  }

  def isChanged[M](column: BoarGridColumn with ModelType[M] with Field.AlwaysFetch, oldValue: Option[M]): Boolean =
    model.getValue(column) != oldValue
  def isChanged[M](column: BoarGridColumn with ModelType[M] with Field.AlwaysFetch, otherBoarModel: BoarModel): Boolean =
    isChanged(column, otherBoarModel.model.getValue(column))

  def isDirty(column: BoarGridColumn with ModelType[String] with Field.AlwaysFetch, oldValue: Option[String]): Boolean =
    isPresent(column) && isChanged(column, oldValue)

  def isDirty(column: BoarGridColumn with ModelType[String] with Field.AlwaysFetch, otherBoarModel: BoarModel): Boolean =
    isDirty(column, otherBoarModel.model.getValue(column))

  case class LocationModel(id: Long) {
    def getFullReverseNumber(): String = "getFullReverseNumber"
  }

  def getId(): String = id.toString

  def getBoarNumber(): String = getValue(BoarGridColumn.BoarNumber).getOrElse("")
  def getAnimalId(): String = getValue(BoarGridColumn.AnimalId).getOrElse("")
  def getBreed(): String = getValue(BoarGridColumn.Breed).getOrElse("")
  def getDamId() = getValue(BoarGridColumn.DamId).getOrElse("")
  def getSireId() = getValue(BoarGridColumn.SireId).getOrElse("")
  def getIndex() = getValue(BoarGridColumn.Index)
  def getLitterSize() = getValue(BoarGridColumn.LitterSize)
  def getBoarDate() = getValue(BoarGridColumn.BoarDate)
  def getBirthDate() = getValue(BoarGridColumn.BirthDate)
  def getLocation() = LocationModel(getValue(BoarGridColumn.LocationId).getOrElse(-1))
  def getFirstSemenDate() = getValue(BoarGridColumn.FirstSemenDate)
  def getLastSemenDate() = getValue(BoarGridColumn.LastSemenDate)
  def getComment(): String = getValue(BoarGridColumn.Comment).getOrElse("")

}
