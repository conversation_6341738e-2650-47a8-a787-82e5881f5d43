package utils

import javax.inject.Inject
import javax.sql.DataSource
import org.postgresql.util.PSQLException
import play.api.db.Database
import play.api.{ Configuration, Environment }
import scalikejdbc._

object CreateDefaultFarmViews {

  val listOfStates: List[String] = List(
    "backfatboar",
    "backfatsow",
    "backfatsowreport",
    "BatchesGrid",
    "benchmarkfarm2farm",
    "boars",
    "boarsuccessreport",
    "breed",
    "breedinganimals",
    "breedingorderlist",
    "breedingstock",
    "BudgetingAndForecastingGrid",
    "business",
    "buying",
    "cost",
    "CostsPageGrid",
    "creategilts",
    "customreport",
    "danavloverview",
    "DanGenLogsGrid",
    "DanishCrownGrid",
    "DayReportGrid",
    "dayreportsetup",
    "dead",
    "deadtransfer",
    "DeviceTrackingGrid",
    "EfficiencyReportGrid",
    "emptylocation",
    "FarmUsersGrid",
    "farrow",
    "FeedAdditivesConsumptionGrid",
    "FeedbackGrid",
    "feed$consumption",
    "feed$stations",
    "FeedSystemGrid",
    "feed$systems",
    "fostering",
    "FSCommunicationHistoryGrid",
    "growthrate",
    "HoldingBudgetGrid",
    "hop",
    "IncomingFeedGrid",
    "issue",
    "KpiCustomizationGrid",
    "kpigoal",
    "localtransfer",
    "LocationDanishCrownGrid",
    "LocationRWAGrid",
    "LocationTicanGrid",
    "MatingPlanGrid",
    "medicine",
    "medicineusage",
    "MultipleProcessingGrid",
    "MultiprescriptionGrid",
    "ownuse",
    "PigletsTransferGrid",
    "PigTestingTestteamsGrid",
    "planninggilt",
    "predictedlosses",
    "pregnancyscanning",
    "prescription",
    "PrescriptionGrid",
    "problematicaction",
    "selling",
    "SellingScreenGrid",
    "semenentering",
    "SemenGrid",
    "serversuccessreport",
    "serving",
    "servinglist",
    "SikavaProblemsGrid",
    "SiloEmptyingGrid",
    "SiloGrid",
    "sowlist",
    "SowListGrid",
    "spare-part-log",
    "spare-part-storage",
    "stocktaking",
    "Stocktaking2PageGrid",
    "TagPoolGrid",
    "testteam",
    "TicanGrid",
    "TraceabilityGrid",
    "TransferReportPageGrid-Farm",
    "TransferReportPageGrid-Location",
    "TransferReportPageGrid-Organization",
    "unit",
    "weaning",
    "weightgroups",
    "WeightGroupsGrid",
  )

  case class FarmAndRoot(farmId: Long, rootId: Long)
  def getHierarchyIds(rootId: Long)(implicit dbSession: DBSession): List[Long] = {
    sql"""
       select h.id from holding r
         join holding h on h.prefix like r.prefix || '%'
         where r.id = $rootId order by h.prefix asc
     """.map(r => r.long(1)).list.apply()
  }

  def getAllFarmIds()(implicit dbSession: DBSession) =
    sql"""select distinct id, root_id from public.organization""".map(r => FarmAndRoot(r.long("id"), r.long("root_id"))).list.apply()

  def getHoldingViewDefaults(holdingId: Long, state: String)(implicit dbSession: DBSession): Option[String] = {
    val rootSchemaName = "root_" + holdingId
    val schemaExists = sql"""SELECT schema_name FROM information_schema.schemata WHERE schema_name = $rootSchemaName""".map(r =>
      (r.string(1)),
    ).single.apply()
    if (schemaExists.nonEmpty) {
      sql"""select view_name from ${SQLSyntax.createUnsafely("root_" + holdingId)}.default_holding_view where state=$state""".map(r =>
        (r.string("view_name")),
      ).single.apply()
    } else None
  }

  def getFarmViewDefaults(farmId: Long, state: String)(implicit dbSession: DBSession): Option[String] = {
    sql"""select view_name from ${SQLSyntax.createUnsafely("farm_" + farmId)}.default_farm_view where state=$state""".map(r =>
      (r.string("view_name")),
    ).single.apply()
  }

  def setDefaultViewForFarm(farmId: Long, viewName: String, state: String)(implicit dbSession: DBSession) = {
    sql"""insert into ${SQLSyntax.createUnsafely(
      "farm_" + farmId,
    )}.default_farm_view (state, view_name) VALUES ($state, $viewName);""".execute.apply()
  }

  def dangerouslyCreateAllDefaultViewsForFarms()(implicit DBSession: DBSession) =
    listOfStates.foreach(stateName => dangerouslyCreateDefaultViewsForFarms(stateName))

  def dangerouslyCreateDefaultViewsForFarms(state: String)(implicit dbSession: DBSession) = {
    getAllFarmIds().foreach(farmAndRootId => {
      if (farmAndRootId.farmId > 0) {
        val holdingIds = getHierarchyIds(farmAndRootId.rootId)
        // println("farm " + farmAndRootId.farmId + ", roots " + holdingIds)
        val holdingViews = holdingIds.flatMap(holdingId => getHoldingViewDefaults(holdingId, state))
        if (holdingViews.isEmpty && getFarmViewDefaults(farmAndRootId.farmId, state).isEmpty) {
          setDefaultViewForFarm(farmAndRootId.farmId, "V0", state)
        }
      }
    })
  }
}
