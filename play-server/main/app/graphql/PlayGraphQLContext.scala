package graphql

import play.api.db.DBApi
import play.api.i18n.Lang
import scalikejdbc.interpolation.SQLSyntax
import scalikejdbc.{ DB, DBSession, _ }
import security.AppRole
import tenant.{ AutowireContext, AutowireContextBuilder }

/**
  * Created by Milan Satala
  * Date: 11. 6. 2020
  * Time: 16:24
  */
trait PlayGraphQLContext {
  def tenantBuilder: AutowireContextBuilder

  def dBApi: DBApi

  def accountId: Long

  def getAppRoles(implicit session: DBSession): Set[AppRole] = {
    sql"""SELECT role_id FROM account_approle r
                     WHERE r.account_id = $accountId""".map(rs =>
      AppRole.values()(rs.int("role_id")),
    ).list.apply().toSet
  }

  def noFarmSession[A](checkAppRoles: Set[AppRole] => Boolean)(block: DBSession => A): A = {

    dBApi.database("default").withConnection { conn =>
      DB(conn).localTx {
        implicit session =>
          val appRoles = getAppRoles
          if (!checkAppRoles(appRoles)) sys.error(s"Your app roles ${appRoles.mkString(", ")} are not enough for this action.")
          else {
            sql"""set local cloudfarms.account to ${SQLSyntax.createUnsafely(accountId.toString)}""".execute.apply()
            sql"""set local cloudfarms.ui to 'W'""".execute.apply()
            block(session)
          }
      }
    }
  }
}
