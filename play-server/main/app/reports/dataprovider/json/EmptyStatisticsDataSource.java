package reports.dataprovider.json;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Map;

/**
 * Created by <PERSON> on 3.6.2014.
 */
public class EmptyStatisticsDataSource implements IStatisticsDataSource {
    @Override
    public Map<String, Integer> getSaldoAmountAtBeginning() {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Integer> getSaldoAmountAtEnd() {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Integer> getBoughtAnimals() {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Integer> getTransferredInAnimals() {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Integer> getSlaughteredAnimals() {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Integer> getLiveSellAnimals() {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Integer> getDeadAnimals() {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Integer> getTransferredOutAnimals() {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Integer> getFeedingDays() {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, BigDecimal> getWeightGain() {
        return Collections.emptyMap();
    }

    @Override
    public Integer getLiveBorns() {
        return null;
    }

    @Override
    public Integer getFirstFarrowed() {
        return null;
    }

    @Override
    public Integer getFarrowings() {
        return null;
    }

    @Override
    public Integer getGestatingSows() {
        return null;
    }

    @Override
    public Integer getLactatingSows() {
        return null;
    }

    @Override
    public Integer getEmptySows() {
        return null;
    }

    @Override
    public Integer getDiscardedBoarsAndSows() {
        return null;
    }

    @Override
    public Integer getPigletsAndWeanersAmount() {
        return null;
    }

    @Override
    public Integer getFattenersFrom20To49() {
        return null;
    }

    @Override
    public Integer getFattenersFrom50To79() {
        return null;
    }

    @Override
    public Integer getFattenersFrom80To109() {
        return null;
    }

    @Override
    public Integer getFattenersFrom110() {
        return null;
    }

    @Override
    public Integer getBreedingPigsFrom20To49() {
        return null;
    }

    @Override
    public Integer getBreedingPigsFrom50() {
        return null;
    }
}
