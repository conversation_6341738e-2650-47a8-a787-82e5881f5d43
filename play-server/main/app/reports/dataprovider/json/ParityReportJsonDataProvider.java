package reports.dataprovider.json;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableList;
import domains.kpi.KpiDefinitionService;
import models.handlers.SettingsHandler;
import org.apache.commons.lang3.tuple.Pair;
import play.i18n.Messages;
import reactapp.shared.kpi.Kpi;
import reactapp.shared.kpi.KpiConversions;
import reports.KpiDefinitionProvider;
import reports.ReportConstants;
import reports.dataprovider.CommonDataHelper;
import reports.kpi.calculators.KpisCalculator;
import reports.kpi.calculators.data.KpisDataSourceService;
import security.*;
import tenant.*;
import utils.*;

import jakarta.persistence.EntityManager;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Connection;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static reports.ReportConstants.BREED;
import static reports.ReportConstants.DATE_FROM;
import static reports.ReportConstants.DATE_TO;
import static reports.ReportConstants.FARM_NAME;

/**
 * Created by Gabriel on 1/8/14.
 */
public class ParityReportJsonDataProvider implements IReportJsonDataProvider {
    private final KpisCalculator kpisCalculator;
    private final Map<String, List<Kpi>> kpis;
    private final int daysInPeriod;
    private final ObjectMapper om = Utils.mapper();
    private final ImmutableList<Integer> parities;
    private final ObjectNode globalData = om.createObjectNode();
    private final SettingsHandler settingsHandler;
    private final ApplicationUser user;
    private final Connection conn;
    private final Messages messages;

    public ParityReportJsonDataProvider(
            LocalDate from,
            LocalDate to,
            Set<Integer> parities,
            Set<Long> parentLocationIds,
            Set<String> breed,
            SettingsHandler settingsHandler,
            CalendarUtilsJTime calendarUtilsJTime,

            KpiDefinitionService kpiDefinitionService,
            KpisDataSourceService kpisDataSourceService,
            AccountFarmLangSession tt,
            Messages messages
    ) {
        this.settingsHandler = settingsHandler;
        this.user = tt.user();
        this.conn = tt.connection();
        this.messages = messages;

        this.daysInPeriod = (int) ChronoUnit.DAYS.between(from, to) + 1;
        this.parities = ImmutableList.copyOf(new TreeSet<>(parities));
        this.kpisCalculator = new KpisCalculator(
            kpiDefinitionService.getKpiDefinitionsJavaMap(tt.dbSession(), tt.user(), tt.lang()),
            kpisDataSourceService.getKpisDataSourceForParities(user, conn, this.parities, parentLocationIds, from, to, breed),
            settingsHandler,
            user,
            conn
        );
        kpis = KpiDefinitionProvider.provide(messages, KpiDefinitionProvider.REPORT_PARITY, kpiDefinitionService, tt);
        initializeGlobalData(from, to, parentLocationIds, breed);
    }

    private void initializeGlobalData(LocalDate from, LocalDate to, Set<Long> parentLocationIds, Set<String> breed) {
        ZoneId tz = ZoneId.of(user.getFarmTimezoneName());

        globalData.set(ReportConstants.CALCULATION_DATE, om.<JsonNode>valueToTree(new Date()));
        globalData.set(DATE_FROM, om.<JsonNode>valueToTree(Date.from(from.atStartOfDay(tz).toInstant())));
        globalData.set(DATE_TO, om.<JsonNode>valueToTree(Date.from(to.atStartOfDay(tz).toInstant())));
        if (parentLocationIds.isEmpty()) {
            globalData.put(FARM_NAME, user.getFarmName());
        } else {
            globalData.put(FARM_NAME, CommonDataHelper.getLocationNames(parentLocationIds, user.getFarmName(), JooqUtilsDI.getConnectionDSLContext(conn)));
        }
        if (!breed.isEmpty()) {
            globalData.put(BREED, String.join(", ", breed));
        }
    }

    @Override
    public JsonNode provide() {
        List<Pair<Kpi, ObjectNode>> reportNodes = new ArrayList<>();
        Set<Kpi> nonZeroKpis = new HashSet<>();
        String energyUnit = messages.at("js.label.energy.unit." + settingsHandler.getFarmEnergyUnit(user));
        String weightUnit = messages.at("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user));
        String currency = settingsHandler.getFarmCurrency(user);

        for (Map.Entry<String, List<Kpi>> sectionWithKpis : kpis.entrySet()) {
            String sectionId = sectionWithKpis.getKey();
            for (Kpi kpi : sectionWithKpis.getValue()) {
                ObjectNode kpiNode = om.createObjectNode();

                kpiNode.put(ReportConstants.REPORT_SECTION_ID, sectionId);
                kpiNode.put(ReportConstants.LINE_ID, MessageFormat.format(kpi.name(), energyUnit, weightUnit, currency));
                kpiNode.put(ReportConstants.KPI_CODE, kpi.code());
                reportNodes.add(Pair.of(kpi, kpiNode));
            }
        }

        parities.forEach(p -> addKpiValuesForParity(reportNodes, nonZeroKpis, globalData, OptionalInt.of(p)));
        addKpiValuesForParity(reportNodes, nonZeroKpis, globalData, OptionalInt.empty());

        ArrayNode repeatingData = om.createArrayNode();
        ObjectNode result = om.createObjectNode();

        for (Pair<Kpi, ObjectNode> reportNode : reportNodes) {
            Kpi kpi = reportNode.getLeft();
            if (!kpi.hideWhenZero() || nonZeroKpis.contains(kpi)) {
                repeatingData.add(reportNode.getRight());
            }
        }
        result.put("version", 2);
        result.set("global", globalData);
        result.set("repeating", repeatingData);

        return result;
    }

    private void addKpiValuesForParity(List<Pair<Kpi, ObjectNode>> reportNodes, Set<Kpi> nonZeroKpis, ObjectNode globalData, OptionalInt parity) {
        int categoryId = parity.isPresent() ? parities.indexOf(parity.getAsInt()) + 1 : 0;
        String columnId = parity.isPresent() ? "parity" + parity.getAsInt() : "total";

        for (Pair<Kpi, ObjectNode> kpiNodeEntry : reportNodes) {
            Kpi kpi = kpiNodeEntry.getKey();
            ObjectNode kpiNode = kpiNodeEntry.getValue();
            Optional<Double> kpiValue = kpisCalculator.calculate(kpi, categoryId).map( value ->
                KpiConversions.convertToOrgUnit(kpi.kpiUnit(), user.farmSettings().weight(), value)
            );

            if (!kpiValue.isPresent()) {
                kpiNode.put(columnId, (Double) null);
            } else {
                Double v = null;
                if (kpi.periodDependent() && kpi.days() != null) {

                    v = BigDecimal.valueOf(kpi.days() * kpiValue.get() / daysInPeriod).setScale(kpi.decimalPlaces(), RoundingMode.HALF_UP).doubleValue();
                } else {
                    v = BigDecimal.valueOf(kpiValue.get()).setScale(kpi.decimalPlaces(), RoundingMode.HALF_UP).doubleValue();
                }
                if (v > 0.0000001d) {
                    nonZeroKpis.add(kpi);
                }
                kpiNode.put(columnId, v);
            }
            kpiNode.put("decimalPlaces", kpi.decimalPlaces());
        }
    }
}
