package reports.dataprovider.json;

import org.eclipse.persistence.config.QueryHints;
import org.eclipse.persistence.config.ResultType;
import org.jooq.impl.*;
import reports.dataprovider.CommonDataHelper;
import utils.*;

import jakarta.persistence.*;
import java.sql.Connection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Data provider for week reports.
 * This class is deprecated there are fixes in query in {@link ExpectedFarrowingsDataSourceSc}
 */
@Deprecated
public class ExpectedFarrowingsDataSource {
    /**
     * Query updated based on expanded fertility report
     */
    private static String EXPECTED_FARROWINGS_QUERY =
            "with recursive weeks (weeknum, thedate) as (\n" +
                "    select public.extract_week(#thedate), #thedate::date\n" +
                "    union\n" +
                "    select extract_week(thedate - 1), thedate - 1\n" +
                "    from weeks \n" +
                "    where thedate > #thedate::date - 6 - 16*7\n" +
                "), " +
                "failed_scans AS (\n" +
                "       WITH scans AS (\n" +
                "           SELECT\n" +
                "               sowscan.serving_id,\n" +
                "               sowscan.actor_date,\n" +
                "               LAG(sowscan.pregnant) OVER w AS prev_pregnant,\n" +
                "               sowscan.pregnant AS this_pregnant,\n" +
                "               LAST_VALUE(sowscan.pregnant) OVER w AS last_pregnant,\n" +
                "               sowscan.illnesstype_code,\n" +
                "               COALESCE(illnesstype.abortion, false) as abortion\n" +
                "           FROM serving\n" +
                "                    INNER JOIN sowscan ON sowscan.serving_id = serving.id\n" +
                "                    LEFT JOIN illnesstype ON sowscan.illnesstype_code = illnesstype.code\n" +
                "                    inner join organization o on o.id = get_tenant_organization()\n" +
                "           WHERE serving.actor_date < (#thedate :: date + 1) :: timestamp at time zone o.timezonename and\n" +
                "                 serving.actor_date >= (#thedate :: date - 6 - 16*7) :: timestamp at time zone o.timezonename\n" +
                "               WINDOW w AS ( PARTITION BY sowscan.serving_id ORDER BY sowscan.actor_date ROWS BETWEEN 1 PRECEDING AND UNBOUNDED FOLLOWING )\n" +
                "       )\n" +
                "       SELECT DISTINCT ON(serving_id) *\n" +
                "       FROM scans\n" +
                "       WHERE last_pregnant = false AND this_pregnant = false AND (prev_pregnant = true or prev_pregnant IS NULL)\n" +
                "       ORDER BY serving_id, actor_date DESC\n" +
                "   )," +
                "serving_states as (\n" +
                "    select\n" +
                "      distinct on (s.id)\n" +
                "      w.weeknum,\n" +
                "      s.actor_date serving_date,\n" +
                "      s.id serving_id,\n" +
                "      (not #abortionNoLiveborn or s.liveborn <> 0) and s.farrow_endtime is not null isfarrowed,\n" +
                "      not coalesce(x.this_pregnant, true) and not x.abortion and (LEAST(r.actor_date, d.actor_date, t.actor_date, x.actor_date) = x.actor_date) isempty,\n" +
                "      (#abortionNoLiveborn and s.liveborn = 0) or (not coalesce(x.this_pregnant, true) and x.abortion and (LEAST(r.actor_date, d.actor_date, t.actor_date, x.actor_date) = x.actor_date)) aborted,\n" +
                "      d.id is not null and d.deathtype_code is not null and (LEAST(r.actor_date, d.actor_date, t.actor_date, x.actor_date) = d.actor_date) as isdead,\n" +
                "      t.id is not null and (LEAST(r.actor_date, d.actor_date, t.actor_date, x.actor_date) = t.actor_date) as issold,\n" +
                "      t.id is not null and (LEAST(r.actor_date, d.actor_date, t.actor_date, x.actor_date) = t.actor_date) and t.livesell = true as isalivesold,\n" +
                "      r.id is not null and (LEAST(r.actor_date, d.actor_date, t.actor_date, x.actor_date) = r.actor_date) as isrepeatserved\n" +
                "    from\n" +
                "      serving s\n" +
                "      join organization o on o.id = get_tenant_organization()\n" +
                "      join weeks w on (s.actor_date at time zone o.timezonename) :: date = w.thedate\n" +
                "      join sow on s.sow_id = sow.id and (#breeds::text[] is null or sow.breed = any(#breeds::text[]))\n" +
                "      left join dead d on s.farrow_endtime is null and d.sow_id = s.sow_id and d.serving_id is null \n" +
                "      left join transferindividualout ti on s.farrow_endtime is null and ti.sow_id = s.sow_id\n" +
                "      left join transferout t on t.id = ti.transferout_id\n" +
                "      left join failed_scans x on s.farrow_endtime is null and x.serving_id = s.id \n" +
                "      left join serving r on s.farrow_endtime is null and r.sow_id = s.sow_id and r.litter = s.litter and r.actor_date > s.actor_date\n" +
                "    where\n" +
                "      (#locations::bigint[] is null or s.location_id = any(#locations::bigint[])) and\n" +
                "      s.actor_date < (#thedate :: date + 1) :: timestamp at time zone o.timezonename and\n" +
                "      s.actor_date >= (#thedate :: date - 6 - 16*7) :: timestamp at time zone o.timezonename\n" +
                "    order by\n" +
                "      s.id, x.actor_date desc\n" +
                "), servings as (\n" +
                "    select \n" +
                "      weeknum,\n" +
                "      max(serving_date) serving_date,\n" +
                "      count(serving_id) v\n" +
                "    from serving_states s\n" +
                "    group by weeknum\n" +
                "), falsescan as (\n" +
                "    select \n" +
                "      weeknum,\n" +
                "      count(serving_id) v\n" +
                "    from serving_states\n" +
                "    where isempty\n" +
                "    group by weeknum\n" +
                "), abortions as (\n" +
                "    select \n" +
                "      weeknum,\n" +
                "      count(serving_id) v\n" +
                "    from serving_states\n" +
                "    where aborted\n" +
                "    group by weeknum\n" +
                "), repeatservice as (\n" +
                "    select\n" +
                "      weeknum,\n" +
                "      count(serving_id) v\n" +
                "    from serving_states\n" +
                "    where isrepeatserved\n" +
                "    group by weeknum\n" +
                "), deadsows as (\n" +
                "    select\n" +
                "      weeknum,\n" +
                "      count(serving_id) v\n" +
                "    from serving_states\n" +
                "    where not isempty and isdead\n" +
                "    group by weeknum\n" +
                "), soldsows as (\n" +
                "    select\n" +
                "      weeknum,\n" +
                "      count(serving_id) v\n" +
                "    from serving_states\n" +
                "    where not isempty and issold and not isalivesold\n" +
                "    group by weeknum\n" +
                "), alivesoldsows as (\n" +
                "    select\n" +
                "      weeknum,\n" +
                "      count(serving_id) v\n" +
                "    from serving_states\n" +
                "    where not aborted and isalivesold\n" +
                "    group by weeknum\n" +
                ")\n" +
                "select\n" +
                "  s.weeknum::integer weeknum,\n" +
                "  s.v::integer service,\n" +
                "  coalesce(x.v, 0)::integer falsescan,\n" +
                "  coalesce(a.v, 0)::integer abortions,\n" +
                "  coalesce(r.v, 0)::integer repeatservice,\n" +
                "  coalesce(ss.v, 0)::integer sold,\n" +
                "  coalesce(d.v, 0)::integer died,\n" +
                "  coalesce(ass.v, 0)::integer alivesold,\n" +
                "  CASE " +
                "    WHEN #excludeSoldPregnantSows = 'true' THEN " +
                "       (s.v - coalesce(x.v, 0) - coalesce(a.v, 0) - coalesce(r.v, 0) - coalesce(d.v, 0) - coalesce(ss.v, 0))::integer\n" +
                "    ELSE" +
                "       (s.v - coalesce(x.v, 0) - coalesce(a.v, 0) - coalesce(r.v, 0) - coalesce(d.v, 0) - coalesce(ss.v, 0) - coalesce(ass.v, 0))::integer\n" +
                "  END expfarrowings," +
                "  CASE " +
                "    WHEN #exludeSoldPregnantSows = 'true' THEN" +
                "       (s.v - coalesce(x.v, 0) - coalesce(a.v, 0) - coalesce(r.v, 0) - coalesce(d.v, 0) - coalesce(ss.v, 0) - coalesce(ass.v, 0))/(s.v - coalesce(ass.v, 0))::numeric\n" +
                "    ELSE " +
                "       (s.v - coalesce(x.v, 0) - coalesce(a.v, 0) - coalesce(r.v, 0) - coalesce(d.v, 0) - coalesce(ss.v, 0))/(s.v)::numeric\n" +
                "   END rate\n" +
                "from \n" +
                "  servings s \n" +
                "  left join falsescan x on s.weeknum = x.weeknum\n" +
                "  left join abortions a on s.weeknum = a.weeknum\n" +
                "  left join repeatservice r on s.weeknum = r.weeknum\n" +
                "  left join deadsows d on s.weeknum = d.weeknum\n" +
                "  left join soldsows ss on s.weeknum = ss.weeknum\n" +
                "  left join alivesoldsows ass on s.weeknum = ass.weeknum\n" +
                "order by s.serving_date desc, 2 desc";

    public static List<Map<String, Object>> getExpectedFarrowings(EntityManager em, Date theDate, List<String> breed, Set<Long> locations, Boolean excludeSoldPregnantSows, Boolean abortionNoLiveborn) {
        final Connection conn = em.unwrap(Connection.class);
        Query q = em.createNativeQuery(EXPECTED_FARROWINGS_QUERY)
            .setParameter("thedate", theDate)
            .setParameter("breeds", breed.isEmpty() ? null : DbUtils.getTextArray(conn, breed))
            .setParameter("locations", locations.isEmpty() ? null : locations)
            .setParameter("excludeSoldPregnantSows", excludeSoldPregnantSows)
            .setParameter("abortionNoLiveborn", abortionNoLiveborn);

        q.setHint(QueryHints.RESULT_TYPE, ResultType.Map);
        return q.getResultList();
    }
}
