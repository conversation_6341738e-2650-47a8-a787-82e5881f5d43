package reports.dataprovider.json;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import domains.kpi.KpiDefinitionService;
import models.handlers.SettingsHandler;
import org.apache.commons.lang3.tuple.Pair;
import play.i18n.Messages;
import play.libs.Json;
import reactapp.shared.auth.FarmTypeS;
import reactapp.shared.kpi.Kpi;
import reactapp.shared.kpi.KpiConversions;
import reports.ReportConstants;
import reports.kpi.calculators.KpisCalculator;
import reports.kpi.calculators.data.KpisDataSourceService;
import security.*;
import tenant.*;
import utils.Utils;
import utils.jr.JsonJRDataSource;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static models.InhabitantType.BOAR;
import static models.InhabitantType.FATTENER;
import static models.InhabitantType.GILT;
import static models.InhabitantType.PAR0;
import static models.InhabitantType.PIGLET;
import static models.InhabitantType.SOW;
import static models.InhabitantType.WEANER;

/**
 * Created by Gabriel on 26.10.2015.
 */
public class AccountingReportJsonDataProvider implements IReportJsonDataProvider {
    private static final List<String> kpiNameSuffixes = ImmutableList.of(
            "START_AMOUNT",
            "START_TOTAL_WEIGHT",
            "FINAL_AMOUNT",
            "FINAL_TOTAL_WEIGHT",
            "AVERAGE_AMOUNT",
            "LIVE_BORN_AMOUNT",
            "LIVE_BORN_TOTAL_WEIGHT",
            "LOCAL_INCREASE_AMOUNT",
            "LOCAL_INCREASE_TOTAL_WEIGHT",
            "LOCAL_DECREASE_AMOUNT",
            "LOCAL_DECREASE_TOTAL_WEIGHT",
            "TRANSFERRED_TO_SOWS_AMOUNT",
            "TRANSFERRED_TO_SOWS_TOTAL_WEIGHT",
            "BOUGHT_AMOUNT",
            "BOUGHT_TOTAL_WEIGHT",
            "SOLD_AMOUNT",
            "SOLD_TOTAL_WEIGHT",
            "SOLD_LIVE_AMOUNT",
            "SOLD_LIVE_TOTAL_WEIGHT",
            "SOLD_SLAUGHTERED_AMOUNT",
            "SOLD_SLAUGHTERED_TOTAL_WEIGHT",
            "CONDEMNED_AMOUNT",
            "CONDEMNED_TOTAL_WEIGHT",
            "DEAD_AMOUNT",
            "DEAD_TOTAL_WEIGHT",
            "STOCKTAKING_DIFF_AMOUNT",
            "FEEDING_DAYS",
            "TOTAL_WEIGHT_GAIN"
    );

    private static final Map<String, String> aliases = ImmutableMap.of(
        "PIGL_LIVE_BORN_AMOUNT", "LIVE_BORN_AMOUNT",
        "PIGL_LIVE_BORN_TOTAL_WEIGHT", "LIVE_BORN_TOTAL_WEIGHT"
    );

    private static final String FINAL_STATUS = "_FINAL_AMOUNT";

    private final LocalDate from;
    private final LocalDate to;
    private final Map<String, Kpi> kpiDefinitions;
    private final KpisCalculator kpisCalculator;
    private final SettingsHandler settingsHandler;
    private final ApplicationUser user;
    private final Messages messages;

    public AccountingReportJsonDataProvider(
            LocalDate fromDate,
            LocalDate toDate,
            Collection<Long> locationId,
            SettingsHandler settingsHandler,
            AccountFarmLangSession tt,
            KpiDefinitionService kpiDefinitionService,
            KpisDataSourceService kpisDataSourceService,
            Messages messages
    ) {
        this.messages = messages;
        this.user = tt.user();
        from = fromDate;
        to = toDate;
        Set<Long> locations = ImmutableSet.copyOf(locationId);
        this.settingsHandler = settingsHandler;
        kpiDefinitions = kpiDefinitionService.getKpiDefinitionsJavaMap(tt.dbSession(), tt.user(), tt.lang());
        if (locations.isEmpty()) {
            kpisCalculator = new KpisCalculator(
                kpiDefinitions,
                kpisDataSourceService.getKpisDataSourceForOrganization(user, tt.connection(), user.getFarmId(), ImmutableList.of(Pair.of(from, to)), ImmutableMap.of()),
                settingsHandler,
                user,
                tt.connection()
            );
        } else {
            kpisCalculator = new KpisCalculator(
                kpiDefinitions, kpisDataSourceService.getKpisDataSourceForLocationHierarchy(user, tt.connection(), locations, ImmutableList.of(Pair.of(from, to)), ImmutableMap.of()),
                settingsHandler,
                user,
                tt.connection()
            );
        }
    }

    private List<String> allowedInhabitants(ApplicationUser user) {
        FarmTypeS farmType = FarmTypeS.withCode((Integer) user.farmSettings().farmType());
        Set<String> allowedInhabitantTypeCodes = scala.jdk.javaapi.CollectionConverters.asJava(farmType.allowedInhabitantTypesCodes());

        ImmutableList.Builder<String> listBuilder = ImmutableList.builder();

        if (allowedInhabitantTypeCodes.contains(BOAR)) listBuilder.add(BOAR);
        if (allowedInhabitantTypeCodes.contains(GILT)) listBuilder.add(GILT);
        if (allowedInhabitantTypeCodes.contains(PAR0)) listBuilder.add(PAR0);
        if (allowedInhabitantTypeCodes.contains(SOW))  listBuilder.add(SOW);
        if (allowedInhabitantTypeCodes.contains(PAR0) || allowedInhabitantTypeCodes.contains(SOW)) listBuilder.add("PAR0SOW");
        if (allowedInhabitantTypeCodes.contains(PIGLET)) listBuilder.add(PIGLET);
        if (allowedInhabitantTypeCodes.contains(WEANER)) listBuilder.add(WEANER);
        if (allowedInhabitantTypeCodes.contains(FATTENER)) listBuilder.add(FATTENER);
        listBuilder.add("ALL");

        return listBuilder.build();
    }

    @Override
    public JsonNode provide() {

        ObjectMapper om = Utils.mapper();
        ObjectNode result = Json.newObject();
        ArrayNode repeating = om.createArrayNode();
        ObjectNode global = Json.newObject();
        String energyUnit = messages.at("js.label.energy.unit." + settingsHandler.getFarmEnergyUnit(user));
        String weightUnit = settingsHandler.getFarmWeightUnit(user);
        String weightUnitMsg = messages.at("js.label.weight.unit." + weightUnit);
        String currency = settingsHandler.getFarmCurrency(user);


        for (String kpiNameSuffix : kpiNameSuffixes) {
            ObjectNode on = Json.newObject();
            String kpiNameId = "js.report.accounting.".concat(kpiNameSuffix);

            if (!messages.isDefinedAt(kpiNameId)) {
                kpiNameId = "js.kpi.".concat(kpiNameSuffix);
            }
            on.put(ReportConstants.LINE_ID, messages.isDefinedAt(kpiNameId) ? messages.at(kpiNameId, energyUnit, weightUnitMsg, currency) : kpiNameSuffix);
            for (String it : allowedInhabitants(user)) {
                String kpiName = it.concat("_").concat(kpiNameSuffix);
                final Double kpiValue;

                kpiName = aliases.getOrDefault(kpiName, kpiName);
                if (kpiDefinitions.containsKey(kpiName)) {
                    Kpi kpi = kpiDefinitions.get(kpiName);
                    kpiValue = kpisCalculator.calculate(kpi, 1).map( value ->
                            KpiConversions.convertToOrgUnit(kpi.kpiUnit(), weightUnit, value)
                    ).orElse(Double.NaN);
                    // it is OK to overwrite, as the formatting should be the same for all KPIs - regardless of the inhabitant type
                    on.put("decimalPlaces", kpi.decimalPlaces());
                } else {
                    kpiValue = Double.NaN;
                }
                if (!Double.isNaN(kpiValue)) {
                    on.put(it, kpiValue);
                }
            }
            repeating.add(on);
        }
        ZoneId tz = ZoneId.of(user.getFarmTimezoneName());
        global.set(ReportConstants.CALCULATION_DATE, om.<JsonNode>valueToTree(new Date()));
        global.set(ReportConstants.DATE_FROM, om.<JsonNode>valueToTree(Date.from(from.atStartOfDay(tz).toInstant())));
        global.set(ReportConstants.DATE_TO, om.<JsonNode>valueToTree(Date.from(to.atTime(LocalTime.MAX).atZone(tz).toInstant())));
        global.set(ReportConstants.CALCULATION_DATE, om.<JsonNode>valueToTree(new Date()));
        result.set(ReportConstants.REPEATING, repeating);
        result.set(ReportConstants.GLOBAL, global);

        return result;
    }

    @Override
    public Map<String, Object> getParameters() {
        return ImmutableMap.of("finalStatusDataSource", new JsonJRDataSource(getFinalStatusData()));
    }

    private JsonNode getFinalStatusData() {
        ObjectMapper om = Utils.mapper();
        ObjectNode result = Json.newObject();
        ArrayNode repeating = om.createArrayNode();
        ObjectNode global = Json.newObject();


        for (String itc : allowedInhabitants(user)) {
            ObjectNode on = Json.newObject();
            String kpiNameId = "js.label.inhab.plural.".concat(itc.toLowerCase());
            String kpiName = itc.concat(FINAL_STATUS);
            final Double kpiValue;
            if (kpiDefinitions.containsKey(kpiName)) {
                Kpi kpi = kpiDefinitions.get(kpiName);
                kpiValue = kpisCalculator.calculate(kpi, 1).map( value ->
                    KpiConversions.convertToOrgUnit(kpi.kpiUnit(), settingsHandler.getFarmWeightUnit(user), value)
                ).orElse(Double.NaN);
                // it is OK to overwrite, as the formatting should be the same for all KPIs - regardless of the inhabitant type
                if (!Double.isNaN(kpiValue)) {
                    on.put("amount", kpiValue);
                    on.put(ReportConstants.LINE_ID, messages.isDefinedAt(kpiNameId) ? messages.at(kpiNameId) : itc);
                    on.put("decimalPlaces", kpi.decimalPlaces());
                }
                repeating.add(on);
            }
        }
        result.set(ReportConstants.REPEATING, repeating);
        result.set(ReportConstants.GLOBAL, global);

        return result;
    }
}
