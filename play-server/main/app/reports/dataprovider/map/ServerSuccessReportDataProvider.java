package reports.dataprovider.map;

import play.i18n.Lang;
import reports.utils.JReportConstants;
import org.eclipse.persistence.config.QueryHints;
import org.eclipse.persistence.config.ResultType;
import reports.ReportConstants;

import jakarta.persistence.*;
import java.time.LocalDate;
import java.util.*;

/**
 * Created with IntelliJ IDEA.
 * User: Gabriel
 * Date: 12/4/13
 * Time: 7:51 AM
 * To change this template use File | Settings | File Templates.
 */
public class ServerSuccessReportDataProvider implements IReportDataProvider {
    public static final String SERVER_SUCCESS_REPORT_QUERY_NAME = "serversuccessreport";

    private final LocalDate from;
    private final LocalDate to;
    private final EntityManager em;

    public ServerSuccessReportDataProvider(EntityManager em, LocalDate from, LocalDate to) {
        this.em = em;
        this.from = from;
        this.to = to;
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Map<String, Object>> getRepeatingData(Lang lang) {
        Query q = em.createNamedQuery(SERVER_SUCCESS_REPORT_QUERY_NAME);
        q.setHint(QueryHints.RESULT_TYPE, ResultType.Map);

        q.setParameter(ReportConstants.DATE_FROM, java.sql.Date.valueOf(from)).setParameter(ReportConstants.DATE_TO, java.sql.Date.valueOf(to));
        return q.getResultList();
    }

    @Override
    public Map<String, Object> getStaticData(Lang lang) {
        Map<String, Object> result = new HashMap<>();

        result.put(JReportConstants.REPORT_FROM_DATE, from);
        result.put(JReportConstants.REPORT_TO_DATE, to);
        return result;
    }
}
