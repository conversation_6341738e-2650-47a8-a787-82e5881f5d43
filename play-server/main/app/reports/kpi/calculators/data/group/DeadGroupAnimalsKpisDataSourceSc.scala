package reports.kpi.calculators.data.group

import domainz.common.AnimalType
import reactapp.shared.kpi.{ KpiDescription, KpiDynamicSource }
import reports.kpi.calculators.KpiQueryHelper
import reports.kpi.calculators.data.BasicFields._
import reports.kpi.calculators.data._
import scalikejdbc._
import security.UserAndFarm

class DeadGroupAnimalsKpisDataSourceSc(params: KpiDataSourceParams)
    extends AbstractScalaWithDynamicValueKpiDataSource {
  implicit override val dbSession: DBSession = params.dbSession

  implicit override val userAndFarm: UserAndFarm = params.userAndFarm
  override def currentTimeZoneName: OrgTz = params.currentTimeZoneName
  override def currentFarmId: OrgId = params.currentFarmId

  val settingsHandler = params.settingsHandlerOpt.getOrElse(throw new IllegalArgumentException("SettingsHandler is mandatory parameter"))

  override val intervals: Seq[KpiInterval] = params.intervals
  override val organizationsWithTimeZones: Map[Long, String] = params.organizationsWithTimeZones
  override val locations: Set[Long] = params.locations
  override val breeds: Set[Breed] = params.breeds

  private val GROUP_ANIMALS_AMOUNT = "DEAD_GROUP_ANIMALS_AMOUNT"
  private val GROUP_ANIMALS_WEIGHT = "DEAD_GROUP_ANIMALS_WEIGHT"

  val amountKpiSourceId = getKpiSourceId(GROUP_ANIMALS_AMOUNT, params.animalTypeOpt)
  val weightKpiSourceId = getKpiSourceId(GROUP_ANIMALS_WEIGHT, params.animalTypeOpt)

  // 1. define fields
  private val fAmount = KpiField(amountKpiSourceId)
  private val fWeight = KpiField(weightKpiSourceId)
  private val fLocationId = KpiField("_location_id")
  private val fDeadType = KpiField("F_DEAD_TYPE")
  private val fDeadReason = KpiField("F_DEAD_REASON")

  // 2. define provided KPIs - it enables you to link KPI to a field name
  // TODO: Add descriptions as they are in Java version
  private val fieldsAndBaseNames = Map[KpiField, String](
    fAmount -> amountKpiSourceId,
    fWeight -> weightKpiSourceId,
  )

  // override aggregated fields
  override lazy val aggregatedFields = fieldsAndBaseNames.keys.toSeq

  // register datasource
  override def registerKpiDataSources(registry: KpiDataSourceRegistry): Unit = {
    fieldsAndBaseNames.foreach {
      case (kpiField, baseName) =>
        // TODO: add registration of descriptions from Java source
        // register static KPI
        registerSingleKpiDataSource(kpiField, baseName, KpiDescription.UnusedNewDataSource)(registry)

        val dynamicSources: Set[KpiDynamicSource] = Set(
          KpiDynamicSource.DeathType, // Death type recorded.
          KpiDynamicSource.DeathReason, // Death reason recorded.
        )

        dynamicSources.foreach { dynamicSource =>
          // register dynamic KPIs per feed type
          registerDynamicKpiDataSource(
            kpiField,
            getDynamicKpiSourceId(baseName, params.animalTypeOpt, dynamicSource.placeHolder),
            dynamicSource,
            KpiDescription.UnusedNewDataSource,
          )(registry)
        }
    }
  }

  def inhabitantTypeCode: Set[AnimalType] = {
    params.animalTypeOpt match {
      case Some(AnimalType.Maiden) => Set(AnimalType.Maiden, AnimalType.Sow, AnimalType.Boar)
      case Some(animalType)        => Set(animalType)
      case None                    => Set.empty
    }
  }

  // 3. implement getSingleOrgQuery
  override def getSingleOrgQuery(orgId: OrgId, dynamicSource: KpiDynamicSource): SingleOrgQueryParams => SingleOrgQuery = {
    case SingleOrgQueryParams(orgTimeZone: OrgTz, orgSchema: OrgSchemaSqls) =>
      val helper = KpiQueryHelper(params, orgTimeZone, orgSchema)
      import helper.{ orgSchema => _, orgTimeZone => _, params => _, _ }

      val inhabitantTypeCode: Option[String] = params.animalTypeOpt.map(_.dbCode)
      val progenyTypeCode = params.progenyTypeCodeOpt

      val groupByDeadType = dynamicSource == KpiDynamicSource.DeathType
      val groupByDeadReason = dynamicSource == KpiDynamicSource.DeathReason

      val deathTypeDescription =
        if (groupByDeadType) sqls"deathtype.description AS ${fDeadType.asSqls}" else sqls"NULL AS ${fDeadType.asSqls}"
      val deadReasonDescription =
        if (groupByDeadReason) sqls"illnesstype.description AS ${fDeadReason.asSqls}" else sqls"NULL AS ${fDeadReason.asSqls}"

      val ctData = sqls"""
        |_ct_data AS (
        |  SELECT DISTINCT ON (dead.id, $SEQ)
        |    $SEQ,
        |    dead.amount AS ${fAmount.asSqls},
        |    dead.weight AS ${fWeight.asSqls},
        |    coalesce(batch_location.location_id, dead.location_id) AS ${fLocationId.asSqls},
        |    $deathTypeDescription,
        |    $deadReasonDescription
        |  FROM ${orgSchema}.dead
        |    JOIN ${orgSchema}.location ON
        |      location.id = dead.location_id
        |    JOIN dates ON (
        |      dead.actor_date >= (from_date::timestamp AT TIME ZONE $orgTimeZone) AND
        |      dead.actor_date < ((to_date + interval '1 day')::timestamp AT TIME ZONE $orgTimeZone)
        |    )
        |    JOIN ${orgSchema}.deathtype ON
        |      deathtype.code = dead.deathtype_code
        |    LEFT OUTER JOIN ${orgSchema}.illnesstype ON
        |      illnesstype.code = dead.illnesstype_code
        |    LEFT OUTER JOIN ${orgSchema}.batch_location ON
        |      (batch_location.batch_id = dead.location_id AND coalesce(batch_location.entry_date, location.validfrom) <= dead.actor_date)
        |  WHERE
        |    dead.deathtype_code IS NOT NULL AND
        |    dead.sow_id IS NULL AND
        |    dead.boar_id IS NULL AND
        |    dead.gilt_id IS NULL AND
        |    (${inhabitantTypeCode.map(_ => sqls"dead.serving_id IS NULL").getOrElse(sqls"TRUE")})
        |  ORDER BY
        |    dead.id asc,
        |    seq asc,
        |    batch_location.entry_date desc nulls Last
        |)
      """.stripMargin

      val mainQuery = sqls"""
        |WITH
        |  ${params.datesCTE},
        |  $ctData
        |SELECT
        |  $orgId AS orgId,
        |  $SEQ,
        |  sum(${fAmount.asSqls}) AS ${fAmount.asSqls},
        |  sum(${fWeight.asSqls}) AS ${fWeight.asSqls},
        |  ${fDeadType.asSqls},
        |  ${fDeadReason.asSqls}
        |FROM _ct_data
        |  JOIN ${orgSchema}.location ON ${fLocationId.asSqls} = location.id
        |WHERE
        |  ${inhabitantTypeCode.map(inhabitant => sqls"location.inhabitanttype_code in ($inhabitant)").getOrElse(sqls"TRUE")} AND
        |  ${progenyTypeCode.map(progeny => sqls"location.progeny_type_code in ($progeny)").getOrElse(sqls"TRUE")} AND
        |  (${locationsColumnCondition(sqls"location.id")})
        |GROUP BY $SEQ, ${fDeadType.asSqls}, ${fDeadReason.asSqls}
      """.stripMargin

      mainQuery
  }

  /**
   * Implement this method to define which field in final query and sub-queries represent column for grouping by dynamic source.
   * For example `feedname` column for dynamic KPIs per feed name
   *
   * @param dynamicSource Dynamic source for which to get the field
   * @return Field representing column for grouping
   */
  override def getDynamicFieldFor(dynamicSource: KpiDynamicSource): KpiField = {
    dynamicSource match {
      case KpiDynamicSource.DeathType   => fDeadType
      case KpiDynamicSource.DeathReason => fDeadReason
      case KpiDynamicSource.NoSource    => fDeadType
      case _                            => throw new IllegalArgumentException(s"Unsupported dynamic source: $dynamicSource")
    }
  }
}

object DeadGroupAnimalsKpisDataSourceSc extends KpiDataSourceFactory {

  override def createAndRegister(params: KpiDataSourceParams): Unit =
    new DeadGroupAnimalsKpisDataSourceSc(params).registerKpiDataSources(params.registry)

}
