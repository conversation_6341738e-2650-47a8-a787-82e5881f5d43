package reports.kpi.calculators.data.group;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableSet;
import com.google.protobuf.BoolValue;
import models.InhabitantType;
import models.handlers.SettingsHandler;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.*;
import reports.kpi.KpiUtilsDI;
import reports.kpi.calculators.data.InhabitantCommon;
import reports.kpi.calculators.data.KpiDataSourceRegistrar;
import reports.kpi.calculators.data.KpiDataSourceRegistry;
import security.ApplicationUser;
import utils.JooqUtilsDI;
import utils.avgweight.AverageWeightCalculator;
import utils.avgweight.AvgWeightCalculator;
import utils.avgweight.AvgWeightCalculatorSplitter;
import utils.jooq.CloudfarmsDSL;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static jooq.farm.Tables.BATCH_LOCATION;
import static jooq.farm.Tables.LOCATION;
import static org.jooq.impl.DSL.*;
import static org.jooq.impl.DSL.lead;
import static reports.kpi.calculators.data.AbstractKpiDataSource.FROM_DATE;
import static reports.kpi.calculators.data.AbstractKpiDataSource.SEQ;
import static reports.kpi.calculators.data.AbstractKpiDataSource.TO_DATE;

import static utils.jooq.CloudfarmsDSL.*;

/**
 * Created by Gabriel on 10.04.2017.
 */
abstract class AbstractBatchRelocationKpisDataSource extends InhabitantCommon implements KpiDataSourceRegistrar {
    private static final Field<Long> F_BATCH_ID = field("_batch_id", Long.class);
    private static final Field<Long> F_BATCH_FROM_LOCATION_ID = field("_batch_from_location_id", Long.class);
    private static final Field<Long> F_BATCH_TO_LOCATION_ID = field("_batch_to_location_id", Long.class);
    private static final Field<Timestamp> F_BATCH_RELOCATION_TIMESTAMP = field("_batch_relocation_timestamp", Timestamp.class);
    private static final Field<Date> F_BATCH_RELOCATION_DATE = field("_batch_relocation_date", Date.class);

    private final List<Pair<LocalDate, LocalDate>> periods;
    private final Set<Long> locations;
    private final Optional<String> inhabitantTypeCode;
    private final Optional<String> progenyTypeCode;
    private final String amountKpi;
    private final String weightKpi;
    private final Field<Long> fAmount;
    private final Field<BigDecimal> fWeight;
    private final String timeZone;
    private final boolean isIn;
    private final ApplicationUser user;
    private final Connection conn;
    protected final SettingsHandler settingsHandler;

    private Map<Integer, List<Pair<AverageWeightCalculator.LocationId, LocalDateTime>>> batchRelocations;
    private List<LocalDateTime> avgWeightDateTimes;
    private Optional<AvgWeightCalculator> maybeAvgWeightCalculator;

    protected AbstractBatchRelocationKpisDataSource(List<Pair<LocalDate, LocalDate>> periods, Set<Long> locations, boolean in, Optional<String> inhabitantTypeCode, Optional<String> progenyTypeCode, SettingsHandler settingsHandler, ApplicationUser user, Connection conn) {
        this.user = user;
        this.conn = conn;
        this.periods = Collections.unmodifiableList(periods);
        this.locations = Collections.unmodifiableSet(locations);
        this.inhabitantTypeCode = inhabitantTypeCode;
        this.progenyTypeCode = progenyTypeCode;
        this.amountKpi = inhabitantTypeCode.orElse("LOCATION").concat("_BATCH_RELOCATION_").concat(in ? "IN" : "OUT").concat("_AMOUNT");
        this.weightKpi = inhabitantTypeCode.orElse("LOCATION").concat("_BATCH_RELOCATION_").concat(in ? "IN" : "OUT").concat("_TOTAL_WEIGHT");
        this.fAmount = CloudfarmsDSL.kpiField(amountKpi, Long.class);
        this.fWeight = CloudfarmsDSL.kpiField(weightKpi, BigDecimal.class);
        this.timeZone = user == null ? "UTC" : user.getFarmTimezoneName();
        this.isIn = in;
        this.settingsHandler = settingsHandler;
    }

    /**
     * Returns specific claim for the KPI description.
     */
    abstract String inOutClaim();

    @Override
    public void registerKpiDataSources(KpiDataSourceRegistry registry) {
        String inhabitantPlural = getInhabitantPlural(inhabitantTypeCode);
        registry.addKpiDataSource(
            amountKpi,
            (orgId, index) -> {
                initialize();
                List<Pair<AverageWeightCalculator.LocationId, LocalDateTime>> relocations = this.batchRelocations.getOrDefault(index, Collections.emptyList());

                if (relocations.isEmpty()) {
                    return OptionalDouble.empty();
                } else {
                    return OptionalDouble.of(
                        relocations.stream()
                            .map(d -> this.maybeAvgWeightCalculator.map(awc -> awc.getLocationAverageWeight(this.avgWeightDateTimes.indexOf(d.getRight()) + 1, d.getLeft())))
                            .mapToInt(d -> d.map(v -> v.amount).orElse(0))
                            .sum()
                    );
                }
            },
            "Provides total amount of " + inhabitantPlural + " organized in batches (groups) which were relocated " + inOutClaim() + " in the reporting interval."
        );

        registry.addKpiDataSource(
            weightKpi,
            (orgId, index) -> {
                initialize();
                List<Pair<AverageWeightCalculator.LocationId, LocalDateTime>> relocations = this.batchRelocations.getOrDefault(index, Collections.emptyList());

                if (relocations.isEmpty()) {
                    return OptionalDouble.empty();
                } else {
                    return OptionalDouble.of(
                        relocations.stream()
                            .map(d -> this.maybeAvgWeightCalculator.map(awc -> awc.getLocationAverageWeight(this.avgWeightDateTimes.indexOf(d.getRight()) + 1, d.getLeft())))
                            .mapToDouble(d -> d.map(v -> v.totalWeight).orElse(0d))
                            .sum()
                    );
                }
            },
            "Provides total weights of " + inhabitantPlural + " organized in batches (groups) which were relocated " + inOutClaim() + " in the reporting interval. Total weight equals to sum of average weights of every batch relocations in the scope."
        );
    }

    private synchronized void initialize() {
        if (maybeAvgWeightCalculator == null) {
            WindowDefinition w = name("w").as(partitionBy(BATCH_LOCATION.BATCH_ID).orderBy(BATCH_LOCATION.ENTRY_DATE.asc().nullsFirst()));
            CommonTableExpression<?> ctBatchRelocation = name("_ct_batch_relocation").as(
                select(
                    BATCH_LOCATION.BATCH_ID.as(F_BATCH_ID),
                    //lead(asDateAtTimeZone(BATCH_LOCATION.ENTRY_DATE, timeZone)).over(w).as(F_BATCH_RELOCATION_DATE),
                    lead(BATCH_LOCATION.ENTRY_DATE).over(w).as(F_BATCH_RELOCATION_TIMESTAMP),
                    BATCH_LOCATION.LOCATION_ID.as(F_BATCH_FROM_LOCATION_ID),
                    lead(BATCH_LOCATION.LOCATION_ID).over(w).as(F_BATCH_TO_LOCATION_ID)
                )
                    .from(BATCH_LOCATION)
                    .window(w)
            );
            CommonTableExpression<?> dates = KpiUtilsDI.periodsWithClause(periods, SEQ, FROM_DATE, TO_DATE);
            Result<Record4<Integer, Long, String, Timestamp>> r = JooqUtilsDI.getConnectionDSLContext(conn).with(dates).with(ctBatchRelocation)
                .select(
                    SEQ,
                    F_BATCH_ID,
                    LOCATION.INHABITANTTYPE_CODE,
                    F_BATCH_RELOCATION_TIMESTAMP
                )
                .from(ctBatchRelocation)
                .join(LOCATION).on(LOCATION.ID.eq(F_BATCH_ID))
                .join(dates).on(F_BATCH_RELOCATION_TIMESTAMP.ge(asTimestampAtTimeZone(FROM_DATE, timeZone)).and(F_BATCH_RELOCATION_TIMESTAMP.lt(asTimestampAtTimeZoneNextDay(TO_DATE, timeZone))))
                .where(
                    inhabitantTypeCode.map(itc -> LOCATION.INHABITANTTYPE_CODE.eq(itc)).orElse(trueCondition())
                        .and(progenyTypeCode.map(ptc -> LOCATION.PROGENY_TYPE_CODE.eq(ptc)).orElse(trueCondition()))
                        .and(isIn ? F_BATCH_TO_LOCATION_ID.in(locations) : F_BATCH_FROM_LOCATION_ID.in(locations))
                        .and(isIn ? F_BATCH_FROM_LOCATION_ID.notIn(locations) : F_BATCH_TO_LOCATION_ID.notIn(locations))
                ).fetch();
            ZoneId tz = ZoneId.of(timeZone);
            Set<AverageWeightCalculator.LocationId> batchIds = r.stream().map(rec -> new AverageWeightCalculator.LocationId(rec.value2(), rec.value3())).collect(Collectors.toSet());
            this.avgWeightDateTimes = new ArrayList<>(r.stream().map(rec -> LocalDateTime.ofInstant(rec.value4().toInstant(), tz)).collect(Collectors.toSet()));
            Collections.sort(this.avgWeightDateTimes);
            this.batchRelocations = r.stream().collect(
                Collectors.groupingBy(
                    val -> val.value1(),
                    Collectors.collectingAndThen(
                        Collectors.toList(), l -> l.stream().map(rec -> Pair.of(new AverageWeightCalculator.LocationId(rec.value2(), rec.value3()), LocalDateTime.ofInstant(rec.value4().toInstant(), tz))).collect(Collectors.toList())
                    )
                )
            );
            if (this.avgWeightDateTimes.isEmpty()) {
                this.maybeAvgWeightCalculator = Optional.empty();
            } else {
                this.maybeAvgWeightCalculator = Optional.of(
                    new AvgWeightCalculatorSplitter(batchIds, this.avgWeightDateTimes, user, conn, settingsHandler).getCalculatorBasedOnSetting()
                );
            }
        }
    }
}
