package reports.kpi.calculators.data

import com.cloudfarms.settings.Setting
import com.google.common.collect.ImmutableSet
import domainz.time.LocalDateInterval
import domains.organizationstructure.OrganizationStructureService
import domainz.organization.Organization._
import models.handlers.SettingsHandler
import org.apache.commons.lang3.tuple.Pair
import play.api.db.DBApi
import reports.dataprovider.CommonDataHelper
import reports.kpi.KpiUtilsDI
import reports.kpi.calculators.data.analysis.{ AbstractSowAnalysisKpisDataSourceBuilder, SowAnalysisKpisDataSourceImpl }
import reports.kpi.calculators.data.parity.{ ParitiesKpisDataSourceBuilder, ParitiesKpisDataSourceImpl }
import scalikejdbc.{ DBSession, _ }
import security.ApplicationUser
import tenant.AccountFarmLangSession
import utils.{ CalendarUtilsJTime, JooqUtilsDI }

import java.sql.Connection
import java.time.LocalDate
import java.util.{ Collections, Optional }
import java.{ lang, util }
import javax.inject.{ Inject, Singleton }
import scala.jdk.CollectionConverters._
import scala.language.existentials

/**
 * Service responsible for creating KpisDataSource implementations. All injectable parameters used in datasources should be
 * provided here.
 */

@Singleton
class KpisDataSourceService @Inject() (
  dbApi: DBApi,
  setting: Setting,
  settingsHandler: SettingsHandler,
  calendarUtilsJTime: CalendarUtilsJTime,
  organizationStructureService: OrganizationStructureService,
) {

  val PROGENY_TYPE_PARAM = "progenyType"
  val BREED_PARAM = "breed"

  /**
   * Default values for java constructors of [[KpisDataSourceImpl]]
   */
  val defaultUseServiceGroups = false
  val emptyLocationSet = Collections.emptySet[java.lang.Long]

  /**
   * Return list of supported raw KPIs for current user from [[KpisDataSourceImpl]] implementation.
   * 
   * @param user User
   * @param em Entity manager
   * @return List of supported KPIs
   */
  def getSupportedKpiSources(user: ApplicationUser, conn: Connection) = {

    val emptyPeriods = new util.ArrayList[Pair[LocalDate, LocalDate]]()
    val emptyBreeds = new util.HashSet[String]()

    val kpisDataSource = new KpisDataSourceImpl(
      settingsHandler,
      user,
      emptyLocationSet,
      emptyPeriods,
      emptyBreeds,
      conn,
      dbApi,
    )
    kpisDataSource.getSupportedKpiSources.asScala
  }

  def getSupportedDynamicKpiSources(user: ApplicationUser, conn: Connection) = {

    val emptyPeriods = new util.ArrayList[Pair[LocalDate, LocalDate]]()
    val emptyBreeds = new util.HashSet[String]()

    val kpisDataSource = new KpisDataSourceImpl(
      settingsHandler,
      user,
      emptyLocationSet,
      emptyPeriods,
      emptyBreeds,
      conn,
      dbApi,
    )
    kpisDataSource.getSupportedDynamicKpiSources.asScala
  }

  /**
   * Provide a list of all KPI descriptions from registered KPI datasources.
   * @param user user
   * @param conn db connection
   * @return map of KPI code to description
   */
  def getKpiDescriptions(user: ApplicationUser, conn: Connection) = {
    val emptyPeriods = new util.ArrayList[Pair[LocalDate, LocalDate]]()
    val emptyBreeds = new util.HashSet[String]() // TODO: all breeds

    val kpisDataSource = new KpisDataSourceImpl(
      settingsHandler,
      user,
      emptyLocationSet,
      emptyPeriods,
      emptyBreeds,
      conn,
      dbApi,
    )
    kpisDataSource.getAllKpiDescriptions.asScala
  }

  /**
   * Based on legacy [[reports.kpi.calculators.data.OrganizationKpisDataSourceFactory]]
   * Return KpisDataSource implementation for a single organization.
   * Best for calling from Java code.
   *
   * @param user User
   * @param em Entity manager TODO: should be replaced with DBApi
   * @param organizationId Organization id
   * @param periods List of calculation intervals
   * @param params Additional parameters like a breed and progeny type
   * @return
   */
  def getKpisDataSourceForOrganization(
    user: ApplicationUser,
    conn: Connection,
    organizationId: java.lang.Long,
    periods: java.util.List[Pair[LocalDate, LocalDate]],
    params: java.util.Map[String, java.util.Set[Object]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      organizationsWithTimeZoneDb(conn, Seq(organizationId)).map(keyAsJavaLong).asJava,
      emptyLocationSet,
      periods,
      breedsFromParams(params),
      progenyTypeCodeFromParams(params),
      defaultUseServiceGroups,
      conn,
      dbApi,
    )
  }

  /**
   * Based on legacy [[reports.kpi.calculators.data.OrganizationKpisDataSourceFactory]]
   * Return KpisDataSource implementation for a single organization.
   * Best for calling from Scala code.
   *
   * @param user User
   * @param conn Connection
   * @param organizationId Organization id
   * @param intervals List of calculation intervals
   * @return
   */
  def getKpisDataSourceForOrganization(
    user: ApplicationUser,
    conn: Connection,
    organizationId: Long,
    // useServiceGroups: Boolean, // Why it is not used here?
    intervals: Seq[LocalDateInterval],
  ): KpisDataSource = {
    val javaIntervals = intervals.map(interval => Pair.of(interval.from, interval.to)).asJava
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      organizationsWithTimeZoneDb(conn, Seq(organizationId)).map(keyAsJavaLong).asJava,
      javaIntervals,
      util.Collections.emptySet(),
      conn,
      dbApi,
    )
  }

  def getKpisDataSourceForOrganization(
    user: ApplicationUser,
    conn: Connection,
    organizationId: Long,
    // useServiceGroups: Boolean, // Why it is not used here?
    periods: Seq[Pair[LocalDate, LocalDate]],
    params: java.util.Map[String, java.util.Set[Object]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      organizationsWithTimeZoneDb(conn, Seq(organizationId)).map(keyAsJavaLong).asJava,
      periods.asJava,
      breedsFromParams(params),
      conn,
      dbApi,
    )
  }

  /**
   * Based on legacy [[reports.kpi.calculators.data.OrganizationKpisDataSourceFactory]]
   * Return KpisDataSource implementation for multiple organization
   * @param user User
   * @param conn DB connection
   * @param organizationIds Organization ids
   * @param periods List of calculation intervals
   * @param params Additional parameters like a breed and progeny type
   * @return KpisDataSources implementation
   */
  // TODO: not used? delete me???
  def getKpisDataSourceForOrganization(
    user: ApplicationUser,
    conn: Connection,
    organizationIds: util.Collection[java.lang.Long],
    // useServiceGroups: Boolean, // Why it is not used here?
    periods: util.List[Pair[LocalDate, LocalDate]],
    params: java.util.Map[String, java.util.Set[Object]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      organizationsWithTimeZoneDb(conn, organizationIds.asScala.map(Long2long)).map(keyAsJavaLong).asJava,
      emptyLocationSet,
      periods,
      breedsFromParams(params),
      progenyTypeCodeFromParams(params),
      defaultUseServiceGroups,
      conn,
      dbApi,
    )
  }

  def getKpisDataSourceForOrganization(
    user: ApplicationUser,
    conn: Connection,
    organizationIds: Set[Long],
    // useServiceGroups: Boolean, // Why it is not used here?
    periods: util.List[Pair[LocalDate, LocalDate]],
    params: java.util.Map[String, java.util.Set[Object]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      organizationsWithTimeZoneDb(conn, organizationIds).map(keyAsJavaLong).asJava,
      emptyLocationSet,
      periods,
      breedsFromParams(params),
      progenyTypeCodeFromParams(params),
      defaultUseServiceGroups,
      conn,
      dbApi,
    )
  }

  /**
   * Based on legacy [[reports.kpi.calculators.data.LocationHierarchyKpisDataSourceFactory]]
   * Return KpisDataSource implementation for location hierarchy from single location.
   * @param user User
   * @param conn DB connection
   * @param locationId Location id
   * @param useServiceGroups If true, service groups are considered in KPI datasources that support it
   * @param periods List of calculation intervals
   * @param params Additional parameters like breed and progeny type
   * @return
   */
  def getKpisDataSourceForLocationHierarchy(
    user: ApplicationUser,
    conn: Connection,
    locationId: Long,
    useServiceGroups: Boolean,
    periods: java.util.List[Pair[LocalDate, LocalDate]],
    params: java.util.Map[String, java.util.Set[Object]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      locationsForParentLocations(conn, user.farmId(), Seq(locationId)).map(long2Long).asJava,
      periods,
      breedsFromParams(params),
      progenyTypeCodeFromParams(params),
      useServiceGroups,
      conn,
      dbApi,
    )
  }

  /**
   * Based on legacy [[reports.kpi.calculators.data.LocationHierarchyKpisDataSourceFactory]]
   * Return KpisDataSource implementation for location hierarchy from multiple location.
   * @param user User
   * @param conn DB connection
   * @param locationIds Location ids
   * @param useServiceGroups If true, service groups are considered in KPI datasources that support it
   * @param periods List of calculation intervals
   * @param params Additional parameters like breed and progeny type
   * @return
   */
  def getKpisDataSourceForLocationHierarchy(
    user: ApplicationUser,
    conn: Connection,
    locationIds: Seq[Long],
    useServiceGroups: Boolean,
    periods: util.List[Pair[LocalDate, LocalDate]],
    params: util.Map[String, util.Set[Object]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      locationsForParentLocations(conn, user.farmId(), locationIds).map(long2Long).asJava,
      periods,
      breedsFromParams(params),
      progenyTypeCodeFromParams(params),
      useServiceGroups,
      conn,
      dbApi,
    )
  }

  def getKpisDataSourceForLocationHierarchy(
    user: ApplicationUser,
    conn: Connection,
    locationId: Long,
    periods: util.List[Pair[LocalDate, LocalDate]],
  ): KpisDataSource = {
    val emptyParams = util.Collections.emptyMap[String, util.Set[Object]]()
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      locationsForParentLocations(conn, user.farmId(), Seq(locationId)).map(long2Long).asJava,
      periods,
      breedsFromParams(emptyParams),
      progenyTypeCodeFromParams(emptyParams),
      false,
      conn,
      dbApi,
    )
  }

  def getKpisDataSourceForLocationHierarchy(
    user: ApplicationUser,
    conn: Connection,
    locationId: Long,
    periods: Seq[Pair[LocalDate, LocalDate]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      locationsForParentLocations(conn, user.farmId(), Seq(locationId)).map(long2Long).asJava,
      periods.asJava,
      util.Collections.emptySet(),
      conn,
      dbApi,
    )
  }

  // best for calling from scala code
  def getKpisDataSourceForLocationHierarchy(
    user: ApplicationUser,
    conn: Connection,
    locationIds: Seq[Long],
    periods: Seq[Pair[LocalDate, LocalDate]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      locationsForParentLocations(conn, user.farmId(), locationIds).map(long2Long).asJava,
      periods.asJava,
      util.Collections.emptySet(),
      conn,
      dbApi,
    )
  }

  def getKpisDataSourceForLocationHierarchy(
    user: ApplicationUser,
    conn: Connection,
    locationIds: Seq[Long],
    useServiceGroups: Boolean,
    periods: Seq[Pair[LocalDate, LocalDate]],
    params: util.Map[String, util.Set[Object]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      locationsForParentLocations(conn, user.farmId(), locationIds).map(long2Long).asJava,
      periods.asJava,
      breedsFromParams(params),
      progenyTypeCodeFromParams(params),
      useServiceGroups,
      conn,
      dbApi,
    )
  }

  def getKpisDataSourceForLocationHierarchy(
    user: ApplicationUser,
    conn: Connection,
    locationIds: util.Collection[java.lang.Long],
    useServiceGroups: Boolean,
    periods: util.List[Pair[LocalDate, LocalDate]],
    params: java.util.Map[String, java.util.Set[Object]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      locationsForParentLocations(conn, user.farmId(), locationIds.asScala.map(Long2long)).map(long2Long).asJava,
      periods,
      breedsFromParams(params),
      progenyTypeCodeFromParams(params),
      useServiceGroups,
      conn,
      dbApi,
    )
  }

  def getKpisDataSourceForLocationHierarchy(
    user: ApplicationUser,
    conn: Connection,
    locationIds: util.Collection[java.lang.Long],
    periods: util.List[Pair[LocalDate, LocalDate]],
    params: java.util.Map[String, java.util.Set[Object]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      locationsForParentLocations(conn, user.farmId(), locationIds.asScala.map(Long2long)).map(long2Long).asJava,
      periods,
      breedsFromParams(params),
      progenyTypeCodeFromParams(params),
      defaultUseServiceGroups,
      conn,
      dbApi,
    )
  }

  /**
   * Based on legacy [[reports.kpi.calculators.data.OrganizationWithLocationKpisDataSourceFactory]]
   * return KpisDataSource for combination of organization ids and location ids.
   * @param user User
   * @param conn DB connection
   * @param organizationIds Organization ids
   * @param locationIds Location ids
   * @param useServiceGroups If true, service groups are considered in KPI datasources that support it
   * @param periods List of calculation intervals
   * @param params Additional parameters like breed and progeny type
   * @return
   */

  def getKpisDataSourceForOrganizationAndLocation(
    user: ApplicationUser,
    conn: Connection,
    organizationIds: Seq[Long],
    locationIds: Seq[Long],
    useServiceGroups: Boolean,
    periods: Seq[Pair[LocalDate, LocalDate]],
    params: java.util.Map[String, java.util.Set[Object]] = Collections.emptyMap(),
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      organizationsWithTimeZone(organizationIds).asJava,
      locationsForParentLocations(conn, user.farmId(), locationIds).map(long2Long).asJava,
      periods.asJava,
      breedsFromParams(params),
      progenyTypeCodeFromParams(params),
      useServiceGroups,
      conn,
      dbApi,
    )
  }

  /**
   * Based on legacy [[reports.kpi.calculators.data.HoldingKpisDataSourceFactory]]
   * return KpisDataSource for combination of holding ids.
   * @param user User
   * @param conn DB connection
   * @param holdingIds Holdings
   * @param useServiceGroups If true, service groups are considered in KPI datasources that support it
   * @param periods List of calculation intervals
   * @param params Additional parameters like breed and progeny type
   * @return [[KpisDataSource]] implementation
   */
  def getKpisDataSourceForHolding(
    user: ApplicationUser,
    conn: Connection,
    holdingIds: Seq[Long],
    useServiceGroups: Boolean,
    periods: Seq[Pair[LocalDate, LocalDate]],
    // Tip: consider to remove params parameter and use the following two parameters instead
    // breed: util.Collection[String],
    // progenyTypeCodeOpt: Option[String] = None,
    params: java.util.Map[String, java.util.Set[Object]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      allOrganizationsFromHoldings(conn, holdingIds).map(keyAsJavaLong).asJava,
      periods.asJava,
      breedsFromParams(params), // ImmutableSet.copyOf(breed: lang.Iterable[String]),
      progenyTypeCodeFromParams(params).get(), // toJava(progenyTypeCodeOpt),
      conn,
      dbApi,
    )
  }

  def getKpisDataSourceForHolding(
    user: ApplicationUser,
    conn: Connection,
    holdingIds: util.List[java.lang.Long],
    useServiceGroups: Boolean,
    periods: util.List[Pair[LocalDate, LocalDate]],
    // Tip: consider to remove params parameter and use the following two parameters instead
    // breed: util.Collection[String],
    // progenyTypeCodeOpt: Option[String] = None,
    params: java.util.Map[String, java.util.Set[Object]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      allOrganizationsFromHoldings(conn, holdingIds.asScala.map(_.longValue())).map(keyAsJavaLong).asJava,
      periods,
      breedsFromParams(params), // ImmutableSet.copyOf(breed: lang.Iterable[String]),
      progenyTypeCodeFromParams(params).get(), // toJava(progenyTypeCodeOpt),
      conn,
      dbApi,
    )
  }

  /**
   * Based on legacy [[reports.kpi.calculators.data.HoldingKpisDataSourceFactory]]
   * return KpisDataSource for single holding.
   * @param user User
   * @param conn DB connection
   * @param holdingId Holding id
   * @param useServiceGroups If true, service groups are considered in KPI datasources that support it
   * @param periods List of calculation intervals
   * @param params Additional parameters like breed and progeny type
   * @return [[KpisDataSource]] implementation
   */
  def getKpisDataSourceForHolding(
    user: ApplicationUser,
    conn: Connection,
    holdingId: java.lang.Long,
    useServiceGroups: Boolean,
    periods: java.util.List[Pair[LocalDate, LocalDate]],
    // Tip: consider to remove params parameter and use the following two parameters instead
    // breed: util.Collection[String],
    // progenyTypeCodeOpt: Option[String] = None,
    params: java.util.Map[String, java.util.Set[Object]],
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      allOrganizationsFromHoldings(conn, Seq(holdingId)).map(keyAsJavaLong).asJava,
      emptyLocationSet,
      periods,
      breedsFromParams(params), // ImmutableSet.copyOf(breed: lang.Iterable[String]),
      progenyTypeCodeFromParams(params),
      defaultUseServiceGroups,
      conn,
      dbApi,
    )
  }

  /**
   * Based on legacy [[reports.kpi.calculators.data.OrganizationWithLocationKpisDataSourceFactory]]
   * return KpisDataSource for combination of holding ids and location ids.
   * @param user User
   * @param conn DB connection
   * @param holdingIds Holdings
   * @param locationIds Locations
   * @param useServiceGroups If true, service groups are considered in KPI datasources that support it
   * @param periods List of calculation intervals
   * @param params Additional parameters like breed and progeny type
   * @return [[KpisDataSource]] implementation
   */
  def getKpisDataSourceForHoldingWithLocation(
    user: ApplicationUser,
    conn: Connection,
    holdingIds: Iterable[Long],
    locationIds: Seq[Long],
    useServiceGroups: Boolean,
    periods: Seq[Pair[LocalDate, LocalDate]],
    // Tip: consider to remove params parameter and use the following two parameters instead
    // breed: util.Collection[String],
    // progenyTypeCodeOpt: Option[String] = None,
    params: java.util.Map[String, java.util.Set[Object]] = Collections.emptyMap(),
  ): KpisDataSource = {
    new KpisDataSourceImpl(
      settingsHandler,
      user,
      organizationsWithinHoldings(holdingIds).map { case (key, value) => long2Long(key) -> value }.asJava,
      locationsForParentLocations(conn, user.farmId(), locationIds).map(long2Long).asJava,
      periods.asJava,
      breedsFromParams(params), // ImmutableSet.copyOf(breed: lang.Iterable[String]),
      progenyTypeCodeFromParams(params), // toJava(progenyTypeCodeOpt),
      useServiceGroups,
      conn,
      dbApi,
    )
  }

  /**
   * Based on legacy [[reports.kpi.calculators.data.OrganizationWithLocationKpisDataSourceFactory]]
   * return KpisDataSource for combination of holding ids and location ids.
   * @param user User
   * @param conn DB connection
   * @param organizations Organizations
   * @param useServiceGroups If true, service groups are considered in KPI datasources that support it
   * @param periods List of calculation intervals
   * @param params Additional parameters like breed and progeny type
   * @return [[KpisDataSource]] implementation
   */
  def getKpisDataSourceForOrganizations(
    user: ApplicationUser,
    conn: Connection,
    organizations: Iterable[FlatOrganizationEntity],
    useServiceGroups: Boolean,
    periods: Seq[Pair[LocalDate, LocalDate]],
    params: java.util.Map[String, java.util.Set[Object]] = Collections.emptyMap(),
  ): KpisDataSource = {
    implicit val dbSession: DBSession = AccountFarmLangSession.dbSessionFromConnection(conn)
    val farmIds = organizationStructureService.getAllFarmIds(organizations.toSeq)(user, dbSession)
    val allFarmIdsWithTimezones = organizationsWithTimeZone(farmIds)

    new KpisDataSourceImpl(
      settingsHandler,
      user,
      allFarmIdsWithTimezones.asJava,
      Set.empty[java.lang.Long].asJava,
      periods.asJava,
      breedsFromParams(params),
      progenyTypeCodeFromParams(params),
      useServiceGroups,
      conn,
      dbApi,
    )
  }

  /**
   * Use this datasource for special cases where you need to calculate KPIs for parities.
   * @param user User
   * @param conn DB connection
   * @param parities Parities from 0 and up
   * @param parentLocationIds Parent location ids
   * @param fromDate Report start date
   * @param toDate Report end date
   * @param breeds Filter for breeds
   * @return [[KpisDataSource]] implementation
   */
  def getKpisDataSourceForParities(
    user: ApplicationUser,
    conn: Connection,
    parities: util.List[Integer],
    parentLocationIds: util.Set[java.lang.Long],
    fromDate: LocalDate,
    toDate: LocalDate,
    breeds: util.Set[String],
  ): KpisDataSource = {
    new ParitiesKpisDataSourceImpl(
      new ParitiesKpisDataSourceBuilder(fromDate, toDate, parities, calendarUtilsJTime, settingsHandler, user, conn, dbApi)
        .withBreeds(if (breeds.isEmpty) ImmutableSet.of() else ImmutableSet.copyOf(breeds))
        .withLocations(
          if (parentLocationIds.isEmpty || parentLocationIds.contains(-1L))
            ImmutableSet.of()
          else
            CommonDataHelper.getChildLocations(ImmutableSet.copyOf(parentLocationIds), JooqUtilsDI.getConnectionDSLContext(conn)),
        ),
      calendarUtilsJTime,
      settingsHandler,
      user,
      conn,
      dbApi,
      KpiUtilsDI.pigQualityClasses(conn, user.getFarmId()),
    )
  }

  def getKpisDataSourceForSowAnalysis[T](
    user: ApplicationUser,
    conn: Connection,
    builder: AbstractSowAnalysisKpisDataSourceBuilder[T],
    pigQualityClasses: util.Set[String],
  ): KpisDataSource = {
    new SowAnalysisKpisDataSourceImpl(
      builder,
      settingsHandler,
      user,
      conn,
      dbApi,
      pigQualityClasses,
    )
  }

  /* Support functions */

  private val keyAsJavaLong = { p: (Long, String) => long2Long(p._1) -> p._2 }

  /**
   * Extracts progeny type code from params
   * Based on legacy [[reports.kpi.calculators.data.AbstractKpisDataSourceFactory]]
   * @param params Map of Sets where set may contains String for progeny type. First one is used.
   * @return Optional progeny type code
   */
  private def progenyTypeCodeFromParams(params: java.util.Map[String, java.util.Set[Object]]): Optional[String] = {
    params.getOrDefault(PROGENY_TYPE_PARAM, Collections.emptySet)
      .stream.filter((p: AnyRef) => p.isInstanceOf[String])
      .map[String]((p: AnyRef) => p.asInstanceOf[String])
      .findFirst
  }

  /**
   * Extracts breeds from params
   * Based on legacy [[reports.kpi.calculators.data.AbstractKpisDataSourceFactory]]
   * @param params Map of Sets where set may contains Strings for breeds.
   * @return Set of breeds
   */
  private def breedsFromParams(params: java.util.Map[String, java.util.Set[Object]]): util.Set[String] = {
    params.getOrDefault(BREED_PARAM, Collections.emptySet)
      .stream.filter((p: AnyRef) => p.isInstanceOf[String])
      .map[String]((p: AnyRef) => p.asInstanceOf[String])
      .collect(util.stream.Collectors.toSet())
  }

  // TODO: dwo functions do the same but comes from different sources. Ask if second one that use
  // setting is needed [[organizationsWithTimeZone]].
  private def organizationsWithTimeZoneDb(conn: Connection, orgIds: Iterable[OrgId]): Map[Long, String] = {
    implicit val dbSession: DBSession = AccountFarmLangSession.dbSessionFromConnection(conn)
    if (orgIds.isEmpty) {
      Map.empty
    } else {
      sql"""
        |select id, timezonename
        |from organization
        |where id in ($orgIds)
        """.stripMargin
        .map(rs => rs.long("id") -> rs.string("timezonename"))
        .list
        .apply().toMap
    }
  }

  private def organizationsWithTimeZone(orgIds: Seq[OrgId]): Map[lang.Long, String] = {
    setting.farms
      .filter(f => orgIds.contains(f._1))
      .map(f => long2Long(f._1) -> f._2.timeZoneName.get)
  }

  private def organizationsWithinHoldings(holdingIds: Iterable[Long]): Map[Long, String] = {
    setting
      .farms
      .filter(f => (f._2.parents.map(_.id).toSet intersect holdingIds.toSet).nonEmpty)
      .map(f => f._1 -> f._2.timeZoneName.get)
  }

  private def allOrganizationsFromHoldings(conn: Connection, holdingIds: Iterable[Long]): Map[Long, String] = {
    implicit val dbSession: DBSession = AccountFarmLangSession.dbSessionFromConnection(conn)
    sql"""
    |select distinct on (organization.id) organization.id, organization.timezonename
    |from holding as parent_holding
    |  inner join holding on holding.prefix like concat(parent_holding.prefix, '%')
    |  inner join organization on holding.id = organization.holding_id
    | where parent_holding.id in ($holdingIds)
    """.stripMargin
      .map(rs => rs.long("id") -> rs.string("timezonename"))
      .list
      .apply().toMap
  }

  private def locationsForParentLocations(conn: Connection, farmId: Long, locationIds: Iterable[Long]): Set[Long] = {
    implicit val dbSession: DBSession = AccountFarmLangSession.dbSessionFromConnection(conn)
    if (locationIds.isEmpty) {
      Set.empty
    } else {
      val farmSchema: SQLSyntax = sqls"farm_${SQLSyntax.createUnsafely(farmId.toString)}"
      sql"""
      |select distinct location.id
      |from $farmSchema.location as parentlocation
      |  inner join $farmSchema.location on location.prefix like concat(parentlocation.prefix, '%')
      |where parentlocation.id in ($locationIds)
      """.stripMargin
        .map(rs => rs.long("id"))
        .list
        .apply().toSet
    }
  }

}
