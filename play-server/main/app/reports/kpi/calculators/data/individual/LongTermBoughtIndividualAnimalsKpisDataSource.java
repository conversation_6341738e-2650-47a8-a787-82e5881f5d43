package reports.kpi.calculators.data.individual;

import models.InhabitantType;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.CommonTableExpression;
import org.jooq.Field;
import org.jooq.Record3;
import org.jooq.Select;
import reactapp.shared.kpi.KpiDescription;
import reports.kpi.KpiUtilsDI;
import reports.kpi.calculators.data.AbstractKpiDataSource;
import reports.kpi.calculators.data.KpiDataSource;
import reports.kpi.calculators.data.KpiDataSourceRegistrar;
import reports.kpi.calculators.data.KpiDataSourceRegistry;
import security.ApplicationUser;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.Date;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static jooq.farm.Tables.*;
import static org.jooq.impl.DSL.*;
import static utils.jooq.CloudfarmsDSL.*;

/**
 * Created by <PERSON> on 23.10.2014.
 * <pre>
    with dates as (
        select
          "seqNum" as seq,
          "dateFrom" as datefrom,
          "dateTo" as dateto
        from
          json_populate_recordset(null::public.period_type, ?::json)
    ), org as (
        select timezonename tz
        from organization
        where id = get_tenant_organization()
    )
    select
      d.seq,
      d.datefrom,
      d.dateto,
      sum(1)::numeric as amount,
      sum(ti.pigliveweight)::numeric as weight
    from
      transferin t
      cross join org o
      join transferindividualin ti on t.id = ti.transferin_id
      join dates d on (t.actor_date at time zone o.tz)::date between d.datefrom and d.dateto
    where
      ti.location_id = any(?::bigint[])
      and (('SOW' = ? and sow_id is not null)
           or ('GILT' = ? and gilt_id is not null)
           or ('BOAR' = ? and boar_id is not null))
    group by d.seq, d.datefrom, d.dateto
    order by seq
 * </pre>
 */
@Deprecated(since = "AI Redo to scala")
public class LongTermBoughtIndividualAnimalsKpisDataSource extends AbstractKpiDataSource implements KpiDataSourceRegistrar {
    private static final String ANIMALS_AMOUNT = "_LT_BOUGHT_IND_ANIMALS_AMOUNT";
    private static final String ANIMALS_WEIGHT = "_LT_BOUGHT_IND_ANIMALS_WEIGHT";

    private final Field<Long> F_AMOUNT;
    private final Field<BigDecimal> F_WEIGHT;
    private final Optional<String> inhabitantTypeCode;
    private final Optional<String> progenyTypeCode;
    private final String amountKpiSourceId;
    private final String weightKpiSourceId;

    public LongTermBoughtIndividualAnimalsKpisDataSource(Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, ApplicationUser user, Connection conn) {
        this(null, locations, periods, null, Optional.empty(), Optional.empty(), user, conn);
    }

    public LongTermBoughtIndividualAnimalsKpisDataSource(Map<Long, String> organizationsWithTimeZone, Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, ApplicationUser user, Connection conn) {
        this(organizationsWithTimeZone, null, periods, null, Optional.empty(), Optional.empty(), user, conn);
    }

    public LongTermBoughtIndividualAnimalsKpisDataSource(Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, String inhabitantTypeCode, ApplicationUser user, Connection conn) {
        this(null, locations, periods, null, Optional.of(inhabitantTypeCode), Optional.empty(), user, conn);
    }

    public LongTermBoughtIndividualAnimalsKpisDataSource(Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, String inhabitantTypeCode, String progenyTypeCode, ApplicationUser user, Connection conn) {
        this(null, locations, periods, null, Optional.of(inhabitantTypeCode), Optional.ofNullable(progenyTypeCode), user, conn);
    }

    public LongTermBoughtIndividualAnimalsKpisDataSource(Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, Set<String> breeds, String inhabitantTypeCode, ApplicationUser user, Connection conn) {
        this(null, locations, periods, breeds, Optional.of(inhabitantTypeCode), Optional.empty(), user, conn);
    }

    public LongTermBoughtIndividualAnimalsKpisDataSource(Map<Long, String> organizationsWithTimeZone, Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, String inhabitantTypeCode, ApplicationUser user, Connection conn){
        this(organizationsWithTimeZone, null, periods, null, Optional.of(inhabitantTypeCode), Optional.empty(), user, conn);
    }

    public LongTermBoughtIndividualAnimalsKpisDataSource(Map<Long, String> organizationsWithTimeZone, Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, String inhabitantTypeCode, String progenyTypeCode, ApplicationUser user, Connection conn) {
        this(organizationsWithTimeZone, null, periods, null, Optional.of(inhabitantTypeCode), Optional.ofNullable(progenyTypeCode), user, conn);
    }

    public LongTermBoughtIndividualAnimalsKpisDataSource(Map<Long, String> organizationsWithTimeZone, Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, Set<String> breeds, String inhabitantTypeCode, ApplicationUser user, Connection conn) {
        this(organizationsWithTimeZone, null, periods, breeds, Optional.of(inhabitantTypeCode), Optional.empty(), user, conn);
    }

    private LongTermBoughtIndividualAnimalsKpisDataSource(Map<Long, String> organizationsWithTimeZone, Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, Set<String> breeds, Optional<String> inhabitantTypeCode, Optional<String> progenyTypeCode, ApplicationUser user, Connection conn) {
        super(organizationsWithTimeZone, locations, periods, breeds, conn, user);
        this.inhabitantTypeCode = inhabitantTypeCode;
        this.amountKpiSourceId = getKpiSourceId(inhabitantTypeCode, ANIMALS_AMOUNT);
        this.weightKpiSourceId = getKpiSourceId(inhabitantTypeCode, ANIMALS_WEIGHT);
        this.F_AMOUNT = kpiField(amountKpiSourceId, Long.class);
        this.F_WEIGHT = kpiField(weightKpiSourceId, BigDecimal.class);
        this.progenyTypeCode = progenyTypeCode;
    }

    @Override
    public void registerKpiDataSources(KpiDataSourceRegistry registry) {
        registry.addKpiDataSource(amountKpiSourceId, getAmountDataSource(), KpiDescription.UnusedOldDataSource());
        registry.addKpiDataSource(weightKpiSourceId, getWeightDataSource(), KpiDescription.UnusedOldDataSource());
    }

    public KpiDataSource getAmountDataSource() {
        return (orgId, periodSeqNum) -> getValue(orgId, periodSeqNum, F_AMOUNT);
    }

    public KpiDataSource getWeightDataSource() {
        return (orgId, periodSeqNum) -> getValue(orgId, periodSeqNum, F_WEIGHT);
    }

    @Override
    protected Select getOrgQuery(Long orgId, String orgTimeZone, String orgSchema, CommonTableExpression<Record3<Long, Date, Date>> dates) {
        return with(dates)
                .select(
                        inline(orgId).as(ORG_ID),
                        dates.field(SEQ),
                        sum(inline(1)).as(F_AMOUNT),
                        sum(TRANSFERINDIVIDUALIN.PIGLIVEWEIGHT).as(F_WEIGHT)
                )
                .from(orgTable(orgSchema, TRANSFERIN))
                .join(orgTable(orgSchema, TRANSFERINDIVIDUALIN))
                .on(TRANSFERIN.ID.eq(TRANSFERINDIVIDUALIN.TRANSFERIN_ID))
                .join(orgTable(orgSchema, LOCATION))
                .on(LOCATION.ID.eq(TRANSFERINDIVIDUALIN.LOCATION_ID))
                .join(dates)
                .on(
                    TRANSFERIN.ACTOR_DATE.ge(asTimestampAtTimeZone(minusDays(dates.field(TO_DATE), inline(365)), orgTimeZone))
                        .and(TRANSFERIN.ACTOR_DATE.lt(asTimestampAtTimeZoneNextDay(dates.field(TO_DATE), orgTimeZone)))
                )
                .leftOuterJoin(orgTable(orgSchema, SOW))
                .on(SOW.ID.eq(TRANSFERINDIVIDUALIN.SOW_ID))
                .where(
                        (useLocations() ? TRANSFERINDIVIDUALIN.LOCATION_ID.in(getLocations()) : trueCondition())
                        .and(progenyTypeCode.map(ptc -> LOCATION.PROGENY_TYPE_CODE.eq(ptc)).orElse(trueCondition()))
                        .and(inhabitantTypeCode.map(itc -> (isUsingBreeds() && InhabitantType.isSow(itc) ? SOW.BREED.in(getBreeds()) : trueCondition())
                            .and(
                                value(itc).eq(inline(InhabitantType.SOW)).and(TRANSFERINDIVIDUALIN.SOW_ID.isNotNull())
                                .or(value(itc).eq(inline(InhabitantType.GILT)).and(TRANSFERINDIVIDUALIN.GILT_ID.isNotNull()).and(LOCATION.INHABITANTTYPE_CODE.notIn(inline(InhabitantType.WEANER), inline(InhabitantType.FATTENER))))
                                .or(value(itc).eq(inline(InhabitantType.BOAR)).and(TRANSFERINDIVIDUALIN.BOAR_ID.isNotNull()))
                                .or(value(itc).eq(inline(InhabitantType.WEANER)).and(LOCATION.INHABITANTTYPE_CODE.eq(inline(InhabitantType.WEANER)).and(TRANSFERINDIVIDUALIN.GILT_ID.isNotNull())))
                                .or(value(itc).eq(inline(InhabitantType.FATTENER)).and(LOCATION.INHABITANTTYPE_CODE.eq(inline(InhabitantType.FATTENER)).and(TRANSFERINDIVIDUALIN.GILT_ID.isNotNull())))
                            )
                        ).orElse(TRANSFERINDIVIDUALIN.GILT_ID.isNotNull()))
                )
                .groupBy(dates.field(SEQ));
    }

    @Override
    protected Field[] getAggregatedFields() {
        return new Field[] {F_AMOUNT, F_WEIGHT};
    }
}
