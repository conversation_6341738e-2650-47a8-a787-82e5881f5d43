package reports.kpi.calculators.data.individual.gilt;

import com.google.common.collect.ImmutableList;
import models.BreedingAnimal;
import models.handlers.SettingsHandler;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.util.postgres.PostgresDataType;
import play.libs.F;
import reactapp.shared.kpi.KpiDescription;
import reports.kpi.KpiUtilsDI;
import reports.kpi.calculators.data.AbstractKpiDataSource;
import reports.kpi.calculators.data.KpiDataSourceRegistrar;
import reports.kpi.calculators.data.KpiDataSourceRegistry;
import security.ApplicationUser;
import utils.jooq.CloudfarmsDSL;

import java.sql.Connection;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static jooq.farm.Tables.*;
import static jooq.farm.tables.Hop.HOP;
import static org.jooq.impl.DSL.*;
import static org.jooq.util.postgres.PostgresDataType.TIMESTAMPTZ;
import static utils.jooq.CloudfarmsDSL.*;

/**
 * Created by Gabriel on 23.10.2014.
 */
@Deprecated(since = "AI Redo to scala")
public class BAFeedingDaysKpiDataSource extends AbstractKpiDataSource implements KpiDataSourceRegistrar {
    private static final String BA_MALE_FEEDING_DAYS = "BA_MALE_FEEDING_DAYS";
    private static final String BA_FEMALE_FEEDING_DAYS = "BA_FEMALE_FEEDING_DAYS";
    private static final Field<Long> F_FEMALE_FEEDING_DAYS = kpiField(BA_FEMALE_FEEDING_DAYS, Long.class);
    private static final Field<Long> F_MALE_FEEDING_DAYS = kpiField(BA_MALE_FEEDING_DAYS, Long.class);

    private List<F.Tuple3<String, String, Field<Long>>> F_MALE_FEEDING_DAYS_BY_BREED;
    private List<F.Tuple3<String, String, Field<Long>>> F_FEMALE_FEEDING_DAYS_BY_BREED;

    private static final Field<Timestamp> F_HOP_FROM_DATETIME = field("_hop_from_datetime", Timestamp.class);
    private static final Field<Timestamp> F_HOP_TO_DATETIME = field("_hop_to_datetime", Timestamp.class);
    private static final Field<Date> F_HOP_FROM_DATE = field("_hop_from_date", Date.class);
    private static final Field<Date> F_HOP_TO_DATE = field("_hop_to_date", Date.class);
    private static final Field<Date> F_EXIT_DATE = field("_exit_date", Date.class);
    private static final Field<Timestamp> F_EXIT_DATETIME = field("_exit_datetime", Timestamp.class);
    private static final Field<Date> F_ENTRY_DATE = field("_entry_date", Date.class);
    private static final Field<Timestamp> F_ENTRY_DATETIME = field("_entry_datetime", Timestamp.class);
    private static final Field<Long> F_LOCATION_ID = field("_location_id", Long.class);
    private static final Field<Long> F_GILT_ID = field("_gilt_id", Long.class);
    private static final Field<Long> F_HOP_ID = field("_hop_id", Long.class);
    private static final Field<Long> F_DEAD_ID = field("_dead_id", Long.class);
    private static final Field<String> F_SEX = field("_sex", String.class);

    private static final Field<String> F_BREED = field("_breed", String.class);

    private final SettingsHandler settingsHandler;

    public BAFeedingDaysKpiDataSource(Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, SettingsHandler settingsHandler, ApplicationUser user, Connection conn) {
        super(locations, periods, user, conn);
        this.settingsHandler = settingsHandler;
        initializeBreedKpiFields(user.getFarmId());
    }

    public BAFeedingDaysKpiDataSource(Map<Long, String> organizationsWithTimeZone, Set<Long> locations, List<Pair<LocalDate, LocalDate>> periods, SettingsHandler settingsHandler, ApplicationUser user, Connection conn) {
        super(organizationsWithTimeZone, locations, periods, user, conn);
        this.settingsHandler = settingsHandler;
        initializeBreedKpiFields(organizationsWithTimeZone.keySet().toArray(new Long[organizationsWithTimeZone.size()]));
    }

    private void initializeBreedKpiFields(Long ... organizations) {
        Set<String> breeds  = KpiUtilsDI.breedingAnimalBreeds(conn, organizations);
        F_FEMALE_FEEDING_DAYS_BY_BREED = getKpiFieldsByBreed(breeds, BA_FEMALE_FEEDING_DAYS);
        F_MALE_FEEDING_DAYS_BY_BREED = getKpiFieldsByBreed(breeds, BA_MALE_FEEDING_DAYS);
    }

    private List<F.Tuple3<String, String, Field<Long>>> getKpiFieldsByBreed(Set<String> breeds, String kpiNameBase) {
        return ImmutableList.copyOf(breeds.stream().map(breed -> F.Tuple3(breed, kpiNameBase + "$" + breed, kpiField(kpiNameBase + "$" + breed, Long.class))).iterator());
    }

    @Override
    public void registerKpiDataSources(KpiDataSourceRegistry registry) {
        registry.addKpiDataSource(BA_FEMALE_FEEDING_DAYS, (orgId, periodSeqNum) -> getValue(orgId, periodSeqNum, F_FEMALE_FEEDING_DAYS), KpiDescription.UnusedOldDataSource());
        registry.addKpiDataSource(BA_MALE_FEEDING_DAYS, (orgId, periodSeqNum) -> getValue(orgId, periodSeqNum, F_MALE_FEEDING_DAYS), KpiDescription.UnusedOldDataSource());
        registerKpidDataSourcesByBreed(registry, F_FEMALE_FEEDING_DAYS_BY_BREED);
        registerKpidDataSourcesByBreed(registry, F_MALE_FEEDING_DAYS_BY_BREED);
    }

    private void registerKpidDataSourcesByBreed(KpiDataSourceRegistry registry, List<F.Tuple3<String, String, Field<Long>>> kpiFieldsByBreed) {
        kpiFieldsByBreed.forEach(f ->
            registry.addKpiDataSource(f._2, (orgId, categoryId) -> super.getValue(orgId, categoryId, f._3), KpiDescription.UnusedOldDataSource())
        );
    }

    @Override
    protected Select getOrgQuery(Long orgId, String orgTimeZone, String orgSchema, CommonTableExpression<Record3<Long, Date, Date>> dates) {
        String slaughterFarmNumber = settingsHandler.getFarmnumberSlaughter(user);

        CommonTableExpression<Record2<Date, Date>> cteMinMaxDate = name("min_max_date").as(
                select(
                        min(FROM_DATE).as(FROM_DATE),
                        max(TO_DATE).as(TO_DATE)
                )
                .from(dates)
        );

        CommonTableExpression<Record8<Long, String, String, Date, Timestamp, Date, Timestamp, Long>> cteBreedingAnimals = name("ct_breeding_animals").as(
            select(
                GILT.ID.as(F_GILT_ID),
                GILT.SEX.as(F_SEX),
                upper(coalesce(BREEDEQ.BREED1, GILT.BREED)).as(F_BREED),
                coalesce(asDateAtTimeZoneNotInCondition(TRANSFERIN.ACTOR_DATE, orgTimeZone), GILT.BIRTHDATE).as(F_ENTRY_DATE),
                coalesce(TRANSFERIN.ACTOR_DATE, atTimeZone(cast(GILT.BIRTHDATE, PostgresDataType.TIMESTAMP), orgTimeZone)).as(F_ENTRY_DATETIME),
                coalesce(GILT.FORSLAUGHTER, asDateAtTimeZoneNotInCondition(DEAD.ACTOR_DATE, orgTimeZone)).as(F_EXIT_DATE),
                coalesce(asTimestampAtTimeZone(GILT.FORSLAUGHTER, orgTimeZone), DEAD.ACTOR_DATE).as(F_EXIT_DATETIME),
                coalesce(TRANSFERINDIVIDUALIN.LOCATION_ID, GILT.CREATED_LOCATION_ID, GILT.LOCATION_ID).as(F_LOCATION_ID)
            )
                .from(orgTable(orgId, GILT))
                .leftOuterJoin(orgTable(orgSchema, TRANSFERINDIVIDUALIN))
                .on(GILT.ID.eq(TRANSFERINDIVIDUALIN.GILT_ID))
                .leftOuterJoin(orgTable(orgSchema, TRANSFERIN))
                .on(TRANSFERIN.ID.eq(TRANSFERINDIVIDUALIN.TRANSFERIN_ID))
                .leftOuterJoin(orgTable(orgId, DEAD))
                .on(GILT.ID.eq(DEAD.GILT_ID))
                .leftOuterJoin(orgTable(orgSchema, BREEDEQ)).on(upper(GILT.BREED).eq(upper(BREEDEQ.BREED2)))
                .where(
                    GILT.BIRTHDATE.isNotNull()
                    .and(not(GILT.ANIMALID.startsWith(slaughterFarmNumber)))
                )
        );

        CommonTableExpression<Record8<Long, String, String, Date, Timestamp, Date, Timestamp, Long>> cteActiveBA = name("ct_active_breeding_animals").as(
                select(
                    F_GILT_ID,
                    F_SEX,
                    F_BREED,
                    F_ENTRY_DATE,
                    F_ENTRY_DATETIME,
                    F_EXIT_DATE,
                    F_EXIT_DATETIME,
                    F_LOCATION_ID
                )
                    .from(cteBreedingAnimals)
                    .join(cteMinMaxDate)
                    .on(F_ENTRY_DATETIME.lt(asTimestampAtTimeZoneNextDay(TO_DATE, orgTimeZone))
                        .and(F_EXIT_DATETIME.isNull()
                            .or(F_EXIT_DATETIME.ge(asTimestampAtTimeZoneNextDay(FROM_DATE, orgTimeZone)))
                        )
                    )
        );

        CommonTableExpression<Record12<Long, Long, String, String, Date, Timestamp, Date, Timestamp, Long, Timestamp, Date, Long>> cteFullHop = name("ct_full_hop").as(
            select(
                inline(-1L).as(F_HOP_ID),
                F_GILT_ID,
                F_BREED,
                F_SEX,
                F_ENTRY_DATE,
                F_ENTRY_DATETIME,
                F_EXIT_DATE,
                F_EXIT_DATETIME,
                F_LOCATION_ID,
                F_ENTRY_DATETIME.as(F_HOP_FROM_DATETIME),
                F_ENTRY_DATE.as(F_HOP_FROM_DATE),
                castNull(Long.class).as(F_DEAD_ID)
            )
                .from(cteActiveBA)
                .union(
                    select(
                        HOP.ID.as(F_HOP_ID),
                        HOP.GILT_ID.as(F_GILT_ID),
                        cteActiveBA.field(F_BREED),
                        cteActiveBA.field(F_SEX),
                        cteActiveBA.field(F_ENTRY_DATE),
                        cteActiveBA.field(F_ENTRY_DATETIME),
                        cteActiveBA.field(F_EXIT_DATE),
                        cteActiveBA.field(F_EXIT_DATETIME),
                        HOP.TO_LOCATION_ID.as(F_LOCATION_ID),
                        HOP.ACTOR_DATE.as(F_HOP_FROM_DATETIME),
                        asDateAtTimeZoneNotInCondition(HOP.ACTOR_DATE, orgTimeZone).as(F_HOP_FROM_DATE),
                        HOP.DEAD_ID.as(F_DEAD_ID)
                    )
                        .from(cteActiveBA)
                        .join(orgTable(orgId, HOP))
                        .on(cteActiveBA.field(F_GILT_ID).eq(HOP.GILT_ID))
                )
        );

        WindowDefinition wNextHop = name("w_next_hop").as(
                orderBy(
                    F_GILT_ID,
                    F_HOP_FROM_DATETIME,
                    decode().when(F_DEAD_ID.isNull(), inline(1)).otherwise(inline(2)),
                    F_HOP_ID
                )
        );

        CommonTableExpression<Record13<Long, Long, String, String, Date, Timestamp, Date, Timestamp, Long, Timestamp, Date, Timestamp, Date>> cteHopFromTo = name("ct_hop_from_to").as(
            select(
                F_HOP_ID,
                F_GILT_ID,
                F_BREED,
                F_SEX,
                F_ENTRY_DATE,
                F_ENTRY_DATETIME,
                F_EXIT_DATE,
                F_EXIT_DATETIME,
                F_LOCATION_ID,
                F_HOP_FROM_DATETIME,
                F_HOP_FROM_DATE,
                decode().when(F_GILT_ID.isNotDistinctFrom(lead(F_GILT_ID).over(wNextHop)), lead(F_HOP_FROM_DATETIME, 1, infinity(TIMESTAMPTZ)).over(wNextHop))
                    .when(F_DEAD_ID.isNotNull(), castNull(TIMESTAMPTZ))
                    .otherwise(infinity(TIMESTAMPTZ)).as(F_HOP_TO_DATETIME),
                asDateAtTimeZoneNotInCondition(decode().when(F_GILT_ID.isNotDistinctFrom(lead(F_GILT_ID).over(wNextHop)), lead(F_HOP_FROM_DATETIME, 1, infinity(TIMESTAMPTZ)).over(wNextHop))
                    .when(F_DEAD_ID.isNotNull(), castNull(TIMESTAMPTZ))
                    .otherwise(infinity(TIMESTAMPTZ)), orgTimeZone).as(F_HOP_TO_DATE)
            )
                .from(cteFullHop)
                .window(wNextHop)
        );

        CommonTableExpression<Record13<Long, Long, String, String, Date, Timestamp, Date, Timestamp, Long, Timestamp, Date, Timestamp, Date>> cteDailyHop = name("ct_daily_hop").as(
                select(
                    F_HOP_ID,
                    F_GILT_ID,
                    F_BREED,
                    F_SEX,
                    F_ENTRY_DATE,
                    F_ENTRY_DATETIME,
                    F_EXIT_DATE,
                    F_EXIT_DATETIME,
                    F_LOCATION_ID,
                    F_HOP_FROM_DATETIME,
                    F_HOP_FROM_DATE,
                    F_HOP_TO_DATETIME,
                    CloudfarmsDSL.minusDays(F_HOP_TO_DATE, inline(1)).as(F_HOP_TO_DATE)
                )
                .distinctOn(F_GILT_ID, F_LOCATION_ID, F_HOP_FROM_DATE)
                .from(cteHopFromTo)
                .where(F_HOP_TO_DATE.isNotNull())
                .orderBy(F_GILT_ID.asc(), F_LOCATION_ID.asc(), F_HOP_FROM_DATE.asc(), F_HOP_TO_DATETIME.desc(), F_HOP_ID.desc())
        );

        CommonTableExpression<Record> cteHopInPeriods = name("hop_in_periods").as(
                select(
                    dates.field(SEQ),
                    dates.field(FROM_DATE),
                    dates.field(TO_DATE)
                )
                .select(cteDailyHop.fields())
                .from(dates)
                .join(cteDailyHop)
                .on(
                    cteDailyHop.field(F_ENTRY_DATETIME).lt(asTimestampAtTimeZoneNextDay(dates.field(TO_DATE), orgTimeZone))
                        .and(cteDailyHop.field(F_EXIT_DATETIME).isNull()
                            .or(cteDailyHop.field(F_EXIT_DATETIME).ge(asTimestampAtTimeZoneNextDay(dates.field(FROM_DATE), orgTimeZone)))
                        )
                )
        );

        Field<Integer> feedingDaysInPeriodPerAnimal = dateDiff(least(F_HOP_TO_DATE, TO_DATE), greatest(F_HOP_FROM_DATE, FROM_DATE)).plus(inline(1));
        List<Field<Long>> femalesByBreedFields = F_FEMALE_FEEDING_DAYS_BY_BREED.stream().map(e ->
            sum(decode()
                .when(F_SEX.eq(inline(BreedingAnimal.FEMALE_SEX)).and(upper(F_BREED).eq(e._1.toUpperCase())), feedingDaysInPeriodPerAnimal)
                .otherwise(inline(0))).cast(Long.class).as(e._3)
        ).collect(Collectors.toList());

        List<Field<Long>> malesByBreedFields = F_MALE_FEEDING_DAYS_BY_BREED.stream().map(e ->
            sum(decode()
                .when(F_SEX.eq(inline(BreedingAnimal.MALE_SEX)).and(upper(F_BREED).eq(e._1.toUpperCase())), feedingDaysInPeriodPerAnimal)
                .otherwise(inline(0))).cast(Long.class).as(e._3)
        ).collect(Collectors.toList());


        Condition fromDatetimeBetweenDates = F_HOP_FROM_DATETIME.ge(asTimestampAtTimeZone(FROM_DATE, orgTimeZone))
                .and(F_HOP_FROM_DATETIME.lt(asTimestampAtTimeZoneNextDay(TO_DATE, orgTimeZone)));

        Condition toDatetimeBetweenDates = F_HOP_TO_DATETIME.ge(asTimestampAtTimeZone(FROM_DATE, orgTimeZone))
            .and(F_HOP_TO_DATETIME.lt(asTimestampAtTimeZoneNextDay(TO_DATE, orgTimeZone)));

        Condition wholeIntervalBetweenDates = F_HOP_FROM_DATETIME.lt(asTimestampAtTimeZone(FROM_DATE, orgTimeZone))
            .and(F_HOP_TO_DATETIME.ge(asTimestampAtTimeZoneNextDay(TO_DATE, orgTimeZone)));

        return with(dates)
            .with(cteMinMaxDate)
            .with(cteBreedingAnimals)
            .with(cteActiveBA)
            .with(cteFullHop)
            .with(cteHopFromTo)
            .with(cteDailyHop)
            .with(cteHopInPeriods)
            .select(
                inline(orgId).as(ORG_ID),
                SEQ,
                sum(decode()
                    .when(F_SEX.eq(inline(BreedingAnimal.FEMALE_SEX)), feedingDaysInPeriodPerAnimal)
                    .otherwise(inline(0))
                ).as(F_FEMALE_FEEDING_DAYS),
                sum(decode()
                    .when(F_SEX.eq(inline(BreedingAnimal.MALE_SEX)), feedingDaysInPeriodPerAnimal)
                    .otherwise(inline(0))
                ).as(F_MALE_FEEDING_DAYS)
            )
            .select(femalesByBreedFields)
            .select(malesByBreedFields)
            .from(cteHopInPeriods)
            .where(
                (useLocations() ? F_LOCATION_ID.in(getLocations()) : trueCondition())
                .and(
                    fromDatetimeBetweenDates
                        .or(toDatetimeBetweenDates)
                        .or(wholeIntervalBetweenDates)
                )
            )
            .groupBy(SEQ);
    }

    @Override
    protected Field[] getAggregatedFields() {
        return ImmutableList.<Field<Long>>builder()
            .add(F_FEMALE_FEEDING_DAYS)
            .add(F_MALE_FEEDING_DAYS)
            .addAll(F_FEMALE_FEEDING_DAYS_BY_BREED.stream().map(f -> f._3).iterator())
            .addAll(F_MALE_FEEDING_DAYS_BY_BREED.stream().map(f -> f._3).iterator())
            .build().toArray(new Field[0]);
    }
}
