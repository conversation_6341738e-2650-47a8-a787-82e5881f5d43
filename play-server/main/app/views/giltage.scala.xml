@(customization: Map[String, Array[Integer]],preset:Array[String])<?xml version="1.0" encoding="UTF-8"?>
        @sumUp(widths : scala.collection.immutable.Vector[Int]) = @{
        var sum = 0
        widths.foreach { width => sum += width }
        sum
        }
        @getWidths() = @{
        (customization.size+preset.length) match {
        case 0 => scala.collection.immutable.Vector(405)
        case 1 => scala.collection.immutable.Vector(405)
        case 2 => scala.collection.immutable.Vector(202,202)
        case 3 => scala.collection.immutable.Vector(135,135,135)
        case 4 => scala.collection.immutable.Vector(101,101,101,101)
        case 5 => scala.collection.immutable.Vector(81, 81, 81, 81, 81)
        case 6 => scala.collection.immutable.Vector(67, 67, 67, 67, 67, 67)
        case 7 => scala.collection.immutable.Vector(57, 57, 57, 57, 57, 57, 57)
        case 8 => scala.collection.immutable.Vector(50, 50, 50, 50, 50, 50, 50, 50)
        case 9 => scala.collection.immutable.Vector(45, 45, 45, 45, 45, 45, 45, 45, 45)
        case _ => scala.collection.immutable.Vector(30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30)
        }
        }
        @getXs() = @{
        val widths = getWidths()
        val result = scala.collection.mutable.ArrayBuffer[Int]()
        for (i
<- 0 to widths.length) {
        var sum = 0
        widths.take(i).foreach { width => sum += width }
        result += sum
        }
        result
        }
        @color(index: Int) = @{
        if (index == 0) ""
        else "forecolor=\"#FFFFFF\""
        }
        @hide(index: Int) = @{
        if (index < 2) ""
        else "isRemoveLineWhenBlank=\"true\""
        }

        @defining( (getWidths(), getXs()) ) { case(w, x) =>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd"
              name="testReport" language="groovy" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20"
              topMargin="20" bottomMargin="20" isFloatColumnFooter="true" uuid="53471b36-8e29-4a7e-a48f-4279a71fd88a">
<property name="ireport.zoom" value="1.8150000000000077"/>
<property name="ireport.x" value="0"/>
<property name="ireport.y" value="0"/>
<parameter name="farm_logo" class="java.io.File" isForPrompting="false"/>
    <parameter name="app_logo" class="java.lang.String" isForPrompting="false"/>
    <parameter name="producedby" class="java.lang.String" isForPrompting="false"/>
<parameter name="type" class="java.lang.String"/>
<parameter name="simpleDateFormat" class="java.text.Format"/>
<parameter name="simpleDateTimeFormat" class="java.text.Format"/>
<parameter name="PLAY_LANG" class="play.i18n.Lang"/>
    <parameter name="messagesApi" class="play.i18n.MessagesApi"/>
<queryString>
    <![CDATA[]]>
</queryString>
<field name="breed" class="java.lang.String" />
<field name="parityFrom" class="java.lang.Integer" />
<field name="parityTo" class="java.lang.Integer" />
<field name="farmName" class="java.lang.String"/>
<field name="calculationDate" class="java.util.Date"/>
<field name="dateFrom" class="java.util.Date"/>
<field name="dateTo" class="java.util.Date"/>
<field name="lineId" class="java.lang.String"/>
@for((name,interval)<-customization){
<field name="@name" class="java.math.BigDecimal"/>
}
@for(name<-preset){
<field name="@name" class="java.math.BigDecimal"/>
}
<field name="reportSectionId" class="java.lang.String"/>
<group name="reportSection" keepTogether="true">
    <groupExpression><![CDATA[$F{reportSectionId}]]></groupExpression>
    <groupHeader>
        <band height="34">
            <textField>
                <reportElement x="0" y="0" width="555" height="17" uuid="e6eca9a1-d605-4c7a-850c-bb30a7444a1e"/>
                <box leftPadding="20">
                    <topPen lineWidth="0.0"/>
                    <bottomPen lineWidth="0.0"/>
                </box>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font size="12" isBold="true"/>
                </textElement>
                <textFieldExpression><![CDATA[i18n($P{messagesApi},$P{PLAY_LANG},$F{reportSectionId})]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="0" y="17" width="150" height="17" uuid="cb4ef230-9237-4a6a-8977-b28b0d0fdb09"/>
                <box>
                    <topPen lineWidth="1.0"/>
                    <leftPen lineWidth="1.0"/>
                    <bottomPen lineWidth="1.0" lineStyle="Double"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                <textFieldExpression><![CDATA[i18n($P{messagesApi},$P{PLAY_LANG},'js.label.name')]]></textFieldExpression>
            </textField>
            @for(((name, interval), i)<- customization.zipWithIndex) {

            <staticText>
                <reportElement x="@{150+x(i)}" y="17" width="@w(i)" height="17" uuid="e1341ef2-30df-43d5-9005-13ff00e6d2cb"/>
                <box leftPadding="0">
                    <topPen lineWidth="1.0"/>
                    <leftPen lineWidth="0.25"/>
                    <bottomPen lineWidth="1.0" lineStyle="Double"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                <text><![CDATA[@name]]></text>
            </staticText>
            }
            @for((name, i)<- preset.zipWithIndex) {

            <staticText>
                <reportElement x="@{150+x(customization.size+i)}" y="17" width="@w(customization.size+i)" height="17"
                               uuid="e1341ef2-30df-43d5-9005-13ff00e6d2cb"/>
                <box leftPadding="0">
                    <topPen lineWidth="1.0"/>
                    <leftPen lineWidth="0.25"/>
                    <bottomPen lineWidth="1.0" lineStyle="Double"/>
                    @if(i==preset.length-1){
                    <rightPen lineWidth="1.0"/>
                    }
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                <text><![CDATA[@name]]></text>
            </staticText>
            }

        </band>
    </groupHeader>
    <groupFooter>
        <band height="2">
            <staticText>
                <reportElement x="0" y="0" width="555" height="2" uuid="8c45d60d-e7e6-4254-9493-033bc4bbcdae"/>
                <box>
                    <topPen lineWidth="1.0"/>
                </box>
                <text><![CDATA[Static text]]></text>
            </staticText>
        </band>
    </groupFooter>
</group>
<background>
    <band splitType="Stretch"/>
</background>
<title>
    <band splitType="Stretch"/>
</title>
<pageHeader>
    <band height="155" splitType="Stretch">
        <image vAlign="Top" hAlign="Right" scaleImage="RetainShape" onErrorType="Blank" isLazy="true">
            <reportElement x="453" y="0" width="102" height="32"   uuid="4028abcf-4854-2f80-0148-542f80ad0000"/>
            <imageExpression><![CDATA[$P{farm_logo}]]></imageExpression>
        </image>
        <textField>
            <reportElement x="0" y="18" width="555" height="32" uuid="16482e0f-ca4c-4cef-8680-be3374b8cf78"/>
            <textElement textAlignment="Center" verticalAlignment="Middle">
                <font size="20" isBold="true"/>
            </textElement>
            <textFieldExpression><![CDATA[i18n($P{messagesApi},$P{PLAY_LANG},'js.report.giltage.title')]]></textFieldExpression>
        </textField>
        <textField>
            <reportElement x="0" y="85" width="555" height="15" uuid="0c3a2a4a-f7fb-4edc-806e-73f7c6c82c7a"/>
            <textElement textAlignment="Left">
                <font size="11"/>
            </textElement>
            <textFieldExpression>
                <![CDATA[i18n($P{messagesApi},$P{PLAY_LANG},'periodWithValues', $P{simpleDateFormat}.format($F{dateFrom}), $P{simpleDateFormat}.format($F{dateTo}))]]></textFieldExpression>
        </textField>
        <textField>
            <reportElement x="0" y="100" width="555" height="15" uuid="8ccbd8d5-74f0-4116-890e-52580a4856e7"/>
            <textElement textAlignment="Left">
                <font size="11"/>
            </textElement>
            <textFieldExpression>
                <![CDATA[i18n($P{messagesApi},$P{PLAY_LANG},'computationDateTimeWithValue', $P{simpleDateTimeFormat}.format($F{calculationDate}))]]></textFieldExpression>
        </textField>
        <textField>
            <reportElement x="0" y="115" width="555" height="15" isRemoveLineWhenBlank="true" uuid="8ccbd8d5-74f0-4116-890e-52580a4856e7">
                <printWhenExpression><![CDATA[$F{breed} && $F{breed}.trim()]]></printWhenExpression>
            </reportElement>
            <textElement textAlignment="Left">
                <font size="11"/>
            </textElement>
            <textFieldExpression><![CDATA[i18n($P{messagesApi},$P{PLAY_LANG},'js.label.breed') + ': ' + $F{breed}]]></textFieldExpression>
        </textField>
        <textField>
            <reportElement x="0" y="130" width="555" height="15" isRemoveLineWhenBlank="true" uuid="8ccbd8d5-74f0-4116-890e-52580a4856e7">
                <printWhenExpression><![CDATA[$F{parityFrom} != null]]></printWhenExpression>
            </reportElement>
            <textElement textAlignment="Left">
                <font size="11"/>
            </textElement>
            <textFieldExpression><![CDATA[i18n($P{messagesApi},$P{PLAY_LANG},'js.label.parity') + ': ' + $F{parityFrom} + ($F{parityTo} != null ? ' - ' + $F{parityTo} : '')]]></textFieldExpression>
        </textField>
        <textField>
            <reportElement x="0" y="50" width="555" height="32" uuid="b4b28060-8703-4f51-9db3-e908a644540f"/>
            <textElement textAlignment="Center">
                <font size="14" isBold="true"/>
            </textElement>
            <textFieldExpression><![CDATA["(" + $F{farmName} + ")"]]></textFieldExpression>
        </textField>
    </band>
</pageHeader>
<columnHeader>
    <band splitType="Stretch">
        <printWhenExpression><![CDATA[false]]></printWhenExpression>
    </band>
</columnHeader>
<detail>
    <band height="15" splitType="Stretch">
        <textField>
            <reportElement x="0" y="0" width="150" height="15" uuid="d18aa05a-82be-43da-84d6-0685eb05dc02"/>
            <box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
                <leftPen lineWidth="1.0"/>
                <bottomPen lineWidth="0.25"/>
            </box>
            <textElement verticalAlignment="Middle">
                <font size="8" isBold="false"/>
            </textElement>
            <textFieldExpression><![CDATA[i18n($P{messagesApi},$P{PLAY_LANG},$F{lineId})]]></textFieldExpression>
        </textField>

        @for(((name, interval), i)<- customization.zipWithIndex) {


        <textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
            <reportElement x="@{150+x(i)}" y="0" width="@w(i)" height="15" uuid="3ea33674-**************-bd813721f7ec"/>
            <box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
                <leftPen lineWidth="0.25"/>
                <bottomPen lineWidth="0.25"/>
            </box>
            <textElement textAlignment="Right" verticalAlignment="Middle">
                <font size="8" isBold="false"/>
            </textElement>
            <textFieldExpression><![CDATA[$F{@name}]]></textFieldExpression>
        </textField>

        }
        @for((name, i)<- preset.zipWithIndex) {


        <textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
            <reportElement x="@{150+x(customization.size+i)}" y="0" width="@w(customization.size+i)" height="15"
                           uuid="3ea33674-**************-bd813721f7ec"/>
            <box topPadding="0" leftPadding="3" bottomPadding="0" rightPadding="3">
                <leftPen lineWidth="0.25"/>
                <bottomPen lineWidth="0.25"/>
                @if(i==preset.length-1){
                <rightPen lineWidth="1.0"/>
                }
            </box>
            <textElement textAlignment="Right" verticalAlignment="Middle">
                <font size="8" isBold="false"/>
            </textElement>
            <textFieldExpression><![CDATA[$F{@name}]]></textFieldExpression>
        </textField>

        }

    </band>
</detail>
<columnFooter>
    <band splitType="Stretch">
        <printWhenExpression><![CDATA[false]]></printWhenExpression>
    </band>
</columnFooter>
<pageFooter>
    <band height="28" splitType="Stretch">
        <textField>
            <reportElement x="290" y="8" width="168" height="14" uuid="fc00f312-a8b7-434d-ba50-81ca863159ef"/>
            <box rightPadding="3"/>
            <textElement textAlignment="Right" verticalAlignment="Middle"/>
            <textFieldExpression><![CDATA[$P{producedby}]]></textFieldExpression>
        </textField>
        <image vAlign="Middle" hAlign="Right">
            <reportElement x="460" y="2" width="92" height="26" uuid="a2ae8b6c-36e1-4a55-b018-fb39236bd49a"/>
            <imageExpression><![CDATA[$P{app_logo}]]></imageExpression>
        </image>
    </band>
</pageFooter>
<summary>
    <band splitType="Stretch"/>
</summary>
</jasperReport>
        }
