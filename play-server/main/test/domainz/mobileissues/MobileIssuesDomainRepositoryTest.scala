package domainz.mobileissues

import domainz.time.LocalDateInterval
import org.scalatest.flatspec.AnyFlatSpecLike
import scalatestfw.DbSpec
import util.postgres.PostgresTime

import java.time.LocalDate

class MobileIssuesDomainRepositoryTest extends DbSpec with AnyFlatSpecLike {

  val timeZone = "Europe/Bratislava"
  val allFarmIds: Seq[Long] = Seq(3, 5, 11)
  def noFarmIssues(farmId: Long): FarmMobileIssues = FarmMobileIssues(farmId, 0, 0, 0, 0)
  val noFarmDetailIssues: Seq[DetailedFarmMobileIssues] = Seq.empty

  val allFarmsIssues: Seq[FarmMobileIssues] = Seq(
    FarmMobileIssues(3, 0, 6, 0, 1),
    noFarmIssues(5),
    noFarmIssues(11),
  )

  val allFarmsDetailIssues: Map[Long, Seq[DetailedFarmMobileIssues]] = Map(
    3L -> Seq(
      DetailedFarmMobileIssues(LocalDate.of(2013, 10, 14), 0, 1, 0, 0),
      DetailedFarmMobileIssues(LocalDate.of(2013, 9, 23), 0, 1, 0, 0),
      DetailedFarmMobileIssues(LocalDate.of(2014, 5, 5), 0, 1, 0, 0),
      DetailedFarmMobileIssues(LocalDate.of(2013, 9, 22), 0, 1, 0, 0),
      DetailedFarmMobileIssues(LocalDate.of(2014, 5, 22), 0, 1, 0, 0),
      DetailedFarmMobileIssues(LocalDate.of(2014, 1, 22), 0, 1, 0, 0),
      DetailedFarmMobileIssues(LocalDate.of(2013, 8, 14), 0, 0, 0, 1),
    ),
    5L -> noFarmDetailIssues,
    11L -> noFarmDetailIssues,
  )

  class MyTestData extends DbTestContext {
    val repo = new MobileIssuesDomainRepository()
    val MaxPeriod: LocalDateInterval = LocalDateInterval(PostgresTime.MinLocalDate, PostgresTime.MaxLocalDate)
    val SomeDay: LocalDate = LocalDate.of(2000, 1, 1)
    val SomeDayPeriod: LocalDateInterval = LocalDateInterval(SomeDay, SomeDay)
  }

  ////////////////////////////////////
  ///// listFarmsMobileIssues method
  ////////////////////////////////////

  behavior of "MobileIssuesDomainRepositoryTest.listFarmsMobileIssues()"

  it should "return empty with no farms defined for max period" in new MyTestData {
    repo.listFarmsMobileIssues(Seq.empty, MaxPeriod) shouldBe Seq.empty
  }

  allFarmIds.foreach { farmId =>
    val result = allFarmsIssues.filter(_.farmId == farmId)
    val resultText = {
      if (result.head.isEmpty) {
        "empty"
      } else {
        " some"
      }
    }

    it should s"return $resultText data with single farm [farmId: $farmId]" in new MyTestData {
      repo.listFarmsMobileIssues(Seq(farmId), MaxPeriod) shouldBe result
    }
  }

  it should "return empty for all farms except farm [farmId: 3] for max period" in new MyTestData {
    repo.listFarmsMobileIssues(allFarmIds, MaxPeriod).toSet shouldBe allFarmsIssues.toSet // is order important? then remove 'toSet'
  }

  it should "return empty for all farms for this 1 day period" in new MyTestData {
    repo.listFarmsMobileIssues(allFarmIds, SomeDayPeriod).toSet shouldBe allFarmIds.map(noFarmIssues).toSet // is order important? then remove 'toSet'
  }

  ////////////////////////////////////
  ///// detailedFarmMobileIssues method
  ////////////////////////////////////

  behavior of "MobileIssuesDomainRepositoryTest.detailedFarmMobileIssues()"

  allFarmIds.foreach { farmId =>
    it should s"return empty details for farm [farmId: $farmId] for this 1 day period" in new MyTestData {
      repo.detailedFarmMobileIssues(farmId, SomeDayPeriod) shouldBe noFarmDetailIssues
    }
  }

  allFarmIds.foreach { farmId =>
    val result = allFarmsDetailIssues(farmId)
    val resultText = {
      if (result.isEmpty) {
        "empty"
      } else {
        " some"
      }
    }
    it should s"return $resultText details data for farm [farmId: $farmId]" in new MyTestData {
      repo.detailedFarmMobileIssues(farmId, MaxPeriod) shouldBe result
    }
  }
}
