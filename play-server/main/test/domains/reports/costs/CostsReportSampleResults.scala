package domains.reports.costs

import reactapp.shared.reports.costs.CostsReportGrid
import reactapp.shared.reports.costs.CostsReportGrid._
import sjsgrid.shared.common.ModelType
import sjsgrid.shared.grid.dto.RowModel

object CostsReportSampleResults {

  def costRow(pairs: (CostsReportGrid with ModelType[Any], Any)*): RowModel[CostsReportGrid] =
    RowModel(pairs.toMap, CostsReportGrid)

  val allAnimalsServerRows = List(
    costRow(
      ThePrice -> BigDecimal("120407339.84"),
      PricePerAnimal -> BigDecimal("595.0419807166754797357067669544504"),
      Name -> "Selling",
      NumberOfAnimals -> 202351,
      Amount -> BigDecimal("13075009.73000"),
      PricePerUnit -> BigDecimal("9.208967513326661210828789188212711"),
    ),
    costRow(
      ThePrice -> BigDecimal("72758624.73"),
      PricePerAnimal -> BigDecimal("750.5454320669273062997080698569233"),
      Name -> "  | Finishers",
      NumberOfAnimals -> 96941,
      Amount -> BigDecimal("10513895.23000"),
      PricePerUnit -> BigDecimal("6.920234902321734472904767570144410"),
      Unit -> "kg",
    ),
    costRow(Name -> "  | Boars", Unit -> "kg", NumberOfAnimals -> 14),
    costRow(Name -> "  | Ins. gilts", Unit -> "kg", NumberOfAnimals -> 84),
    costRow(Name -> "  | Sows", Unit -> "kg", Amount -> BigDecimal("5540.00000"), NumberOfAnimals -> 1634),
    costRow(Name -> "  | Piglets", Unit -> "kg", NumberOfAnimals -> 2),
    costRow(
      ThePrice -> BigDecimal("47642504.67"),
      PricePerAnimal -> BigDecimal("460.4341680438375228320431416891363"),
      Name -> "  | Weaners",
      NumberOfAnimals -> 103473,
      Amount -> BigDecimal("2554774.50000"),
      PricePerUnit -> BigDecimal("18.64841874302409077591779626734179"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("6210.44"),
      PricePerAnimal -> BigDecimal("30.59330049261083743842364532019704"),
      Name -> "  | Young breeding animals",
      NumberOfAnimals -> 203,
      Amount -> BigDecimal("800.00000"),
      PricePerUnit -> BigDecimal("7.76305"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-19407931.98"),
      PricePerAnimal -> BigDecimal("-351.0778021381668204265480002170728"),
      Name -> "Buying",
      NumberOfAnimals -> 55281,
      Amount -> BigDecimal("1297308.00000"),
      PricePerUnit -> BigDecimal("-14.96015747995079040597915067200696"),
    ),
    costRow(
      ThePrice -> BigDecimal("-19405376.98"),
      PricePerAnimal -> BigDecimal("-379.5524278756821249046492068769926"),
      Name -> "  | Finishers",
      NumberOfAnimals -> 51127,
      Amount -> BigDecimal("1263408.00000"),
      PricePerUnit -> BigDecimal("-15.35954891848080746678824259463293"),
      Unit -> "kg",
    ),
    costRow(Name -> "  | Boars", Unit -> "kg", NumberOfAnimals -> 14),
    costRow(
      ThePrice -> BigDecimal("-400.00"),
      PricePerAnimal -> BigDecimal("-0.50"),
      Name -> "  | Weaners",
      NumberOfAnimals -> 800,
      Amount -> BigDecimal("17100.00000"),
      PricePerUnit -> BigDecimal("-0.02339181286549707602339181286549708"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-2155.00"),
      PricePerAnimal -> BigDecimal("-0.6452095808383233532934131736526946"),
      Name -> "  | Young breeding animals",
      NumberOfAnimals -> 3340,
      Amount -> BigDecimal("16800.00000"),
      PricePerUnit -> BigDecimal("-0.1282738095238095238095238095238095"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("12560944.850"),
      PricePerAnimal -> BigDecimal("276.8557383733744765263389905223716"),
      Name -> "Transfer out",
      NumberOfAnimals -> 45370,
      Amount -> BigDecimal("1391512.90000"),
      PricePerUnit -> BigDecimal("9.026826017926244162019626264334308"),
    ),
    costRow(Name -> "  | Finishers", Unit -> "kg", Amount -> BigDecimal("72.00000"), NumberOfAnimals -> 4),
    costRow(
      ThePrice -> BigDecimal("12560944.850"),
      PricePerAnimal -> BigDecimal("276.8923562736972048320253945860153"),
      Name -> "  | Weaners",
      NumberOfAnimals -> 45364,
      Amount -> BigDecimal("1391440.90000"),
      PricePerUnit -> BigDecimal("9.027293110329012177232967638079346"),
      Unit -> "kg",
    ),
    costRow(Name -> "  | Young breeding animals", Unit -> "kg", NumberOfAnimals -> 2),
    costRow(
      ThePrice -> BigDecimal("-12560944.850"),
      PricePerAnimal -> BigDecimal("-276.8557383733744765263389905223716"),
      Name -> "Transfer in",
      NumberOfAnimals -> 45370,
      Amount -> BigDecimal("1391512.90000"),
      PricePerUnit -> BigDecimal("-9.026826017926244162019626264334308"),
    ),
    costRow(
      ThePrice -> BigDecimal("-12560944.850"),
      PricePerAnimal -> BigDecimal("-276.8923562736972048320253945860153"),
      Name -> "  | Finishers",
      NumberOfAnimals -> 45364,
      Amount -> BigDecimal("1391440.90000"),
      PricePerUnit -> BigDecimal("-9.027293110329012177232967638079346"),
      Unit -> "kg",
    ),
    costRow(Name -> "  | Boars", Unit -> "kg", NumberOfAnimals -> 2),
    costRow(Name -> "  | Weaners", Unit -> "kg", Amount -> BigDecimal("72.00000"), NumberOfAnimals -> 4),
    costRow(
      ThePrice -> BigDecimal("-53148972.1513000"),
      PricePerAnimal -> BigDecimal("0"),
      Name -> "Feed",
      NumberOfAnimals -> 0,
      Amount -> BigDecimal("31318056.99000000000000000000"),
      PricePerUnit -> BigDecimal("-1.697071187023853742594521027468122"),
    ),
    costRow(
      ThePrice -> BigDecimal("-2274.2400000"),
      Name -> "  | +oepiller",
      Amount -> BigDecimal("1236.0000000000000000"),
      PricePerUnit -> BigDecimal("-1.84"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-25300.0000000"),
      Name -> "  | 70-110kg",
      Amount -> BigDecimal("23000.0000000000000000"),
      PricePerUnit -> BigDecimal("-1.1"),
      Unit -> "kg",
    ),
    costRow(Name -> "  | 80-130 kg", Unit -> "kg", Amount -> BigDecimal("1500.0000000000000000")),
    costRow(
      ThePrice -> BigDecimal("-34473.8000000"),
      Name -> "  | Alfasoya",
      Amount -> BigDecimal("7108.0000000000000000"),
      PricePerUnit -> BigDecimal("-4.85"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-613156.4700000"),
      Name -> "  | Alhfasoya",
      Amount -> BigDecimal("140485.0000000000000000"),
      PricePerUnit -> BigDecimal("-4.364568957539950884436060789408122"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-13029.5000000"),
      Name -> "  | B2B complete",
      Amount -> BigDecimal("1133.0000000000000000"),
      PricePerUnit -> BigDecimal("-11.5"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-2520.0000000"),
      Name -> "  | Baby mix",
      Amount -> BigDecimal("720.0000000000000000"),
      PricePerUnit -> BigDecimal("-3.5"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-580.0000000"),
      Name -> "  | Babymix",
      Amount -> BigDecimal("145.0000000000000000"),
      PricePerUnit -> BigDecimal("-4"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-326577.8080000"),
      Name -> "  | Bi-lactal",
      Amount -> BigDecimal("17371.1600000000000000"),
      PricePerUnit -> BigDecimal("-18.8"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-6000.0000000"),
      Name -> "  | BLK",
      Amount -> BigDecimal("4000.0000000000000000"),
      PricePerUnit -> BigDecimal("-1.5"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-6375749.3800000"),
      Name -> "  | Byg",
      Amount -> BigDecimal("6451182.0000000000000000"),
      PricePerUnit -> BigDecimal("-0.9883071629354124561979494610445032"),
      Unit -> "kg",
    ),
    costRow(Name -> "  | Ck 4", Unit -> "kg", Amount -> BigDecimal("15000.0000000000000000")),
    costRow(
      ThePrice -> BigDecimal("-22633.5000000"),
      Name -> "  | Danmil",
      Amount -> BigDecimal("223475.0000000000000000"),
      PricePerUnit -> BigDecimal("-0.1012797852108736995189618525562143"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-294663.9730000"),
      Name -> "  | Danmil supr.",
      Amount -> BigDecimal("25435.5800000000000000"),
      PricePerUnit -> BigDecimal("-11.58471609454158308951476632339424"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-41952.0000000"),
      Name -> "  | Danmilk Gain",
      Amount -> BigDecimal("3800.0000000000000000"),
      PricePerUnit -> BigDecimal("-11.04"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-1303043.8500000"),
      Name -> "  | Die. Miner",
      Amount -> BigDecimal("192111.0000000000000000"),
      PricePerUnit -> BigDecimal("-6.782765432484344988053781407623718"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-21165.3000000"),
      Name -> "  | DIegivning",
      Amount -> BigDecimal("4355.0000000000000000"),
      PricePerUnit -> BigDecimal("-4.86"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-101172.3700000"),
      Name -> "  | DR. Minera",
      Amount -> BigDecimal("28357.0000000000000000"),
      PricePerUnit -> BigDecimal("-3.567809359241104489191381316782452"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-163896.9400000"),
      Name -> "  | Dræ.min",
      Amount -> BigDecimal("22256.0000000000000000"),
      PricePerUnit -> BigDecimal("-7.364168763479511143062544931703810"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-17743.5800000"),
      Name -> "  | Drægtig",
      Amount -> BigDecimal("4538.0000000000000000"),
      PricePerUnit -> BigDecimal("-3.91"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-205770.9537000"),
      Name -> "  | Ferkelin",
      Amount -> BigDecimal("21412.1700000000000000"),
      PricePerUnit -> BigDecimal("-9.61"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-1103685.5600000"),
      Name -> "  | Fiskemel",
      Amount -> BigDecimal("96388.0000000000000000"),
      PricePerUnit -> BigDecimal("-11.45044569863468481553720380130307"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-89641.8000000"),
      Name -> "  | Havre",
      Amount -> BigDecimal("99602.0000000000000000"),
      PricePerUnit -> BigDecimal("-0.9"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-326466.0000000"),
      Name -> "  | Hestebønne",
      Amount -> BigDecimal("233190.000000000000"),
      PricePerUnit -> BigDecimal("-1.4"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-1267951.2600000"),
      Name -> "  | HP 200",
      Amount -> BigDecimal("205964.0000000000000000"),
      PricePerUnit -> BigDecimal("-6.156179040997455866073682779514867"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-11427775.8800000"),
      Name -> "  | hvede",
      Amount -> BigDecimal("10724792.1000000000000000"),
      PricePerUnit -> BigDecimal("-1.065547543807399306136666276262828"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-77901.6000000"),
      Name -> "  | Hvede klim",
      Amount -> BigDecimal("74192.000000000000"),
      PricePerUnit -> BigDecimal("-1.05"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-5137578.9500000"),
      Name -> "  | Kornbl.",
      Amount -> BigDecimal("5101182.000000000000"),
      PricePerUnit -> BigDecimal("-1.007135003220822154551631367004745"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-385641.4800000"),
      Name -> "  | Kornblanding",
      Amount -> BigDecimal("289956.000000000000"),
      PricePerUnit -> BigDecimal("-1.33"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-2116023.5600000"),
      Name -> "  | Mælk",
      Amount -> BigDecimal("229563.0000000000000000"),
      PricePerUnit -> BigDecimal("-9.217615905002112709800795424349743"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-37759.6500000"),
      Name -> "  | Nuklospray",
      Amount -> BigDecimal("2055.0000000000000000"),
      PricePerUnit -> BigDecimal("-18.37452554744525547445255474452555"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-178992.0000000"),
      Name -> "  | Nutramilk",
      Amount -> BigDecimal("13200.0000000000000000"),
      PricePerUnit -> BigDecimal("-13.56"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-206998.0800000"),
      Name -> "  | Polte/Slgf",
      Amount -> BigDecimal("162042.0000000000000000"),
      PricePerUnit -> BigDecimal("-1.277434739141704002665975487836487"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-8486.3200000"),
      Name -> "  | Poltefoder",
      Amount -> BigDecimal("5734.0000000000000000"),
      PricePerUnit -> BigDecimal("-1.48"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-9604.5800000"),
      Name -> "  | Premium",
      Amount -> BigDecimal("1709.0000000000000000"),
      PricePerUnit -> BigDecimal("-5.62"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-222253.2894000"),
      Name -> "  | Premium wean",
      Amount -> BigDecimal("42985.4200000000000000"),
      PricePerUnit -> BigDecimal("-5.170434286788404068170091161142546"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-510231.0600000"),
      Name -> "  | roepiller",
      Amount -> BigDecimal("306182.0000000000000000"),
      PricePerUnit -> BigDecimal("-1.666430619696781652742486494960514"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-421566.7200000"),
      Name -> "  | Rug",
      Amount -> BigDecimal("478136.000000000000"),
      PricePerUnit -> BigDecimal("-0.8816878879649304800307862198203022"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-59578.4112000"),
      Name -> "  | Schauma",
      Amount -> BigDecimal("7809.5600000000000000"),
      PricePerUnit -> BigDecimal("-7.628907544086990816384021635021691"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-201628.4900000"),
      Name -> "  | Sl. ungsvi",
      Amount -> BigDecimal("44632.0000000000000000"),
      PricePerUnit -> BigDecimal("-4.517576850690087829360100376411543"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-209043.1400000"),
      Name -> "  | Sl.ungsvin",
      Amount -> BigDecimal("45668.0000000000000000"),
      PricePerUnit -> BigDecimal("-4.577453359026013839011999649645266"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-48973.6600000"),
      Name -> "  | Slagtesvin små",
      Amount -> BigDecimal("11974.000000000000"),
      PricePerUnit -> BigDecimal("-4.09"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-687604.1600000"),
      Name -> "  | Slg",
      Amount -> BigDecimal("152742.0000000000000000"),
      PricePerUnit -> BigDecimal("-4.501735999266737374134160872582525"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-435105.8600000"),
      Name -> "  | Slg min",
      Amount -> BigDecimal("105357.00000000000000000000"),
      PricePerUnit -> BigDecimal("-4.129823931964653511394591721480300"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-46355.4700000"),
      Name -> "  | Sm 30",
      Amount -> BigDecimal("10349.0000000000000000"),
      PricePerUnit -> BigDecimal("-4.479222147067349502367378490675428"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-61395.4600000"),
      Name -> "  | Sm30",
      Amount -> BigDecimal("13631.0000000000000000"),
      PricePerUnit -> BigDecimal("-4.504105348103587411048345682635170"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-1450.7000000"),
      Name -> "  | små 3 mine",
      Amount -> BigDecimal("326.0000000000000000"),
      PricePerUnit -> BigDecimal("-4.45"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-791558.6000000"),
      Name -> "  | Små. 1 Min",
      Amount -> BigDecimal("103229.0000000000000000"),
      PricePerUnit -> BigDecimal("-7.667986709161185325828982165864244"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-384951.8000000"),
      Name -> "  | Små. 2 Min",
      Amount -> BigDecimal("56800.0000000000000000"),
      PricePerUnit -> BigDecimal("-6.777320422535211267605633802816901"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-341.7600000"),
      Name -> "  | Små. 3 Min",
      Amount -> BigDecimal("64.0000000000000000"),
      PricePerUnit -> BigDecimal("-5.34"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-2492447.4800000"),
      Name -> "  | Smågrise K",
      Amount -> BigDecimal("389422.0000000000000000"),
      PricePerUnit -> BigDecimal("-6.400376660794716271807961543004761"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-61015.6800000"),
      Name -> "  | Sotaskrå",
      Amount -> BigDecimal("23112.000000000000"),
      PricePerUnit -> BigDecimal("-2.64"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-4599288.0000000"),
      Name -> "  | soya",
      Amount -> BigDecimal("1796011.4000000000000000"),
      PricePerUnit -> BigDecimal("-2.560834524769720281285519679886219"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-145722.2400000"),
      Name -> "  | Soya klima",
      Amount -> BigDecimal("56440.0000000000000000"),
      PricePerUnit -> BigDecimal("-2.581896527285613040396881644223955"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-831372.4160000"),
      Name -> "  | soya olie",
      Amount -> BigDecimal("139803.6000000000000000"),
      PricePerUnit -> BigDecimal("-5.946716794131195477083565802311242"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-3243432.3700000"),
      Name -> "  | soyaskrå",
      Amount -> BigDecimal("1227062.0000000000000000"),
      PricePerUnit -> BigDecimal("-2.643250601844079598259908627273927"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-4181652.4500000"),
      Name -> "  | Tilskud sl",
      Amount -> BigDecimal("1397882.0000000000000000"),
      PricePerUnit -> BigDecimal("-2.991420198557532037754259658540564"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-1247224.2900000"),
      Name -> "  | Tilskud un",
      Amount -> BigDecimal("388798.0000000000000000"),
      PricePerUnit -> BigDecimal("-3.207897905853425171940184877494226"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-295303.8900000"),
      Name -> "  | Tilskudsfoder",
      Amount -> BigDecimal("90307.000000000000"),
      PricePerUnit -> BigDecimal("-3.27"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("-23264.3700000"),
      Name -> "  | zink",
      Amount -> BigDecimal("1145.0000000000000000"),
      PricePerUnit -> BigDecimal("-20.31822707423580786026200873362445"),
      Unit -> "kg",
    ),
    costRow(
      ThePrice -> BigDecimal("0"),
      PricePerAnimal -> BigDecimal("0"),
      Name -> "Medicine",
      NumberOfAnimals -> 0,
      Amount -> BigDecimal("428911.26"),
      PricePerUnit -> BigDecimal("0e+2"),
    ),
    costRow(Name -> "  | Alamycin Prol. Vet., 200 mg/ml", Unit -> "ml", Amount -> BigDecimal("15515.10")),
    costRow(Name -> "  | Altresyn©", Unit -> "ml", Amount -> BigDecimal("4.00")),
    costRow(Name -> "  | Aquacycline© Vet. 180 mg/ml", Unit -> "ml", Amount -> BigDecimal("27902.70")),
    costRow(Name -> "  | Cevazuril© 50mg/ml oral suspen", Unit -> "ml", Amount -> BigDecimal("1.00")),
    costRow(Name -> "  | Clamoxyl Prol.", Unit -> "ml", Amount -> BigDecimal("817.15")),
    costRow(Name -> "  | Ethacilin© Vet. 300.000 IE", Unit -> "ml", Amount -> BigDecimal("89885.50")),
    costRow(Name -> "  | Linco-Spectin 66,7%", Unit -> "g", Amount -> BigDecimal("1227.00")),
    costRow(Name -> "  | Metacam© 5mg/ml (kv‘g+svin)", Unit -> "ml", Amount -> BigDecimal("46.00")),
    costRow(Name -> "  | Metacam© inj. 20 mg/ml", Unit -> "ml", Amount -> BigDecimal("2593.00")),
    costRow(Name -> "  | Neomay", Unit -> "g", Amount -> BigDecimal("23845.60")),
    costRow(Name -> "  | Norodine inj.", Unit -> "ml", Amount -> BigDecimal("21216.01")),
    costRow(Name -> "  | Noromylin Vet.", Unit -> "ml", Amount -> BigDecimal("4802.00")),
    costRow(Name -> "  | Norostrep Vet", Unit -> "ml", Amount -> BigDecimal("3862.50")),
    costRow(Name -> "  | Oxytocin Vet. Novartis", Unit -> "ml", Amount -> BigDecimal("1645.00")),
    costRow(Name -> "  | Parofor©, opl. pulv. 140 mg/ml", Unit -> "ml", Amount -> BigDecimal("2035.65")),
    costRow(Name -> "  | Piggidox Vet.", Unit -> "g", Amount -> BigDecimal("74397.60")),
    costRow(Name -> "  | Rifen 100mg/ml, injektionsv‘sk", Unit -> "ml", Amount -> BigDecimal("14104.00")),
    costRow(Name -> "  | Streptocillin", Unit -> "ml", Amount -> BigDecimal("1761.25")),
    costRow(Name -> "  | Tilmovet Vet. oral opl›sning 2", Unit -> "ml", Amount -> BigDecimal("26756.60")),
    costRow(Name -> "  | Veticyclin Prolongatum Vet", Unit -> "ml", Amount -> BigDecimal("1230.70")),
    costRow(Name -> "  | Vetmulin inj. 162 mg/ml", Unit -> "ml", Amount -> BigDecimal("3363.00")),
    costRow(Name -> "  | Vetmulin oral opl. 12,5%", Unit -> "ml", Amount -> BigDecimal("111899.90")),
    costRow(Name -> "Total margin contribution", ThePrice -> BigDecimal("47850435.7087000")),
    costRow(Name -> "Margin contribution [%]", ThePrice -> BigDecimal("39.74046413805399456618374868666146")),
    costRow(Name -> "Margin contribution / sow / year", ThePrice -> BigDecimal("7234.345054924438481027245973099237")),
    costRow(Name -> "Margin contribution / 1 pig sold", ThePrice -> BigDecimal("236.4724449530765847463071593419355")),
    costRow(Name -> "Margin contribution / 1kg sold", ThePrice -> BigDecimal("3.659686432118624511340994619665189")),
    costRow(Name -> "Margin contribution / 1kg gain", ThePrice -> BigDecimal("5.629340545069828046766568807516513")),
    costRow(Name -> "Margin contribution / 1 pr.pig", ThePrice -> BigDecimal("212.5906286070497538046138061977577")),
    costRow(Name -> "Margin contribution / capacity unit", ThePrice -> BigDecimal("5134.166921534334763948497854077253")),
    costRow(Name -> "Total costs", ThePrice -> BigDecimal("-85117848.9813000")),
    costRow(Name -> "Costs / sow / year", ThePrice -> BigDecimal("-12868.67884782322478486266808225958")),
    costRow(Name -> "Costs / 1 pig sold", ThePrice -> BigDecimal("-420.6445680095477660105460314008826")),
    costRow(Name -> "Costs / 1kg sold", ThePrice -> BigDecimal("-6.509964484844784891796787978374988")),
    costRow(Name -> "Costs / 1kg gain", ThePrice -> BigDecimal("-10.01364671570678976118888059799678")),
    costRow(Name -> "Costs / 1 pr.pig", ThePrice -> BigDecimal("-378.1628474769453301305475613668019")),
    costRow(Name -> "Costs / capacity unit", ThePrice -> BigDecimal("-9132.816414302575107296137339055794")),
  )

}
