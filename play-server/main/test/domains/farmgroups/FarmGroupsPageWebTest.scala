package domains.farmgroups

import reactapp.shared.farmgroups.{ FarmGroupMembersGrid, FarmGroupsGrid }
import scalatestfw.Row.WarningPolicy.ResolveAll

import java.time.LocalDate

class FarmGroupsPageWebTest extends FarmGroupsPageTestSpec {

  "FarmGroupsPageWebTest" must {

    "not allow user without holding admin access the page" in {
      accessPageExpectFailure(pageLink, user = StyriUser, farmId = Farm2.id)
    }

    "allow user with holding admin role enter the page" in {
      accessPageExpectSuccess(pageLink)
    }

    "check filtering and sorting of farm groups grid" in {
      loginAndWaitAppForLoad()

      mainGrid.waitForSomeRows()

      mainGrid.checkSortings()

      mainGrid.enterFilterValues(
        FarmGroupsGrid.FarmGroupDesc -> "NonExisting",
        FarmGroupsGrid.FarmGroupName -> "NonExisting",
      )
      mainGrid.waitForVisibleRowsCountToBe(1)

      mainGrid.enterFilterValues(
        FarmGroupsGrid.FarmGroupName -> """"FarmGroup1"""",
      )
      mainGrid.waitForVisibleRowsCountToBe(1)

      mainGrid.enterFilterValues(
        FarmGroupsGrid.FarmGroupDesc -> """"FarmGroup1 description."""",
      )
      mainGrid.waitForVisibleRowsCountToBe(2)
    }

    "check filtering and sorting of farm group members grid" in {
      loginAndWaitAppForLoad()

      mainGrid
        .waitForSomeRows()
        .clickFirstVisibleRow()

      detailGrid.waitForSomeRows()

      detailGrid.checkSortings()

      detailGrid.enterFilterValues(
        FarmGroupMembersGrid.FarmName -> """NonExisting""",
        FarmGroupMembersGrid.HoldingName -> """NonExisting""",
      )
      detailGrid.waitForVisibleRowsCountToBe(1)

      detailGrid.enterFilterValues(
        FarmGroupMembersGrid.FarmName -> """"Farm 1"""",
      )
      detailGrid.waitForVisibleRowsCountToBe(1)

      detailGrid.enterFilterValues(
        FarmGroupMembersGrid.HoldingName -> """"Farm 1"""",
      )
      detailGrid.waitForVisibleRowsCountToBe(2)
    }

    "validate exports" in {
      loginAndWaitAppForLoad()
      validateExports(patienceConfigDownload = timeout(20))
    }

    "create farm group definition with member" in {
      loginAndWaitAppForLoad()

      mainGrid
        .waitForSomeRows()
        .addNewRow()
        .enterValues(
          FarmGroupsGrid.FarmGroupName -> """FarmGroupNew""",
          FarmGroupsGrid.FarmGroupDesc -> """FarmGroupNew description""",
        ).saveAndReload()

      mainGrid.waitForSomeRows()

      mainGrid.enterFilterValues(
        FarmGroupsGrid.FarmGroupName -> """FarmGroupNew""",
        FarmGroupsGrid.FarmGroupDesc -> """"FarmGroupNew description"""",
      )
      mainGrid.waitForVisibleRowsCountToBe(2)

      mainGrid.targetVisibleRowByRowIndex(0)

      val localDate1From = LocalDate.now
      val localDate1To = localDate1From.plusDays(1)
      val localDate2From = localDate1To.plusDays(1)
      val localDate2To = localDate2From.plusDays(1)

      val row1 = detailGrid.addNewRow()
      row1.enterValuesSaveAndValidate(
        Seq(
          FarmGroupMembersGrid.FarmName -> """Farm 1""",
          FarmGroupMembersGrid.ValidFrom -> localDate1From,
          FarmGroupMembersGrid.ValidTo -> localDate1To,
        ),
      )

      val row2 = detailGrid.addNewRow(warningPolicy = ResolveAll)
      row2.enterValuesSaveAndValidate(
        Seq(
          FarmGroupMembersGrid.FarmName -> """Farm 1""",
          FarmGroupMembersGrid.ValidFrom -> localDate2From,
          FarmGroupMembersGrid.ValidTo -> localDate2To,
        ),
        saveSuccessfullyOverrideFn = () => {
          clickSaveBtn()
          row2.dealWithWarnings() // client side warnings

          clickSaveBtn()
          row2.dealWithWarnings() // server side warnings
          row2
        },
      )

      detailGrid
        .enterFilterValues(
          FarmGroupMembersGrid.FarmName -> """"Farm 1"""",
          FarmGroupMembersGrid.ValidFrom -> formatLocalDate(localDate1From),
          FarmGroupMembersGrid.ValidTo -> formatLocalDate(localDate2To),
        )

      detailGrid.waitForVisibleRowsCountToBe(1)
    }

    "create farm group member - validation errors when date range overlaps" in {
      loginAndWaitAppForLoad()

      val localDateFrom = LocalDate.now
      val localDateTo = localDateFrom.plusDays(1)

      mainGrid
        .waitForSomeRows()
        .rowByPermanentId(459171)
        .clickMe()

      detailGrid
        .waitForSomeRows()
      val row = detailGrid.addNewRow(warningPolicy = ResolveAll)

      row.enterValuesSaveAndValidate(
        Seq(
          FarmGroupMembersGrid.FarmName -> """Farm 1""",
          FarmGroupMembersGrid.ValidFrom -> localDateFrom,
          FarmGroupMembersGrid.ValidTo -> localDateTo,
        ),
        saveSuccessfullyOverrideFn = () => {
          clickSaveBtn()
          row.dealWithWarnings() // client side warnings

          clickSaveBtn()
          row.dealWithWarnings() // server side warnings
          row
        },
      ).validateHasServerErrors()
    }

    "update existing farm group definition" in {
      loginAndWaitAppForLoad()

      mainGrid
        .waitForSomeRows()
        .rowByPermanentId(459169)
        .enterValues(
          FarmGroupsGrid.FarmGroupName -> """FarmGroup1Updated""",
          FarmGroupsGrid.FarmGroupDesc -> """FarmGroup1Updated description""",
        )
      clickMainGridSaveButton()

      mainGrid.enterFilterValues(
        FarmGroupsGrid.FarmGroupName -> """FarmGroup1Updated""",
        FarmGroupsGrid.FarmGroupDesc -> """"FarmGroup1Updated description"""",
      )

      mainGrid.waitForVisibleRowsCountToBe(2)
    }

    "update existing farm group member" in {
      loginAndWaitAppForLoad()

      mainGrid
        .waitForSomeRows()
        .rowByPermanentId(459169)
        .clickMe()

      val localDateFrom = LocalDate.now
      val localDateTo = localDateFrom.plusDays(1)

      detailGrid
        .waitForSomeRows()
        .rowByPermanentId(459170)
        .enterValues(
          FarmGroupMembersGrid.ValidFrom -> localDateFrom,
          FarmGroupMembersGrid.ValidTo -> localDateTo,
        ).saveSuccessfully()

      detailGrid.enterFilterValues(
        FarmGroupMembersGrid.FarmName -> """Farm 1""",
        FarmGroupMembersGrid.ValidFrom -> formatLocalDate(localDateFrom),
        FarmGroupMembersGrid.ValidTo -> formatLocalDate(localDateTo),
      )

      detailGrid.waitForVisibleRowsCountToBe(2)
    }

    "delete farm group definition" in {
      loginAndWaitAppForLoad()

      val row = mainGrid.waitForSomeRows().clickFirstVisibleRow()

      row.clickMe()
      clickRemoveBtn()
      clickAntJSModalYesBtn()
      row.grid.waitForSomeRows()
      row.validateDoesNotExist()
    }

    "delete farm group member" in {
      loginAndWaitAppForLoad()

      mainGrid.waitForSomeRows().clickFirstVisibleRow().clickMe()
      val row = detailGrid.waitForSomeRows().clickFirstVisibleRow()

      row.clickMe()
      clickSubgridRemoveButton()
      clickAntJSModalYesBtn()
      row.grid.waitForSomeRows()
      row.validateDoesNotExist()
    }

    "verify that farm group with duplicate or empty name cannot be created" in {
      loginAndWaitAppForLoad()

      val row = mainGrid
        .waitForSomeRows()
        .addNewRow()
        .enterValues(
          FarmGroupsGrid.FarmGroupName -> """FarmGroup1""",
          FarmGroupsGrid.FarmGroupDesc -> """FarmGroup1 description""",
        )
      clickMainGridSaveButton()
      row.validateHasServerErrors()
    }

  }

}
