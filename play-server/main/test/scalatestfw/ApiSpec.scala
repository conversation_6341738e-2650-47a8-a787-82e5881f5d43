package scalatestfw

import akka.stream.Materializer
import akka.util.ByteString
import com.cloudfarms.settings.Setting
import com.softwaremill.tagging.Tagger
import domainz.common.ResourceScope
import org.apache.pdfbox.Loader
import org.apache.pdfbox.text.PDFTextStripper
import org.scalatest.matchers.should.Matchers
import play.api.mvc.{ EssentialAction, Result }
import play.api.test.Helpers._
import play.api.test.{ FakeRequest, Helpers }
import reactapp.shared.common.grid.{ GridColumn, GridDefinition }
import reactapp.shared.grid.Field
import security.{ AppRoleScala, FarmRoleScala }
import sjsgrid.shared.grid.column.{ ColumnPositions, ColumnType }
import sjsgrid.shared.grid.dto._
import tenant.{ AccountFarmLangSession, AutowireContext, SessionProvider }

import scala.concurrent.duration.{ Duration, DurationInt }
import scala.concurrent.{ Await, Future }

trait ApiSpec {

  val SECURITY_HASH = "cb106e18de2d859c45906815c3e6da303d5de8ef"
  val requestSessionParameters = Seq(
    "t" -> new java.util.Date().getTime.toString,
    "account" -> "1",
    "hash" -> SECURITY_HASH,
    "farm" -> "3",
  )
  val defaultColumnsState = """{"fas":{"s":[],"f":{}},"pos":[]}"""

  def addRoles(settings: Setting)(rolesToAdd: Set[FarmRoleScala], persona: Persona = TestDbConstants.RazAtFarm1)(implicit
    autowireContext: AutowireContext,
    sp: SessionProvider,
  ): Unit = {
    withTestUtils(settings) { ctx =>
      ctx.testUtils.addRoles(persona.id, rolesToAdd)
    }
  }

  def addAppRoles(settings: Setting)(rolesToAdd: Set[AppRoleScala], persona: Persona = TestDbConstants.RazAtFarm1)(implicit
    autowireContext: AutowireContext,
    sp: SessionProvider,
  ): Unit = {
    withTestUtils(settings) { ctx =>
      ctx.testUtils.addAppRoles(persona.user.id, rolesToAdd)
    }
  }

  def addModules(settings: Setting)(modulesToAdd: Set[AddModuleUnderScope], persona: Persona = TestDbConstants.RazAtFarm1)(implicit
    autowireContext: AutowireContext,
    sp: SessionProvider,
  ): Unit = {
    withTestUtils(settings) { ctx =>
      modulesToAdd.foreach {
        case AddModuleUnderScope(module, ResourceScope.Account) => ctx.testUtils.addAccountModule(persona.user.id, module)
        case AddModuleUnderScope(module, ResourceScope.Farm)    => ctx.testUtils.addFarmModule(persona.farm.id, module)
        case AddModuleUnderScope(module, ResourceScope.Holding) => ctx.testUtils.addHoldingModule(persona.farm.rootId, module)
        case AddModuleUnderScope(module, ResourceScope.System)  => ctx.testUtils.addSystemModule(module)
      }
    }
  }

  case class ApiTestFixtureContext(userSession: AccountFarmLangSession, settings: Setting) {
    lazy val testUtils: TestUtils = new TestUtils(settings)(userSession.dbSession)
  }

  def withTestUtils[R](settings: Setting)(block: ApiTestFixtureContext => R)(implicit
    autowireContext: AutowireContext,
    sp: SessionProvider,
  ): R = withTransaction(block = ctx => block(ApiTestFixtureContext(ctx, settings)))

  def withTransaction[R](block: AccountFarmLangSession => R)(implicit autowireContext: AutowireContext, sp: SessionProvider): R =
    autowireContext.noAuthNeeded("API test").withSession.transaction(block)

  def requestParams[Grid <: enumeratum.EnumEntry](
    sortings: Seq[GridSorting[Grid]] = Seq.empty,
    filters: GridFilters[Grid] = GridFilters[Grid](),
  ): DataGetterParams[Grid] = {
    new DataGetterParams[Grid](
      RowsToGet.AllRows,
      FilteringAndSorting[Grid](sortings, filters),
      Set.empty[Grid].taggedWith[ColumnPositions.HiddenColumns],
    )
  }

  def getGridSortableColumns[Grid <: enumeratum.EnumEntry](
    gridDefinition: GridDefinition[Grid],
    virtualPaging: Boolean = false,
  ): IndexedSeq[Grid] = {
    gridDefinition.values
      .collect {
        case col: Grid with GridColumn with Field.FromDb if virtualPaging => col
        case col: Grid with GridColumn if !virtualPaging                  => col
      }
      .filterNot {
        case _: ColumnType.Password => true
        case _: ColumnType.Mark     => true
        case col                    => !col.isSortable // || !col.hasReadAccess(serverContext)
      }
  }

  def callExport(url: String, action: EssentialAction, method: String = "GET")(implicit mat: Materializer): Result = {
    import Matchers._

    val request = FakeRequest(method, url).withSession(requestSessionParameters: _*)
    val result = exec(Helpers.call(action, request))
    result.header.status should equal(200)
    result
  }

  def readPdfContent(result: Result)(implicit materializer: Materializer): String = {
    val bytes: ByteString = exec(result.body.consumeData)
    val pdf = Loader.loadPDF(bytes.toArray)
    val stripper = new PDFTextStripper()
    stripper.getText(pdf)
  }

  def exec[F](future: Future[F], timeout: Duration = 1.minute): F = Await.result(future, timeout)

  trait ExportFormat
  object ExportFormat {
    case object Pdf extends ExportFormat
    case object Excel extends ExportFormat
    case object Db extends ExportFormat
  }

}
