<div data-cf-modal="modal.deleteRow">
    <div ng-hide="deleteFailed">
        <h2>@Html(Messages("serving.delete.header.question", "{{ servingEvent.serving.sow.sowNumber }}", "{{ servingEvent.actorDate |
            date}}"))</h2>

        <p class="lead">@Html(Messages("serving.delete.explanation.1"))</p>

        <p ng-hide="deleting">
            <a class="button secondary"
               data-ng-click="modal.deleteRow.close()">@Html(Messages("serving.delete.no"))</a>
            <a class="button alert" data-ng-click="deleteActiveRow(true)">@Html(Messages("serving.delete.yes"))</a>
        </p>

        <p ng-show="deleting" class="round alert label">
            <i class="spinning-icon awesome-icon-repeat"></i> @Html(Messages("label.deleting", {{servingEvent.serving.sow.sowNumber}}))
        </p>
    </div>
    <div ng-show="deleteFailed">
        <h2>@Html(Messages("serving.delete.header.failed", {{servingEvent.serving.sow.sowNumber}}))</h2>
        <p>@Html(Messages("serving.delete.failed.1"))</p>
        <p>{{deleteFailureReason}}</p>
        <p>
            <a class="button secondary" data-ng-click="modal.deleteRow.close()">@Html(Messages("label.close"))</a>
        </p>

    </div>
    <a class="close-reveal-modal">&#215;</a>
</div>

<div class="row">
    <div class="small-12 large-12 columns" id="cf-quick-filters">
        <h1 class="cf-location">
            @Html(Messages("js.title.serving"))
        </h1>

        <dl class="sub-nav">
            <dt>@Html(Messages("js.label.servingevents")):</dt>
            <dd data-ng-class="{'active' : !filter.lastservings.value}"><a
                    data-ng-click="filter.lastservings.value = null" href="">@Html(Messages("js.label.all"))</a></dd>
            <dd data-ng-class="{'active' : filter.lastservings.value === 'true'}"><a
                    data-ng-click="filter.lastservings.value = 'true'" href="">@Html(Messages("js.label.first.servingevents"))</a>
            </dd>
        </dl>

        <table class="statistics" style="margin-left: 5px">
            <tr>
                <td><b>@Html(Messages("js.label.sum.served")):</b></td>
                <td ng-bind="statistics.served | number:0"></td>
                <td><b>@Html(Messages("js.label.servings.events.count")):</b></td>
                <td><span ng-show="state.data.activeCell"> {{state.data.activeCell.row + 1 | number:0}} / </span>{{data.data.getLength() | number:0}}</td>
            </tr>
            <tr>
                <td><b>@Html(Messages("js.label.sum.reserved")):</b></td>
                <td ng-bind="statistics.reserved | number:0"></td>
                <td><b>@Html(Messages("js.label.servings.count")):</b></td>
                <td ng-bind="statistics.totalCount | number:0"></td>
            </tr>
            <tr>
                <td><b>@Html(Messages("js.label.sum.firstserved")):</b></td>
                <td ng-bind="statistics.firstServed | number:0"></td>
                <td></td>
            </tr>
        </table>

        <table class="statistics">
            <tr class="bold">
                <td><b>@Html(Messages("js.label.state")):</b></td>
                <td>@Html(Messages("js.label.ok")):</td>
                <td>@Html(Messages("js.label.state.failed")):</td>
                <td>@Html(Messages("js.label.state.reserved")):</td>
                <td>@Html(Messages("js.label.state.died")):</td>

            </tr>
            <tr>
                <td><b>@Html(Messages("js.label.sum")):</b></td>
                <td ng-bind="statistics.stateOK | number:0"></td>
                <!--<td>{{statistics.stateServed | number:0}}</td>-->
                <td ng-bind="statistics.stateFailed | number:0"></td>
                <td ng-bind="statistics.stateReserved | number:0"></td>
                <td ng-bind="statistics.stateDied | number:0"></td>
                <!--<td>{{statistics.stateFarrowing | number:0}}</td>-->
                <!--<td>{{statistics.stateFarrowed | number:0}}</td>-->
                <!--<td>{{statistics.stateNursery | number:0}}</td>-->
                <!--<td>{{statistics.stateWeaned | number:0}}</td>-->
            </tr>
        </table>


    </div>
</div>

<div class="row" id="cf-middle-fill">
    <div class="small-12 large-12 columns with-left-menu with-details" id="cf-content" data-cf-content-autosize>
        <div class="optional-details" data-ng-class="{'opened' : state.data.details}">

            <!-- CONTENT PERMANENT HERE -->

            <div data-sg-slickgrid data-sg-active-cell="state.data.activeCell"
                 data-sg-on-cell-change="onCellChange(row, cell, item)"
                 data-sg-filter="filter" data-sg-data="data" data-sg-columns="servingEventColumns"
                 data-sg-on-add-new-row="onAddNewRow(args)" data-sg-options="options"
                 data-sg-grid-invalidator="invalidator"
                 data-sg-on-sort="onSort(sortCols)"
                 data-sg-validation-styles="validationStyles"
                 class="sg"></div>

            <div class="toggle-details" data-ng-click="state.data.details=!state.data.details">
                <i data-ng-class="{'enclosed-foundicon-left-arrow' : !state.data.details, 'enclosed-foundicon-right-arrow' : state.data.details}"></i>
            </div>
        </div>


        <div class="small-8 large-8 columns details" data-ng-show="state.data.details">
            <!-- FIXME inline style out-->

            <!-- DETAILED CONTENT HERE -->

            <div class="row" style="padding: 10px">
                <div class="section-container tabs" data-section="tabs">
                    <section jq-details-section id="details-section-sowcard" data-ng-click="state.data.settings.active.detailsSection='sowcard'">
                        <p class="title" data-section-title><a href="">@Html(Messages("js.label.sowcard"))</a></p>

                        <div class="content" data-section-content data-ng-controller="EmbeddedSowCardCtrl">
                            <ng-include src="'partials/embeddedsowcard.html'"></ng-include>
                        </div>
                    </section>
                    <section jq-details-section id="details-section-danavl" ng-if=" hasDanavlModule "
                             data-ng-click="state.data.settings.active.detailsSection='danavl'">
                        <p class="title" data-section-title><a href=""> @Html(Messages("js.title.danavl")) </a></p>
                        <div class="content" data-section-content
                             ng-if="servingEvent.serving.sow.animalId && state.data.settings.active.detailsSection==='danavl'"
                             style="height: 90%;overflow: auto">
                            <div cf-danavl-card
                                 data-animal-id="servingEvent.serving.sow.animalId"
                                 data-animal-type="sow"
                                 data-is-danavl-support="isDanavlSupport"
                            />
                        </div>
                    </section>
                </div>
            </div>
        </div>
        <div data-cf-views
             data-cs-grids="grids"
             data-cs-show="state.showViews"
             data-cs-validate-item="validateItem(item)"></div>
        <div data-cf-hover-menu>
            <li><a href="" data-ng-click="refreshGrid()">@Html(Messages("js.label.refresh"))<i
                    class="menu-icon foundicon-refresh"></i></a></li>
            <li class="divider"></li>
            <li><a href="" data-ng-click="state.showViews = !state.showViews">@Html(Messages("js.label.views"))<i class="menu-icon awesome-icon-eye"></i></a></li>
            <li class="divider"></li>
            <li><a href="" data-ng-class="{'disabled':!options.editable}" data-ng-click="clearGrid()">@Html(Messages("js.label.multientry"))<i
                    class="menu-icon foundicon-plus"><sup class="foundicon-plus"></sup></i></a></li>
            <li class="divider"></li>
            <li><a href="" data-ng-click="printOneSowCard()"
                   data-ng-class="{'disabled': !servingEvent}"
                   target="sowcard-pdf">@Html(Messages("js.label.printsowcard"))<i
                    class="menu-icon awesome-icon-print"></i></a></li>
            <li>
                <a href="" data-ng-click="printSowCards()"
                   data-ng-class="{'disabled':data.data.getLength() == 0 || statistics.served > 500}">
                    @Html(Messages("js.label.printsowcards"))<i class="menu-icon"
                                                                data-ng-class="{'spinning-icon':downloadingSowcards,'awesome-icon-print':!downloadingSowcards,'awesome-icon-repeat':downloadingSowcards}">
                    <span data-ng-hide="downloadingSowcards">&#8319;</span></i>
                </a>
            </li>
            <li class="divider"></li>
            <li><cf-export-to-excel/></li>
            <li class="divider"></li>
            <li><a href="" data-ng-click="deleteActiveRow(false)" data-ng-class="{'disabled':hasNoSelectedRow() || !options.editable}">@Html(Messages("label.delete"))<i
                    class="menu-icon awesome-icon-trash-o"></i></a></li>
            <li class="divider"></li>
        </div>
    </div>
</div>
