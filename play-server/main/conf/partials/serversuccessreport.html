<div class="row rows-without-margin">
    <div class="small-12" id="cf-quick-filters">
        <div class="left">
            <h1 class="cf-location">@Html(Messages("js.report.serversuccess.title"))</h1>
        </div>
        <form class="report_head_table left">
            <table>
                <tr data-report-criteria data-report-resource="data" data-prepare-params="prepareParams(criteria)" data-recompute-required="recompute">
                    <td>
                        <label for="weekYear" class="left inline">@Html(Messages("js.label.week"))</label>
                    </td>
                    <td>
                        <label for="weeksNumber" class="left inline">@Html(Messages("js.label.numberOfWeeks"))</label>
                    </td>
                    <td></td>
                </tr>
                <tr data-report-criteria data-report-resource="data" data-prepare-params="prepareParams(criteria)" data-recompute-required="recompute">
                    <td>
                        <input data-report-criterion data-ng-disabled="data.isLoading()" id="weekYear" name="weekYear" type="text" data-ng-model="weekYear"  data-cf-week-picker />
                    </td>
                    <td>
                        <input data-report-criterion data-ng-disabled="data.isLoading()" id="weeksNumber" name="weeksNumber" type="number" data-ng-model="weeksNumber" />
                    </td>
                    <td>
                        <button data-ng-disabled="data.isLoading()" data-ng-click="loadReport()" class="button small left"
                                data-ng-bind="data.isLoading() ? '@Html(Messages("js.label.processing"))' : '@Html(Messages("js.label.show"))'">&nbsp;
                        </button>
                    </td>
                </tr>
            </table>
        </form>
        <div class="row">
            <div class="small-11 columns report_head_row">
                <span data-ng-hide="data.isReportLoaded"><strong>&nbsp;</strong></span>
                <span data-ng-show="data.isReportLoaded">
                    <strong>@Html(Messages("js.label.period")):</strong>
                    {{data.previousParams._periodStartDate | date:dateFormat}} - {{data.previousParams._periodEndDate | date:dateFormat}}
                </span>
            </div>
            <div class="small-1 columns">&nbsp;</div>
        </div>
    </div>
</div>
<div class="row" id="cf-middle-fill">
    <div class="small-12 large-12 columns with-left-menu no-right-padding" id="cf-content" data-cf-content-autosize>
        <div class="small-12 large-12 columns single-column">
            <div data-sg-slickgrid data-sg-active-cell="state.data.activeCell"
                 data-sg-data="data" data-sg-columns="columns"
                 data-sg-options="options"
                 data-sg-grid-invalidator="grid"
                 class="sg"></div>
        </div>
        <div data-cf-views
             data-cs-grids="grids"
             data-cs-show="state.showViews"
             data-cs-validate-item="validateItem(item)"></div>
        <div data-cf-hover-menu>
            <li><a href="" data-ng-click="state.showViews = !state.showViews">@Html(Messages("js.label.views"))<i class="menu-icon awesome-icon-eye"></i></a></li>
            <li><a href="{{!data.isReportLoaded ? '' : 'api/reports/serversuccess-excel?' + getParams(data.previousParams)}}" target="report-xlsx" data-ng-class="{'disabled':!data.isReportLoaded}">@Html(Messages("js.label.exporttoexcel"))<i class="menu-icon awesome-icon-table"></i></a></li>
            <li class="divider"></li>
            <li><a href="{{!data.isReportLoaded ? '' : 'report/serversuccess/pdf?from=' + data.previousParams.periodFrom + '&to=' + data.previousParams.periodTo}}" target="report-pdf" data-ng-class="{'disabled':!data.isReportLoaded}">@Html(Messages("js.label.exporttopdf"))<i class="menu-icon awesome-icon-print"></i></a></li>
            <li class="divider"></li>
        </div>
    </div>
</div>
