<div data-cf-modal="modal.deleteRow">
    <div ng-hide="deleteFailed">
        <h2>@Html(Messages("testteam.delete.header.question", "{{ '' }}", "{{
            farrowEvent.actorDate | date }}"))</h2>

        <p class="lead">@Html(Messages("testteam.delete.explanation.1"))</p>

        <p ng-hide="deleting">
            <a class="button secondary" data-ng-click="modal.deleteRow.close()">@Html(Messages("js.label.delete.no"))</a>
            <a class="button alert" data-ng-click="deleteActiveRow(true)">@Html(Messages("js.label.delete.yes"))</a>
        </p>

        <p ng-show="deleting" class="round alert label">
            <i class="spinning-icon awesome-icon-repeat"></i> @Html(Messages("label.deleting", "testteam"))
        </p>
    </div>
    <div ng-show="deleteFailed">
        <h2>@Html(Messages("testteam.delete.header.failed", {{ '' }}))</h2>
        <!--<p>@Html(Messages("testteam.delete.failed.1"))</p>-->
        <p>{{deleteFailureReason}}</p>

        <p>
            <a class="button secondary" data-ng-click="modal.deleteRow.close()">@Html(Messages("label.close"))</a>
        </p>
    </div>
    <a class="close-reveal-modal">&#215;</a>
</div>

<div class="row">
    <div class="small-12 large-12 columns" id="cf-quick-filters">
        <h1 class="cf-location">
            @Html(Messages("js.title.testteam"))
        </h1>
        <table class="quickfilter" style="float:left">
            <tr>
                <td>@Html(Messages("js.label.animalid"))</td>
                <td><input type="text" data-ng-trim="false" data-ng-model="filter.qf_animalid.value"></td>
            </tr>
        </table>
        <h5 class="cf-location" data-ng-if="isAllUnlocked" style="color:red;float:left">
            @Html(Messages("js.label.testeams.unlocked.warning"))
        </h5>
        <h5 class="cf-location" data-ng-if="!isAllUnlocked" style="float:left">
            @Html(Messages("js.testeam.update_reminder"))(<i class="menu-icon foundicon-flag" style="font-size:smaller"></i>)
        </h5>
        <table class="statistics">
            <tr>
                <td><b>@Html(Messages("js.label.avg.age.days")):</b></td>
                <td>{{statistics.avgAgeDays}}</td>
                <td><b>@Html(Messages("js.label.avg.breed.index")):</b></td>
                <td>{{statistics.avgBreedIndex}}</td>
                <td>@Html(Messages("js.label.count")):</td>
                <td><span ng-show="state.data.activeCell"> {{state.data.activeCell.row + 1 | number:0}} / </span>{{data.data.getLength() | number:0}}</td>
            </tr>
        </table>
    </div>
</div>

<div class="row" id="cf-middle-fill">
    <div class="small-12 large-12 columns with-left-menu with-details" id="cf-content" data-cf-content-autosize>
        <div class="optional-details" data-ng-class="{'opened' : state.data.details}">
            <div data-sg-slickgrid data-sg-active-cell="state.data.activeCell"
                 data-sg-data="data"
                 data-sg-filter="data.filter"
                 data-sg-columns="columns"
                 data-sg-options="options"
                 data-sg-grid-invalidator="invalidator"
                 data-sg-on-cell-change="onCellChange(row, cell, item)"
                 data-sg-on-cell-click="onCellClick(row, cell, grid, action)"
                 data-sg-on-add-new-row="onAddNewRow(args)"
                 data-sg-validation-styles="validationStyles"
                 data-sg-skipping-columns="skippingColumns"
                 data-sg-on-sort="onSort(sortCols)" class="sg">
            </div>
            <div class="toggle-details" data-ng-click="state.data.details=!state.data.details">
                <i data-ng-class="{'enclosed-foundicon-left-arrow' : !state.data.details, 'enclosed-foundicon-right-arrow' : state.data.details}"></i>
            </div>
        </div>
        <div class="small-8 large-8 columns details no-sections" data-ng-show="state.data.details">
            <div class="details-grid-border" style="overflow: auto;">
                <div style="min-height:400px">
                    <table class="quickfilter" style="float: none;">
                        <tr>
                            <td colspan="6" style="text-align: left">
                                <strong>@Html(Messages("js.label.testteam.validation")):</strong>
                                <strong class="error" data-ng-if="!!sendErrorMessage">{{sendErrorMessage}}</strong>
                                <span data-ng-if="!sendErrorMessage">{{M('js.danavl.ok')}}</span>
                            </td>
                            <td colspan="2">
                                <button class="small button" ng-hide="!!sendErrorMessage" ng-class="{disabled:!!sendErrorMessage}"
                                        data-ng-click="tLogs.getPlainLogs(item,dataRight.data,modal.logs)">
                                    @Html(Messages("js.label.viewlogfile"))
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <span data-cf-status-icon data-si-item="newAnimal" ng-click="warningEditorClick($event)" style="font-size: 16px;"></span>
                            </td>
                            <td>@Html(Messages("js.label.actordate"))</td>
                            <td>
                                <input tabindex="1" type="text" id="input_for_new_animal-0" data-ng-keypress="focusInput($event, 1)" data-ng-model="newAnimal.actorDate" data-cf-date-time style="width: calc(100% - 2em); display: inline">
                            </td>
                            <!--<td>@Html(Messages("js.label.animalname"))</td>-->
                            <!--<td><input tabindex="1" type="text" id="input_for_new_animal-1" data-ng-keypress="focusInput($event, 2)" data-ng-model="newAnimal.number"></td>-->
                            <td>@Html(Messages("js.label.animalid"))</td>
                            <td><input tabindex="1" type="text" id="input_for_new_animal-1" data-ng-keypress="focusInput($event, 2)" data-ng-model="newAnimal.animalId"></td>
                            <td>
                                <button tabindex="1" id="input_for_new_animal-2" data-ng-keypress="focusInput($event, 0)" class="small button" style="margin: 4px 0 5px 0;" data-ng-click="addAnimal()"
                                        data-ng-disabled="!options.editable || !item || !newAnimal.actorDate || (!newAnimal.number && ! newAnimal.animalId)">@Html(Messages("js.label.add.breeding.animal"))
                                </button>
                            </td>
                            <td>@Html(Messages("js.label.count")):</td>
                            <td><span data-ng-show="state.data.activeCellRight"> {{state.data.activeCellRight.row + 1 | number:0}} / </span>{{dataRight.data.getLength() | number:0}}</td>

                        </tr>
                    </table>
                    <div data-sg-slickgrid data-sg-active-cell="state.data.activeCellRight"
                         data-sg-on-cell-change="onCellChangeRight(row, cell, item)"
                         data-sg-data="dataRight" data-sg-columns="columnsRight"
                         data-sg-on-add-new-row="onAddNewRowRight(args)" data-sg-options="optionsRight"
                         data-sg-validation-styles="validationStylesRight"
                         data-sg-grid-invalidator="invalidatorRight"
                         data-sg-do-not-auto-resize="true"
                         data-sg-on-sort="onSortRight(sortCols)"
                         class="sg" style="height: auto;">
                    </div>
                </div>
                <button data-cf-delete class="tiny button" style="margin: 4px 0 0 0; padding-bottom: 5px;" data-ng-click="deleteRight.delete(false)" data-cf-options="deleteRight"
                        data-ng-disabled="deleteRight.hasNoSelectedRow() || !optionsRight.editable">@Html(Messages("js.label.deleterow"))
                </button>
                <div  style="margin-top:1em; margin-bottom:1em">
                    <div cf-testeam-extended-info data-cf-testteam-id="{{item.id}}" data-cf-location-id="{{item.locationId}}" data-cf-externalname="{{item.externalname}}"/>
                </div>
            </div>
        </div>

        <div data-cf-views
             data-cs-grids="grids"
             data-cs-show="state.showViews"
             data-cs-validate-item="validateItem(item)"></div>
        <div data-cf-hover-menu>
            <li><a href="" data-ng-click="refreshGrid();tInfo.loadStatus(false,true);">@Html(Messages("js.label.refresh"))<i class="menu-icon foundicon-refresh"></i></a></li>
            <li class="divider"></li>
            <li><a href="" data-ng-click="state.showViews = !state.showViews">@Html(Messages("js.label.views"))<i class="menu-icon awesome-icon-eye"></i></a></li>
            <li class="divider"></li>
            <li><a href="" data-ng-class="{'disabled':!options.editable}" data-ng-click="newRow();">@Html(Messages("js.label.add"))<i class="menu-icon foundicon-plus"></i></a></li>
            <li class="divider"></li>
            <li>
                <a href="" data-ng-class="{'disabled':!options.editable}" data-ng-click="clearGrid()">@Html(Messages("js.label.multientry"))<i class="menu-icon foundicon-plus"><sup class="foundicon-plus"></sup></i></a>
            </li>
            <li class="divider"></li>
            <li>
                <cf-export-to-excel/>
            </li>
            <li class="divider"></li>
            <li>
                <a href="" data-ng-click="deleteActiveRow(false)" data-ng-class="{'disabled':hasNoSelectedRow() || !options.editable}">@Html(Messages("label.delete"))
                    <i class="menu-icon awesome-icon-trash-o"></i></a>
            </li>
            <li class="divider"></li>
            <li>
                <a href=""
                   data-ng-class="{'disabled':tInfo.isSomethingLoading()}" data-ng-click="tInfo.importTestteams(modal.status,refreshGrid)"
                   data-ng-style="{'color':tInfo.isImportLoading() ? 'blue' : tInfo.hasImportError() ? 'red':false}"
                > @Html(Messages("label.danavl.sync.abbr"))
                    <i class="menu-icon" ng-class="{'foundicon-down-arrow':!tInfo.isImportLoading(), 'awesome-icon-spinner':tInfo.isImportLoading(), 'spinning-icon': tInfo.isImportLoading()}"></i> </a>
            </li>
            <li class="divider"></li>

            <li>
                <a href="" ng-class="{'disabled':!state.data.toBeSent}"
                   data-ng-click="tLogs.avlSmtpSendMarkedTestteams(state.data.toBeSent,logEventCallback)">
                    @Html(Messages("js.label.senddanavl"))
                    <i data-tooltip title="Send test teams which are marked 'Send to breeding organization'"
                       class="menu-icon foundicon-up-arrow"> </i>
                </a>
            </li>
            <li class="divider"></li>
            <li><a href="" data-ng-click="lockUnlockData()" data-ng-show="isAdmin">
                <span data-ng-if="isAllUnlocked" style="color:red">@Html(Messages("js.label.lock"))<i class="menu-icon awesome-icon-lock"></i></span>
                <span data-ng-if="!isAllUnlocked">@Html(Messages("js.label.unlock"))<i class="menu-icon awesome-icon-unlock"></i></span></a></li>
            <li class="divider"></li>
            <li><a href="" data-ng-click="m.danavlStatusModal.open()" data-ng-show="isDanavlSupport">
                <span ng-if="isDanavlSupport">@Html(Messages("js.label.danavlstatus"))<i class="menu-icon foundicon-inbox"></i></span></a>
            <li class="divider"></li>
            <li><a href="" ng-style="{'color':state.data.showAdminToolbar ? 'red' :false}" ng-if="isDanavlSupport" data-ng-click="state.data.showAdminToolbar=!state.data.showAdminToolbar">@Html(Messages("js.label.admin_tools"))<i
                    class="menu-icon awesome-icon-legal"></i></a></li>
            <li class="divider"></li>
            <!--<li>-->
            <!--<a-->
            <!--href="" data-ng-class="{'disabled':tInfo.data.import.loading}" data-ng-click="tInfo.data.importTestteams(modal.status,refreshGrid)">-->
            <!--<span data-ng-bind=" tInfo.data.import.loading ? '@Html(Messages(" js.label.processing"))' : '@Html(Messages("js.label.import"))'"></span>-->
            <!--<i data-tooltip title="Import active Test teams from DanAvl database. See import status for more information about pending data." class="menu-icon foundicon-inbox"></i>-->
            <!--</a>-->
            <!--</li>-->
            <!--<li class="divider"></li>-->
            <!--<li>-->
            <!--<a-->
            <!--href="" data-ng-class="{'disabled':tInfo.data.update.loading}" data-ng-click="tInfo.downloadAndUpdateTestsData(modal.update,refreshGrid)">-->
            <!--<span data-ng-bind=" tInfo.data.update.loading ? '@Html(Messages(" js.label.processing"))' : '@Html(Messages("js.label.update"))'"></span>-->
            <!--<i data-tooltip title="Update information about Breeding animals and Test teams according to DanAvl database. Update info : Start date, End date, Status" class="menu-icon foundicon-down-arrow"></i>-->
            <!--</a>-->
            <!--</li>-->
            <!--<li class="divider"></li>-->

            <!--<li>-->
            <!--<a href="" data-ng-class="{'disabled':tInfo.data.import.loading}" data-ng-click="tInfo.data.importTestteams(modal.status,refreshGrid,true)">-->
            <!--<span data-ng-bind=" tInfo.data.import.loading ? '@Html(Messages(" js.label.processing"))' : 'Devel import'"></span>-->
            <!--<i data-tooltip title="Import active Test teams from DanAvl database. See import status for more information about pending data." class="menu-icon foundicon-inbox"></i>-->
            <!--</a>-->
            <!--</li>-->
            <!--<li class="divider"></li>-->
        </div>
    </div>
</div>
<div id="unloack_dropbox" class="f-dropdown auto-max-width small-padding content" data-dropdown-content>
    <div data-ng-show="!hasSelectedRows">@Html(Messages("js.label.unlock_everyting"))</div>
    <table data-ng-show="hasSelectedRows" class="quickfilter" style="float: none; " data-dropdown-content>
        <tr>
            <td style="text-align: left; font-weight: bold;">@Html(Messages("js.label.actordate"))</td>
            <td style="text-align: left; font-weight: bold;">@Html(Messages("js.label.location.to"))</td>
        </tr>
        <tr>
            <td>
                <input name="movingAnimals.actorDate" type="text" data-ng-model="movingAnimals.actorDate" data-cf-date-time style="display: inline; padding-right: 3px; height: 22px; margin-bottom: 0; width: auto;">
            </td>
            <td>
                <input name="movingAnimals.to" type="text" data-ng-model="movingAnimals.to" class="ui-autocomplete-input" data-cf-lookup-location="['ALL']" data-ng-trim="false" style="display: inline; padding-right: 3px; height: 22px; margin-bottom: 0; width: auto;">
            </td>
            <td>
                <button style="width: 100%; margin: 0" class="small button" data-ng-click="movingAnimals.move()" data-ng-disabled="!movingAnimals.to || !movingAnimals.actorDate">@Html(Messages("js.label.move.selected"))</button>
            </td>
        </tr>
    </table>
</div>


<div data-cf-modal="modal.status" style="max-height: 85%;overflow: auto">
    <h2>@Html(Messages("label.danavl.sync"))</h2>

    <div class="row ">
        <div class="columns small-12">
            <fieldset ng-show="tInfo.data.status.result.error || tInfo.data.update.result.error || tInfo.data.import.result">
                <!--<legend>@Html(Messages("js.danavl.taskstatuscode.wrongcredentials.ext"))</legend>-->
                Result:
                <span ng-show="tInfo.data.status.result.error">{{tInfo.data.status.result|json}}</span>
                <span ng-show="tInfo.data.update.result.error">{{tInfo.data.update.result|json}}</span>
                <span ng-show="tInfo.data.import.result.error">{{tInfo.data.import.result|json}}</span>
            </fieldset>
            <fieldset ng-hide="tInfo.data.status.result.error">
                <legend>@Html(Messages("js.report.summary"))</legend>
                <label style="margin:10px">
                    @Html(Messages("js.danavl.testeam.sync.summary.expl"))
                </label>
                <table class="report" style="float:left;margin:10px;min-width: 500px">
                    <thead>
                    <tr data-ng-show="isDanavlSupport && state.data.showAdminToolbar">
                        <td colspan="2">
                            <button class="tiny button left expand" style="margin:0" data-ng-class="{'disabled':tInfo.isSomethingLoading()}" data-ng-click="tInfo.downloadTestsData(modal.status,refreshGrid)">
                                {{ tInfo.data.update.loading ? '@Html(Messages("js.label.processing"))' : 'Admin - Only Download' }}
                            </button>
                        </td>
                        <td colspan="2">
                            <button class="tiny button left expand" style="margin:0" data-ng-class="{'disabled':tInfo.isSomethingLoading()}" data-ng-click="tInfo.updateTestsData(modal.status,refreshGrid)">
                                {{ tInfo.data.update.loading ? '@Html(Messages("js.label.processing"))' : 'Admin - Only Update' }}
                            </button>
                        </td>
                    </tr>
                    <tr data-ng-show="isDanavlSupport && state.data.showAdminToolbar">
                        <td colspan="4">
                            <button class="tiny button left expand" style="margin:0" data-ng-class="{'disabled':tInfo.isSomethingLoading()}" data-ng-click="tInfo.postImportHops(modal.status,refreshGrid)">
                                {{ tInfo.data.update.loading ? '@Html(Messages("js.label.processing"))' : 'Admin - Post Import Hops' }}
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <button class="tiny button left expand" style="margin:0" data-ng-class="{'disabled':tInfo.isSomethingLoading()}" data-ng-click="tInfo.downloadAndUpdateTestsData(modal.status,refreshGrid)">
                                {{ tInfo.data.update.loading ? '@Html(Messages("js.label.processing"))' : '@Html(Messages("js.danavl.update_all_testteams"))' }}
                            </button>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            <button class="tiny button left expand" style="margin:0" data-ng-class="{'disabled':tInfo.isSomethingLoading()}" data-ng-click="tInfo.downloadAndUpdateSentTestsData(modal.status,refreshGrid)">
                                {{ tInfo.data.update.loading ? '@Html(Messages("js.label.processing"))' : '@Html(Messages("js.danavl.update_sent_testteams"))' }}
                            </button>
                        </td>
                    </tr>
                    <tr style="background: none">
                        <th rowspan="2">Action</th>
                        <th colspan="2">Last change</th>
                        <th rowspan="2">Total change Count</th>
                    </tr>
                    <tr style="background: none">
                        <th>Count</th>
                        <th>Date</th>
                    </tr>

                    </thead>
                    <tbody>
                    <tr>
                        <td class="left-align  "><strong>Breeding animals update</strong></td>
                        <td ng-bind="tInfo.data.status.result.updatedAnimalsLastCnt ? tInfo.data.status.result.updatedAnimalsLastCnt  :''"></td>
                        <td>{{ tInfo.data.status.result.updatedAnimalsLastDate |date:dateFormat }}</td>
                        <td ng-bind="tInfo.data.status.result.updatedAnimalsTotalCnt ? tInfo.data.status.result.updatedAnimalsTotalCnt :''"></td>
                    </tr>
                    <tr>
                        <td class="left-align"><strong>Test teams update</strong></td>
                        <td ng-bind="tInfo.data.status.result.updatedTestteamsLastCnt ? tInfo.data.status.result.updatedTestteamsLastCnt  :''"></td>
                        <td> {{tInfo.data.status.result.updatedTestteamsLastDate |date:dateFormat }}</td>
                        <td ng-bind="tInfo.data.status.result.updatedTestteamsTotalCnt ? tInfo.data.status.result.updatedTestteamsTotalCnt:''"></td>

                    </tr>
                    <tr>
                        <td class="left-align  "><strong>Test teams import</strong></td>
                        <td ng-bind="tInfo.data.import.result.importCounter ? tInfo.data.import.result.importCounter : ''"></td>
                        <td></td>
                        <td ng-bind="tInfo.data.status.result.importedTestteamsTotalCnt ? tInfo.data.status.result.importedTestteamsTotalCnt : ''"></td>
                    </tr>

                    <tr>
                        <td class="left-align"><strong>Import Hops</strong></td>
                        <td ng-bind="tInfo.data.postImport.result.hopCounter ? tInfo.data.postImport.result.hopCounter : ''"></td>
                        <td></td>
                        <td></td>
                    </tr>

                    </tbody>
                </table>
            </fieldset>
            <!--<p>-->
            <!--<span data-ng-if="tInfo.data.import.result.importCounter">-->
            <!--{{tInfo.data.import.result.importCounter}} test teams were imported last time.-->
            <!--</span>-->
            <!--<span data-ng-if="tInfo.data.status.result.importedTestteamsTotalCnt">-->
            <!--{{tInfo.data.status.result.importedTestteamsTotalCnt}} test teams were imported totally.-->
            <!--</span>-->
            <!--<span data-ng-if="tInfo.data.status.result.updatedAnimalsLastCnt">-->
            <!--{{tInfo.data.status.result.updatedAnimalsLastCnt}} animals were updated last time on ({{ tInfo.data.status.result.updatedAnimalsLastDate}})-->
            <!--</span>-->
            <!--<span data-ng-if="tInfo.data.status.result.updatedAnimalsTotalCnt">-->
            <!--{{tInfo.data.status.result.updatedAnimalsTotalCnt}} animals were updated totally.-->
            <!--</span>-->

            <!--<span data-ng-if="tInfo.data.status.result.updatedTestteamsLastCnt">-->
            <!--{{tInfo.data.status.result.updatedTestteamsLastCnt}} test teams were updated last time  on({{ tInfo.data.status.result.updatedTestteamsLastDate}})-->
            <!--</span>-->
            <!--<span data-ng-if="tInfo.data.status.result.updatedTestteamsTotalCnt">-->
            <!--{{tInfo.data.status.result.updatedTestteamsTotalCnt}} animals were updated totally.-->
            <!--</span>-->
            <!--</p>-->
            <!--</div>-->
        </div>
    </div>

    <!--<div class="row">-->
    <!--<div class="columns small-12">-->
    <!---->
    <!--</div>-->
    <!--</div>-->

    <div class="row">
        <div class="columns small-12">
            <div cf-testteam-pending-import cf-tinfo="tInfo" cf-tti="tInfo.data.status.result.pendingImportTestteams" modal="modal"/>
        </div>
    </div>
    <div class="row">
        <div class="columns small-12">
            <div cf-testteam-missing-locations cf-tti="tInfo.data.status.result.missingLocationTestteams"/>
        </div>
    </div>
    <div class="row" data-ng-if="tInfo.data.status.result.problematicImportTestteams.length > 0">
        <div class="columns small-12">
            <fieldset>
                <legend style="color:orange">@Html(Messages("js.danavl.overlapping_testteams.import"))</legend>
                <label style="margin:10px">@Html(Messages("js.danavl.overlapping_testteams.import.expl"))</label>
                <h4>@Html(Messages("js.label.most_frequent_causes"))</h4>
                <ul style="margin-left:50px">
                    <li><label>@Html(Messages("js.danavl.overlapping_testeams.cause1"))</label></li>
                    <li><label>@Html(Messages("js.danavl.overlapping_testeams.cause2"))</label></li>
                </ul>
                <div cf-testeam-problematic-info cf-tti="tInfo.data.status.result.problematicImportTestteams"/>
            </fieldset>
        </div>
    </div>
    <div class="row" data-ng-if="tInfo.data.status.result.problematicUpdateTestteams.length > 0">
        <div class="columns small-12">

            <fieldset>
                <legend style="color:orange">@Html(Messages("js.danavl.overlapping_testteams.update"))</legend>
                <label style="margin:10px">@Html(Messages("js.danavl.overlapping_testteams.update.expl"))</label>
                <h4>@Html(Messages("js.label.most_frequent_causes"))</h4>
                <ul style="margin-left:50px">
                    <li><label>@Html(Messages("js.danavl.overlapping_testeams.cause3"))</label></li>
                </ul>
                <div cf-testeam-problematic-info cf-tti="tInfo.data.status.result.problematicUpdateTestteams"/>
            </fieldset>
        </div>
    </div>

    <div class="row">
        <div class="columns small-12">
            <fieldset data-ng-if="tInfo.data.status.result.testteamLogs.length > 0">
                <legend>Messages to Danavl over Last Month</legend>
                <label style="margin:10px">List of messages to Danavl. Messages are either created for sending or were send and confirmed or failed. In case of failure there can be multiple messages per test team</label>
                <table cf-testteam-log-table cf-logs="tInfo.data.status.result.testteamLogs" class="report" style="float:left;margin:10px;min-width: 500px"></table>

            </fieldset>
        </div>
    </div>
</div>
<div data-cf-modal="m.danavlStatusModal" style="height:80%">
    <div style="height:100%" cf-danavl-status/>
</div>
<div data-cf-modal="modal.update">
    <h2>Update result</h2>

    <div class="row">
        <div class="columns small-12">
            <label data-ng-show="tInfo.data.update.result.updatedAnimalsCnt">{{tInfo.data.update.result.updatedAnimalsCnt}} breeding animals were updated</label>

            <label data-ng-show="tInfo.data.update.result.updatedAnimalsCnt">{{tInfo.data.update.result.updatedTestteamsCnt}} test teams were updated</label>
        </div>
    </div>
</div>

<div data-cf-modal="modal.logs">
    <h2>Log file for DanAvl</h2>

    <div data-ng-if="tLogs.data[item.id]">
        <!--exclude impor-->
        <div class="row">
            <fieldset>
                <pre data-ng-bind="tLogs.data[item.id]"></pre>
            </fieldset>
        </div>
        <div class="row" data-ng-if="!sendErrorMessage && (item.cfStatus===0 ||item.cfStatus===7 || item.cfStatus===8)">
            <!--<button class="button small left">Confirm and Send</button>-->
            <button class="button small left" ng-click="tLogs.approveLogsAvlSmtpSend(item,dataRight.data,logEventCallback)">
                Send
                @Html(Messages("js.label.senddanavl"))
            </button>
        </div>
        <div class="row" data-ng-if="!sendErrorMessage">
            <!--<button class="button small left">Confirm and Send</button>-->
            <button class="button small left">
                <a style="color:white" href="{{'/api/testteam/confirmlogs/file/'+item.id}}">@Html(Messages("js.label.download_zip"))</a>
            </button>
        </div>
    </div>

</div>

