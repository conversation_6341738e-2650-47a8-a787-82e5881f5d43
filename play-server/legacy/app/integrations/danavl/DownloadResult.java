package integrations.danavl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import jooq.public_.tables.records.DanavlUpdateLoggerRecord;
import org.apache.commons.lang3.ArrayUtils;
import org.jooq.DSLContext;
import utils.Utils;

import java.util.*;

import static jooq.public_.Tables.DANAVL_UPDATE_LOGGER;

/**
 * Created by Matej_2 on 14.11.2015.
 */
public class DownloadResult {//TODO cleanup this whole class after the index download is refactored
   String actionName = "";
   List<String[]> parsedDataRows = new ArrayList<>();
   List<String[]> invalidParsedDataRows = new ArrayList<>();
   Map<String, Integer> parsedDataRowsCntByParams = new HashMap<>();
   Map<String, String[]> byAnimalid = new HashMap<>();
   String exceptionMessage = "";
   public String url = "";
   List<String> missingRights = new ArrayList<>();
   public List<String> params = new ArrayList<>();
   public String errorMessage = "";
   public long createDate = System.currentTimeMillis();
   long invalidParsedDataRowsCnt = 0;
   long parsedRowsCnt = 0;
   long uniqueParsedDataRowsCnt = 0;
   long updatedRowsCnt = 0;
   long insertedRowsCnt = 0;
   private boolean serviceClosed = false;
   boolean connectionError = false;
   public int errorCode = 0;
   public String finalStatus = "";
   public long farmId;
   public long accountId;


    DownloadResult() {
    }

    DownloadResult(String actionName) {
        this.actionName = actionName;
    }

    public JsonNode briefJson() {
        ObjectNode res = Utils.createObjectNode();
        res.put("finalStatus", this.finalStatus);
        res.put("actionName", this.actionName);
        res.put("parsedRowsCnt", this.parsedRowsCnt);
        res.put("insertedRowsCnt", this.insertedRowsCnt);
        res.put("invalidParsedDataRowsCnt", this.invalidParsedDataRowsCnt);
        res.put("updatedRowsCnt", this.updatedRowsCnt);
        res.put("errorCode", this.errorCode);
        res.put("connectionError", this.connectionError);
        res.put("serviceClosed", this.serviceClosed);
        return res;
    }


    public boolean success() {
        return this.parsedDataRows.size() > 0 && this.updatedRowsCnt > 0 && !this.connectionError && !this.serviceClosed;
    }

    public long durationMillis() {
        return System.currentTimeMillis() - createDate;
    }

    public void insertLogger(DSLContext db, String status, Integer errorcode, long farmId, long accountId, String
        actionName) {
        this.actionName = actionName;
        this.finalStatus = status;
        this.errorCode = errorcode;
        this.farmId = farmId;
        this.accountId = accountId;
        insertLogger(db);
    }

    public void insertLogger(DSLContext db) {
        DanavlUpdateLoggerRecord nr = db.newRecord(DANAVL_UPDATE_LOGGER);
        nr.setActionname(actionName);
        nr.setStatus(finalStatus);
        nr.setErrorcode(errorCode);
        nr.setParams(ArrayUtils.toString(params));
        nr.setSource(url);
        nr.setFarmId(farmId);
        nr.setAccountId(accountId);
        nr.setUuid(UUID.randomUUID().toString());
        nr.setUpdated(updatedRowsCnt);
        nr.setInserted(insertedRowsCnt);
        nr.setInvalid(invalidParsedDataRowsCnt);
        nr.setMissingRights(Utils.toJson(this.missingRights).toString());
        nr.setDuration(durationMillis());
        nr.setData(Utils.toJson(Arrays.asList(exceptionMessage,errorMessage)).toString());
        nr.store();
    }


}
