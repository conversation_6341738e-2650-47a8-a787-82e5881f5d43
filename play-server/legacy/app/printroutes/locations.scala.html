@(loc: models.Location, farmId: Long, print: <PERSON><PERSON><PERSON>, messages: play.api.i18n.Messages)
@import controllers.QR
@generate(loc: models.Location) = {
    <h2 class="long-number">@Html(messages("js.label.locationtype." + loc.getLocationType().getId()))</h2>
    @if(loc.getLocationType().getId() == "BA" && loc.getLocationNumber().length() > 10){
        <h1 @if(loc.getLocationNumber().length() > 20) {class="very-long-number"} else {class="long-number"}>@loc.getLocationNumber().slice(0,10)</h1>
        <h1 @if(loc.getLocationNumber().length() > 20) {class="very-long-number"} else {class="long-number"}>@loc.getLocationNumber().slice(10,20)</h1>
        <h1 @if(loc.getLocationNumber().length() > 20) {class="very-long-number"} else {class="long-number"}>@loc.getLocationNumber().slice(20,30)</h1>
    } else {
        <h1 @if(loc.getLocationNumber().length() > 5) {
        class="long-number"
        } else { @if(loc.getLocationNumber().length() > 2) {
        class="medium-number"
        }}>@loc.getLocationNumber()</h1>
        @if(null != loc.getParent()) {
        <h2 @if(loc.getParent().getFullReverseNumber().length() > 15) {
        class="long-number"
        } else {@if(loc.getParent().getFullReverseNumber().length() > 8) {
        class="medium-number"
        }}>@loc.getParent().getFullReverseNumber()</h2>
        }
    }
    <div>
        <img class="qr"  src='@QR.embedded("L:"+farmId+":"+loc.getId())' />
    </div>
    @if(loc.getInhabitanttype_code() != null) {
        <h2 class="long-number">(@Html(messages("js.label.inhabitant." + loc.getInhabitanttype_code().toLowerCase())))</h2>
    }
}
@generateFull(loc: models.Location) = {
    @if(null == loc.getLocationType()) {
        @for( sub <- loc.getChildren()) {
            @generateFull(sub)
        }
    } else {
        @loc.getLocationType().getId() match {
            case "ST" | "SE" | "FA" | "AR" | "SI" | "HO" | "BA" => {
                <div class="location one-per-page">
                    @generate(loc)
                </div>
                @for( i <- 1 to 4 ) {
                <div class="location some-per-page">
                    @generate(loc)
                </div>
                }
                @for( sub <- loc.getChildren()) {
                    @generateFull(sub)
                }
            }
            case _ => {
                <div class="location some-per-page">
                    @generate(loc)
                </div>
            }
        }
    }
}
<!DOCTYPE html>
<html>
<head>
    <title>@Html(messages("js.title.location")) @loc.getFullReverseNumber()</title>
    <link rel="stylesheet" href="css/print.css" />
</head>
<body class="print-locations">
    @generateFull(loc)
</body>
</html>
