package controllers;

import com.cloudfarms.pigs.sync.events.FosteringReportingDTO;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.ser.FilterProvider;
import com.fasterxml.jackson.databind.ser.impl.SimpleBeanPropertyFilter;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import com.google.common.collect.Lists;
import jooq.farm.Tables;
import models.*;
import models.handlers.*;
import org.apache.commons.lang3.StringUtils;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.jooq.impl.DefaultConnectionProvider;
import play.api.http.FileMimeTypes;
import play.data.FormFactory;
import play.i18n.Messages;
import play.mvc.*;
import play.mvc.Result;
import security.*;
import utils.*;
import utils.EntityErrors.Message;

import javax.inject.Inject;
import javax.inject.Singleton;
import jakarta.persistence.EntityManager;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Connection;
import java.util.*;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.sum;


@Singleton
public class FosteringController extends RestController {


    private final FosteringHandler fosteringHandler;
    private final SowHandler sowHandler;
    private final IndividualAnimalHandler individualAnimalHandler;
    private final ServingHandler servingHandler;

    private static final String ALIAS_HOP = "hop";
    private static final String ALIAS_FROM_SOW = "fs";
    private static final String ALIAS_TO_SOW = "ts";
    private static final String ALIAS_FROM_SERVING = "fi";
    private static final String ALIAS_TO_SERVING = "ti";
    private static final String ALIAS_FOSTERING = "f";
    private static final String ALIAS_FROM_LOCATION = "fl";
    private static final String ALIAS_TO_LOCATION = "tl";

    private static final ScreenOrder SCREEN_ORDER = new ScreenOrder();

    static {
        SCREEN_ORDER.add("id", fields(Tables.FOSTERING.as(ALIAS_FOSTERING).ID));
        SCREEN_ORDER.add("amount", fields(Tables.FOSTERING.as(ALIAS_FOSTERING).AMOUNT));
        SCREEN_ORDER.add("actorDate", fields(Tables.FOSTERING.as(ALIAS_FOSTERING).ACTOR_DATE));
        SCREEN_ORDER.add("createDate", fields(Tables.FOSTERING.as(ALIAS_FOSTERING).CREATE_DATE));
        SCREEN_ORDER.add("updateDate", fields(Tables.FOSTERING.as(ALIAS_FOSTERING).UPDATE_DATE));
        SCREEN_ORDER.add("comment", fields(Tables.FOSTERING.as(ALIAS_FOSTERING).COMMENT));
        SCREEN_ORDER.add("actorId", fields(Tables.FOSTERING.as(ALIAS_FOSTERING).ACTOR_ID));
        SCREEN_ORDER.add("createAccountId", fields(Tables.FOSTERING.as(ALIAS_FOSTERING).CREATE_ACCOUNT_ID));
        SCREEN_ORDER.add("updateAccountId", fields(Tables.FOSTERING.as(ALIAS_FOSTERING).UPDATE_ACCOUNT_ID));
        SCREEN_ORDER.add("fromLocationId", fields(Tables.FOSTERING.as(ALIAS_FOSTERING).FROM_LOCATION_ID));
        SCREEN_ORDER.add("toLocationId", fields(Tables.FOSTERING.as(ALIAS_FOSTERING).TO_LOCATION_ID));
        SCREEN_ORDER.add("createUi", fields(Tables.FOSTERING.as(ALIAS_FOSTERING).CREATE_UI));
        SCREEN_ORDER.add("fromServing.sow.sowNumber", fields(fo("row(" + ALIAS_FROM_SOW + ".SOWNUMBER)::locname")));
        SCREEN_ORDER.add("fromServing.sow.animalId", fields(Tables.SOW.as(ALIAS_FROM_SOW).ANIMALID));
        SCREEN_ORDER.add("fromServing.actorDate", fields(Tables.SERVING.as(ALIAS_FROM_SERVING).ACTOR_DATE));
        SCREEN_ORDER.add("fromServing.farrowStartTime", fields(Tables.SERVING.as(ALIAS_FROM_SERVING).FARROW_STARTTIME));
        SCREEN_ORDER.add("toServing.sow.sowNumber", fields(fo("row(" + ALIAS_TO_SOW + ".SOWNUMBER)::locname")));
        SCREEN_ORDER.add("toServing.sow.animalId", fields(Tables.SOW.as(ALIAS_TO_SOW).ANIMALID));
        SCREEN_ORDER.add("toServing.actorDate", fields(Tables.SERVING.as(ALIAS_TO_SERVING).ACTOR_DATE));
        SCREEN_ORDER.add("toServing.farrowStartTime", fields(Tables.SERVING.as(ALIAS_TO_SERVING).FARROW_STARTTIME));
        SCREEN_ORDER.add("gilt.animalId", fields(Tables.GILT.as("g").ANIMALID));
    }

    private static final ScreenOrder SCREEN_ORDER_X = new ScreenOrder();

    static {
        SCREEN_ORDER_X.add("id", fields(fo("f___id")));
        SCREEN_ORDER_X.add("amount", fields(fo("f___amount")));
        SCREEN_ORDER_X.add("actorDate", fields(fo("f___actor_date")));
        SCREEN_ORDER_X.add("createDate", fields(fo("f___create_date")));
        SCREEN_ORDER_X.add("updateDate", fields(fo("f___create_date")));
        SCREEN_ORDER_X.add("comment", fields(fo("f___comment")));
        SCREEN_ORDER_X.add("actorId", fields(fo("f___actor_id")));
        SCREEN_ORDER_X.add("createAccountId", fields(fo("f___create_actor_id")));
        SCREEN_ORDER_X.add("updateAccountId", fields(fo("f___update_account_id")));
        SCREEN_ORDER_X.add("fromLocationId", fields(fo("f___from_location_id")));
        SCREEN_ORDER_X.add("toLocationId", fields(fo("f___to_location_id")));
        SCREEN_ORDER_X.add("createUi", fields(fo("f___create_ui")));
        SCREEN_ORDER_X.add("fromServing.sow.sowNumber", fields(fo("row(fs___sownumber)::locname")));
        SCREEN_ORDER_X.add("fromServing.actorDate", fields(fo("fi___actor_date")));
        SCREEN_ORDER_X.add("fromServing.farrowStartTime", fields(fo("fi___farrow_starttime")));
        SCREEN_ORDER_X.add("toServing.sow.sowNumber", fields(fo("row(ts___sownumber)::locname")));
        SCREEN_ORDER_X.add("toServing.actorDate", fields(fo("ti___actor_date")));
        SCREEN_ORDER_X.add("toServing.farrowStartTime", fields(fo("ti___farrow_starttime")));
        SCREEN_ORDER_X.add("gilt.animalId", fields(fo("g___animalid")));
    }

    private final FormFactory formFactory;

    @Inject
    public FosteringController(play.Application application, FosteringHandler fosteringHandler, SowHandler sowHandler, IndividualAnimalHandler individualAnimalHandler, ServingHandler servingHandler, FileMimeTypes fileMimeTypes, FormFactory formFactory) {
        super(application, fileMimeTypes);

        this.fosteringHandler = fosteringHandler;
        this.sowHandler = sowHandler;
        this.individualAnimalHandler = individualAnimalHandler;
        this.servingHandler = servingHandler;
        this.formFactory = formFactory;
    }

    private static class Mappers {
        public final RecordMapper<Record, Fostering> fosteringRecordMapper;
        public final RecordMapper<Record, Serving> fromServingRecordMapper;
        public final RecordMapper<Record, Serving> toServingRecordMapper;
        public final RecordMapper<Record, Sow> fromSowRecordMapper;
        public final RecordMapper<Record, Sow> toSowRecordMapper;
        public final RecordMapper<Record, BreedingAnimal> giltRecordMapper;

        private Mappers(Select<Record> query) {
            this.fosteringRecordMapper = new CfRecordMapper<>("f___", query.fields(), Fostering.class, null, JooqUtils.CF_JOOQ_CONFIG);
            this.fromServingRecordMapper = new CfRecordMapper<>("fi___", query.fields(), Serving.class, null, JooqUtils.CF_JOOQ_CONFIG);
            this.toServingRecordMapper = new CfRecordMapper<>("ti___", query.fields(), Serving.class, null, JooqUtils.CF_JOOQ_CONFIG);
            this.fromSowRecordMapper = new CfRecordMapper<>("fs___", query.fields(), Sow.class, null, JooqUtils.CF_JOOQ_CONFIG);
            this.toSowRecordMapper = new CfRecordMapper<>("ts___", query.fields(), Sow.class, null, JooqUtils.CF_JOOQ_CONFIG);
            this.giltRecordMapper = new CfRecordMapper<>("g___", query.fields(), BreedingAnimal.class, null, JooqUtils.CF_JOOQ_CONFIG);
        }
    }

    public static Fostering fromRecord(Record record, final Mappers mappers) {
        Fostering fostering = mappers.fosteringRecordMapper.map(record);
        Serving fromServing = mappers.fromServingRecordMapper.map(record);
        Serving toServing = mappers.toServingRecordMapper.map(record);
        Sow fromSow = mappers.fromSowRecordMapper.map(record);
        Sow toSow = mappers.toSowRecordMapper.map(record);
        BreedingAnimal gilt = mappers.giltRecordMapper.map(record);
        fromServing.setSow(fromSow);
        toServing.setSow(toSow);
        fostering.setFromServing(fromServing);
        fostering.setToServing(toServing);
        fostering.setGilt(gilt);
        return fostering;
    }

    @WithTenant({@Group(farm = FarmRole.weaningRead), @Group(farm = FarmRole.weaningWrite)})
    public Result list(int from, int count, String order, String columns, Http.Request request) {
        final FosteringFilters filters = formFactory.form(FosteringFilters.class).bindFromRequest(request).get();
        final ApplicationUser user = applicationUserHandler.current(request);
        return withEm(request, em -> {
            FilterProvider filtering = new SimpleFilterProvider()
                .addFilter("BreedingAnimalFilter", SimpleBeanPropertyFilter.filterOutAllExcept("animalId"))
                .addFilter("SowFilter", SimpleBeanPropertyFilter.filterOutAllExcept("sowNumber", "animalId"))
                .addFilter("ServingFilter", SimpleBeanPropertyFilter.filterOutAllExcept("sow", "liveborn"));
            final Select<Record> query = prepareJooqQuery(user, filters, order, false, columns, from, count);
            Connection db = em.unwrap(Connection.class);
            final Configuration configWithDb = JooqUtils.CF_JOOQ_CONFIG.derive(new DefaultConnectionProvider(db));
            query.attach(configWithDb);
            final Mappers mappers = new Mappers(query);

            int filteredCount;
            final org.jooq.Result<Record> fetch = query.fetch();
            final List<Fostering> list = new ArrayList<>();
            fetch.into(record -> {
                Fostering x = fromRecord(record, mappers);
                list.add(x);
            });

            if (list.size() < count) {
                filteredCount = from + list.size();
            } else {
                final Select<Record> countQuery = prepareJooqQuery(user, filters, "", false, columns, -1, -1);
                final List<Field<?>> select = countQuery.getSelect();
                select.clear();
                select.add(DSL.one());
                filteredCount = fetchCount(em, countQuery);
            }

            SelectConditionStep<Record1<BigDecimal>> statisticsQuery = prepareStatisticsQuery(user, filters);
            statisticsQuery.attach(configWithDb);
            Record record = statisticsQuery.fetchOne();


            Long amountSum = record.getValue("amountSum", Long.class);
            return ok(Utils.toJson(list, JsonLevel.Overview.class, filtering))
                .withHeaders(
                    "x-cf-Items-Count", Integer.toString(filteredCount),
                    "x-cf-amountSum", amountSum == null ? "0" : amountSum.toString()
                );
        });
    }

    @WithTenant({@Group(farm = FarmRole.weaningRead), @Group(farm = FarmRole.weaningWrite)}) // TODO - new farm role
    public Result info(long whenMs, String sowNumber, String giltAnimalId, long fosteringId, String sowAnimalId, Http.Request request) throws IOException {
        final ApplicationUser user = applicationUserHandler.current(request);
        final Messages messages = messagesApi.preferred(request);
        final Date when = new Date(whenMs);
        return withEm(request, em -> {
            EntityErrors errors = new EntityErrors();
            if (StringUtils.isNotEmpty(sowNumber) || StringUtils.isNotEmpty(sowAnimalId)) {
                final Sow sow = StringUtils.isNotEmpty(sowAnimalId) ? individualAnimalHandler.sowByAnimalId(em, sowAnimalId, when) : sowHandler.bySowNumber(em, sowNumber, when);
                if (sow == null) {
                    if (StringUtils.isNotEmpty(sowAnimalId)) {
                        errors.properties.put("sowAnimalId", new Message(messages.at("error.sow.not.found", sowAnimalId, when)));
                    } else {
                        errors.properties.put("sowNumber", new Message(messages.at("error.sow.not.found", sowNumber, when)));
                    }
                    return notFound(Utils.toJson(errors));
                } else {
                    Serving farrowing = settingsHandler.isServingLacatingSowAllowed(user) ? servingHandler.lastFarrowingOfSowBefore(em, sow, when) : sowHandler.lastSowServingBefore(em, sow, when);
                    if (farrowing == null) {
                        errors.properties.put("sowNumber", new Message(messages.at("error.farrowing.not.found.before", sow.getSowNumber(), when, sow.getState())));
                    } else if (farrowing.getFarrowStartTime() == null || farrowing.getFarrowStartTime().after(when)) {
                        errors.properties.put("actorDate", new Message(messages.at("error.sow.not.farrowed.on", sow.getSowNumber(), when)));
                    } else {
                        final Optional<Weaned> maybeEarlierWeaning = farrowing.getWeanings().stream().filter(w -> !w.isNursery() && !w.getActorDate().after(when)).findFirst();
                        maybeEarlierWeaning.ifPresent(weaned -> errors.properties.put("actorDate", new Message(messages.at("error.sow.already.weaned.on", sow.getSowNumber(), weaned.getActorDate()))));
                    }
                    if (errors.hasErrors()) {
                        return forbidden(Utils.toJson(errors));
                    }
                    long locationId = sow.getCreatedLocationId();
                    for (Hop h : sow.getHops()) {
                        if (h.getActorDate().after(when)) break;
                        locationId = h.getTo();
                    }

                    int liveborn = farrowing.getLiveborn() == null ? 0 : farrowing.getLiveborn();
                    int sucklings = farrowing.getSucklingsBefore(when);
                    ObjectMapper om = new ObjectMapper();

                    ObjectNode retval = om.createObjectNode();
                    retval.put("sowId", sow.getId());
                    retval.put("sowNumber", sow.getSowNumber());
                    retval.put("animalId", sow.getAnimalId());
                    retval.put("locationId", locationId);
                    retval.put("liveborn", liveborn);
                    retval.put("sucklings", sucklings);
                    return ok(Utils.toJson(retval));
                }
            } else if (StringUtils.isNotEmpty(giltAnimalId)) {
                final BreedingAnimal gilt = individualAnimalHandler.breedingAnimalByAnimalId(em, giltAnimalId, when, false);
                if (gilt == null) {
                    errors.properties.put("gilt.animalId", new Message(messages.at("validation.error.fostering.suckling.not.found", giltAnimalId)));
                    return notFound(Utils.toJson(errors));
                } else if (gilt.getDeath() != null && !gilt.getDeath().getActorDate().after(when)) {
                    errors.properties.put("gilt.animalId", new Message(messages.at("validation.error.fostering.suckling.not.active", giltAnimalId, gilt.getDeath().getActorDate())));
                    return forbidden(Utils.toJson(errors));
                } else if (gilt.getServingId() == null) {
                    errors.properties.put("gilt.animalId", new Message(messages.at("validation.error.fostering.suckling.not.born.here", giltAnimalId)));
                    return forbidden(Utils.toJson(errors));
                } else {

                    final Date birthdate = gilt.getServing().getFarrowStartTime();
                    if (!birthdate.before(when)) {
                        errors.properties.put("gilt.animalId", new Message(messages.at("validation.error.fostering.suckling.born.later", giltAnimalId, birthdate)));
                        return forbidden(Utils.toJson(errors));
                    }
                    // TODO - Check whether it is the right time (if too old, or before in non farrowing location, reject

                    // Note: The `farrowing` is not necessarily the farrowing where the gilt was born. It is the farrowing of the sow the gilt was last under (gilt.getFosterings fetches them in chronological order)
                    final Serving farrowing = Lists.reverse(gilt.getFosterings()).stream().filter(f -> f.getId() != fosteringId && f.getActorDate().before(when)).findFirst().map(Fostering::getToServing).orElse(gilt.getServing());

                    final Sow sow = farrowing.getSow();
                    final Optional<Weaned> maybeEarlierWeaning = farrowing.getWeanings().stream().filter(w -> !w.isNursery() && !w.getActorDate().after(when)).findFirst();
                    maybeEarlierWeaning.ifPresent(weaned -> errors.properties.put("actorDate", new Message(messages.at("error.sow.already.weaned.on", sowNumber, weaned.getActorDate()))));

                    if (errors.hasErrors()) {
                        return forbidden(Utils.toJson(errors));
                    }
                    long locationId = sow.getCreatedLocationId();
                    for (Hop h : sow.getHops()) {
                        if (h.getActorDate().after(when)) break;
                        locationId = h.getTo();
                    }

                    int liveborn = farrowing.getLiveborn() == null ? 0 : farrowing.getLiveborn();
                    int sucklings = farrowing.getSucklingsBefore(when);
                    ObjectMapper om = new ObjectMapper();

                    ObjectNode retval = om.createObjectNode();
                    retval.put("sowId", sow.getId());
                    retval.put("sowNumber", sow.getSowNumber());
                    retval.put("sowNumber", sow.getSowNumber());
                    retval.put("animalId", sow.getAnimalId());
                    retval.put("locationId", locationId);
                    retval.put("liveborn", liveborn);
                    retval.put("sucklings", sucklings);
                    return ok(Utils.toJson(retval));
                }

            } else return notFound();
        });
    }

    @WithTenant({@Group(farm = FarmRole.weaningRead), @Group(farm = FarmRole.weaningWrite)}) // TODO - new farm role
    public Result excel(String order, String columns, String viewColumns, Http.Request request) throws IOException {
        final FosteringFilters filters = formFactory.form(FosteringFilters.class).bindFromRequest(request).get();
        final ApplicationUser user = applicationUserHandler.current(request);
        final Messages messages = messagesApi.preferred(request);
        final Select<Record> query = prepareJooqQuery(user, filters, order, false, columns, -1, -1); // TODO - will not select the available animals for fostering
        return withEm(request, em -> {
            Connection db = em.unwrap(Connection.class);
            final Configuration configWithDb = JooqUtils.CF_JOOQ_CONFIG.derive(new DefaultConnectionProvider(db));
            query.attach(configWithDb);
            final Mappers mappers = new Mappers(query);
            Cursor<Record> cursor = query.fetchLazy();

            final Select<Record> countQuery = prepareJooqQuery(user, filters, "", false, columns, -1, -1);
            int filteredCount = fetchCount(em, countQuery);
            HashMap<String, Integer> columnsWidth = RestController.getViewColumns(viewColumns);
            final ScreenColumns<?> screenColumns = new FosteringColumns(em);
            return excel(
                request,
                new JooqRowIterator<Fostering>(cursor) {
                    @Override
                    public Fostering recordToTarget(Record record) {
                        return fromRecord(record, mappers);
                    }
                }, screenColumns.getColOrder(), screenColumns.getColDefs(), columns, "fostering", filteredCount, messages.at("js.title.fostering"), columnsWidth);         // TODO: rename
        });
    }

    private Select<Record> prepareJooqQuery(ApplicationUser user, FosteringFilters filters, String order, boolean withBreedInfo, String columns, int from, int count) {
        List<Condition> conditions = new ArrayList<>();
        final jooq.farm.tables.Fostering fostering = Tables.FOSTERING.as(ALIAS_FOSTERING);
        final jooq.farm.tables.Serving fromServing = Tables.SERVING.as(ALIAS_FROM_SERVING);
        final jooq.farm.tables.Serving toServing = Tables.SERVING.as(ALIAS_TO_SERVING);
        final jooq.farm.tables.Sow fromSow = Tables.SOW.as(ALIAS_FROM_SOW);
        final jooq.farm.tables.Sow toSow = Tables.SOW.as(ALIAS_TO_SOW);
        final jooq.farm.tables.Dead dead = Tables.DEAD.as("d");
        final jooq.farm.tables.Weaned weaned = Tables.WEANED.as("w");
        final jooq.farm.tables.Fostering nestedFostering = Tables.FOSTERING.as("fx");
        final jooq.farm.tables.Gilt gilt = Tables.GILT.as("g");

        applyConditions(user, conditions, filters, fostering, fromServing, toServing, fromSow, toSow, gilt);

        if (order == null) {
            order = "actorDate desc";
        }


        List<Field<?>> fields = JooqUtils.makeFields(new Field<?>[][]{JooqUtils.prefixFields("f___", fostering.fields())});
        fields.add(fromSow.ID.as("fs___id"));
        fields.add(toSow.ID.as("ts___id"));
        fields.add(fromSow.SOWNUMBER.as("fs___sowNumber"));
        fields.add(fromSow.ANIMALID.as("fs___animalId"));
        fields.add(toSow.SOWNUMBER.as("ts___sowNumber"));
        fields.add(toSow.ANIMALID.as("ts___animalId"));
        fields.add(fromServing.LIVEBORN.as("fi___liveborn"));
        fields.add(toServing.LIVEBORN.as("ti___liveborn"));
        fields.add(gilt.ANIMALID.as("g___animalid"));

        if (count < 0) {
            return DSL.select(fields)
                .from(fostering)
                .join(fromServing).on(fromServing.ID.equal(fostering.FROM_SERVING_ID))
                .join(toServing).on(toServing.ID.equal(fostering.TO_SERVING_ID))
                .join(fromSow).on(fromSow.ID.equal(fromServing.SOW_ID))
                .join(toSow).on(toSow.ID.equal(toServing.SOW_ID))
                .leftOuterJoin(gilt).on(gilt.ID.eq(fostering.GILT_ID))
                .where(conditions);
        }

        final CommonTableExpression<Record> x = DSL.name("x").as(
            DSL.select(fields)
                .from(fostering)
                .join(fromServing).on(fromServing.ID.equal(fostering.FROM_SERVING_ID))
                .join(toServing).on(toServing.ID.equal(fostering.TO_SERVING_ID))
                .join(fromSow).on(fromSow.ID.equal(fromServing.SOW_ID))
                .join(toSow).on(toSow.ID.equal(toServing.SOW_ID))
                .leftOuterJoin(gilt).on(gilt.ID.eq(fostering.GILT_ID))
                .where(conditions)
                .orderBy(order(order, SCREEN_ORDER, fostering.ID.desc()))
                .limit(from, count)
        );

        final CommonTableExpression<Record2<Long, BigDecimal>> fromDeadSum = DSL.name("fd").as(
            DSL.select(x.field("f___id", Long.class), sum(dead.AMOUNT).as("amount")).from(dead).join(x)
                .on(dead.SERVING_ID.eq(x.field("f___from_serving_id", fostering.FROM_SERVING_ID.getDataType())))
                .and(dead.ACTOR_DATE.lt(x.field("f___actor_date", fostering.ACTOR_DATE.getDataType())))
                .groupBy(x.field("f___id"))
        );

        final CommonTableExpression<Record2<Long, BigDecimal>> toDeadSum = DSL.name("td").as(
            DSL.select(x.field("f___id", Long.class), sum(dead.AMOUNT).as("amount")).from(dead).join(x)
                .on(dead.SERVING_ID.eq(x.field("f___to_serving_id", fostering.TO_SERVING_ID.getDataType())))
                .and(dead.ACTOR_DATE.lt(x.field("f___actor_date", fostering.ACTOR_DATE.getDataType())))
                .groupBy(x.field("f___id"))
        );

        final CommonTableExpression<Record2<Long, BigDecimal>> fromWeanSum = DSL.name("fw").as(
            DSL.select(x.field("f___id", Long.class), sum(weaned.AMOUNT).as("amount")).from(weaned).join(x)
                .on(weaned.SERVING_ID.eq(x.field("f___from_serving_id", fostering.FROM_SERVING_ID.getDataType())))
                .and(weaned.ACTOR_DATE.lt(x.field("f___actor_date", fostering.ACTOR_DATE.getDataType())))
                .groupBy(x.field("f___id"))
        );

        final CommonTableExpression<Record2<Long, BigDecimal>> toWeanSum = DSL.name("tw").as(
            DSL.select(x.field("f___id", Long.class), sum(weaned.AMOUNT).as("amount")).from(weaned).join(x)
                .on(weaned.SERVING_ID.eq(x.field("f___to_serving_id", fostering.TO_SERVING_ID.getDataType())))
                .and(weaned.ACTOR_DATE.lt(x.field("f___actor_date", fostering.ACTOR_DATE.getDataType())))
                .groupBy(x.field("f___id"))
        );

        final CommonTableExpression<Record2<Long, BigDecimal>> fromFromFosterSum = DSL.name("fff").as(
            DSL.select(x.field("f___id", Long.class), sum(nestedFostering.AMOUNT).as("amount")).from(nestedFostering).join(x)
                .on(nestedFostering.FROM_SERVING_ID.eq(x.field("f___from_serving_id", fostering.FROM_SERVING_ID.getDataType())))
                .and(nestedFostering.ACTOR_DATE.lt(x.field("f___actor_date", fostering.ACTOR_DATE.getDataType())))
                .groupBy(x.field("f___id"))
        );

        final CommonTableExpression<Record2<Long, BigDecimal>> fromToFosterSum = DSL.name("ftf").as(
            DSL.select(x.field("f___id", Long.class), sum(nestedFostering.AMOUNT).as("amount")).from(nestedFostering).join(x)
                .on(nestedFostering.TO_SERVING_ID.eq(x.field("f___from_serving_id", fostering.FROM_SERVING_ID.getDataType())))
                .and(nestedFostering.ACTOR_DATE.lt(x.field("f___actor_date", fostering.ACTOR_DATE.getDataType())))
                .groupBy(x.field("f___id"))
        );

        final CommonTableExpression<Record2<Long, BigDecimal>> toFromFosterSum = DSL.name("tff").as(
            DSL.select(x.field("f___id", Long.class), sum(nestedFostering.AMOUNT).as("amount")).from(nestedFostering).join(x)
                .on(nestedFostering.FROM_SERVING_ID.eq(x.field("f___to_serving_id", fostering.TO_SERVING_ID.getDataType())))
                .and(nestedFostering.ACTOR_DATE.lt(x.field("f___actor_date", fostering.ACTOR_DATE.getDataType())))
                .groupBy(x.field("f___id"))
        );

        final CommonTableExpression<Record2<Long, BigDecimal>> toToFosterSum = DSL.name("ttf").as(
            DSL.select(x.field("f___id", Long.class), sum(nestedFostering.AMOUNT).as("amount")).from(nestedFostering).join(x)
                .on(nestedFostering.TO_SERVING_ID.eq(x.field("f___to_serving_id", fostering.TO_SERVING_ID.getDataType())))
                .and(nestedFostering.ACTOR_DATE.lt(x.field("f___actor_date", fostering.ACTOR_DATE.getDataType())))
                .groupBy(x.field("f___id"))
        );

        Field<Integer> fromSowPiglets = field("coalesce(x.fi___liveborn, 0) - coalesce(fd.amount, 0) - coalesce(fw.amount, 0) - coalesce(fff.amount, 0) + coalesce(ftf.amount, 0)", Integer.class).as("f___fromSucklings");
        Field<Integer> toSowPiglets = field("coalesce(x.ti___liveborn, 0) - coalesce(td.amount, 0) - coalesce(tw.amount, 0) - coalesce(tff.amount, 0) + coalesce(ttf.amount, 0)", Integer.class).as("f___toSucklings");


        final Select<Record> select = DSL
            .with(x)
            .with(fromDeadSum)
            .with(toDeadSum)
            .with(fromWeanSum)
            .with(toWeanSum)
            .with(fromFromFosterSum)
            .with(fromToFosterSum)
            .with(toFromFosterSum)
            .with(toToFosterSum)
            .select(x.fields())
            .select(fromSowPiglets, toSowPiglets)
            .from(x)
            .leftOuterJoin(fromDeadSum).on(x.field("f___id", Long.class).eq(fromDeadSum.field("f___id", Long.class)))
            .leftOuterJoin(fromWeanSum).on(x.field("f___id", Long.class).eq(fromWeanSum.field("f___id", Long.class)))
            .leftOuterJoin(fromFromFosterSum).on(x.field("f___id", Long.class).eq(fromFromFosterSum.field("f___id", Long.class)))
            .leftOuterJoin(fromToFosterSum).on(x.field("f___id", Long.class).eq(fromToFosterSum.field("f___id", Long.class)))
            .leftOuterJoin(toDeadSum).on(x.field("f___id", Long.class).eq(toDeadSum.field("f___id", Long.class)))
            .leftOuterJoin(toWeanSum).on(x.field("f___id", Long.class).eq(toWeanSum.field("f___id", Long.class)))
            .leftOuterJoin(toFromFosterSum).on(x.field("f___id", Long.class).eq(toFromFosterSum.field("f___id", Long.class)))
            .leftOuterJoin(toToFosterSum).on(x.field("f___id", Long.class).eq(toToFosterSum.field("f___id", Long.class)))
            .orderBy(order(order, SCREEN_ORDER_X, x.field("f___id").desc()));

        return select;
    }

    private SelectConditionStep<Record1<BigDecimal>> prepareStatisticsQuery(ApplicationUser user, FosteringFilters filters) {
        List<Condition> conditions = new ArrayList<>();
        final jooq.farm.tables.Fostering fostering = Tables.FOSTERING.as(ALIAS_FOSTERING);
        final jooq.farm.tables.Serving fromServing = Tables.SERVING.as(ALIAS_FROM_SERVING);
        final jooq.farm.tables.Serving toServing = Tables.SERVING.as(ALIAS_TO_SERVING);
        final jooq.farm.tables.Sow fromSow = Tables.SOW.as(ALIAS_FROM_SOW);
        final jooq.farm.tables.Sow toSow = Tables.SOW.as(ALIAS_TO_SOW);
        final jooq.farm.tables.Gilt gilt = Tables.GILT.as("g");

        applyConditions(user, conditions, filters, fostering, fromServing, toServing, fromSow, toSow, gilt);

        final SelectConditionStep<Record1<BigDecimal>> query = DSL
            .select(sum(fostering.AMOUNT).as("amountSum"))
            .from(fostering)
            .join(fromServing).on(fromServing.ID.equal(fostering.FROM_SERVING_ID))
            .join(toServing).on(toServing.ID.equal(fostering.TO_SERVING_ID))
            .join(fromSow).on(fromSow.ID.equal(fromServing.SOW_ID))
            .join(toSow).on(toSow.ID.equal(toServing.SOW_ID))
            .leftOuterJoin(gilt).on(gilt.ID.eq(fostering.GILT_ID))
            .where(conditions);

        return query;
    }


    private void applyConditions(ApplicationUser user, List<Condition> conditions, FosteringFilters filters, jooq.farm.tables.Fostering fostering, jooq.farm.tables.Serving fromServing, jooq.farm.tables.Serving toServing, jooq.farm.tables.Sow fromSow, jooq.farm.tables.Sow toSow, jooq.farm.tables.Gilt gilt) {
        cfFilters.animalName(conditions, filters.fromServing_sow_sowNumber, fromSow.SOWNUMBER);
        cfFilters.text(conditions, filters.fromServing_sow_animalId, fromSow.ANIMALID);
        cfFilters.text(conditions, filters.toServing_sow_animalId, toSow.ANIMALID);
        cfFilters.text(conditions, filters.fromServing_sow_breed, fromSow.BREED);
        cfFilters.filterLocation(user, conditions, filters.fromLocationId, fostering.FROM_LOCATION_ID);

        cfFilters.animalName(conditions, filters.toServing_sow_sowNumber, toSow.SOWNUMBER);
        cfFilters.text(conditions, filters.toServing_sow_breed, toSow.BREED);
        cfFilters.filterLocation(user, conditions, filters.toLocationId, fostering.TO_LOCATION_ID);

        cfFilters.longNumber(conditions, filters.createAccountId, fostering.CREATE_ACCOUNT_ID);
        cfFilters.longNumber(conditions, filters.updateAccountId, fostering.UPDATE_ACCOUNT_ID);

        cfFilters.shortInterval(conditions, filters.amount, fostering.AMOUNT, true);

        cfFilters.text(conditions, filters.comment, fostering.COMMENT);
        cfFilters.longNumber(conditions, filters.actorId, fostering.ACTOR_ID);
        cfFilters.dateInterval(user, conditions, filters.actorDate, fostering.ACTOR_DATE);
        cfFilters.dateInterval(user, conditions, filters.createDate, fostering.CREATE_DATE);
        cfFilters.dateInterval(user, conditions, filters.updateDate, fostering.UPDATE_DATE);
        cfFilters.dateInterval(user, conditions, filters.actorDate, fostering.ACTOR_DATE);
        cfFilters.ui(conditions, filters.createUi, fostering.CREATE_UI);

        cfFilters.text(conditions, filters.gilt_animalId, gilt.ANIMALID);
    }


    @WithTenant({@Group(farm = FarmRole.weaningWrite)})
    public Result updateBatch(Http.Request request) throws Exception {
        JsonNode json = request.body().asJson();
        if (!json.isObject())
            throw new RuntimeException("Expected an object with fosternings");
        ObjectNode jsonDict = (ObjectNode) json;
        Map<Long, PartialResponse> responseDict = new HashMap<>();
        Iterator<Map.Entry<String, JsonNode>> i = jsonDict.fields();
        final Messages messages = messagesApi.preferred(request);
        final ApplicationUser user = applicationUserHandler.current(request);
        return withEm(request, em -> {
            while (i.hasNext()) {
                Map.Entry<String, JsonNode> e = i.next();
                Long id;
                try {
                    id = Long.parseLong(e.getKey());
                } catch (NumberFormatException nfe) {
                    return status(INTERNAL_SERVER_ERROR, nfe.getMessage());
                }

                ObjectNode parsed = (ObjectNode) e.getValue();
                if (parsed.get("actorDate").asLong() < 0) {
                    return forbidden();
                }

                HashSet<String> acceptedWarnings = Utils.getAcceptedWarnings(parsed);

                final FosteringReportingDTO dto = dtoFromObjectNode(parsed, id);
                final EntityErrors.With<Fostering> errorsWithEvent = fosteringHandler.processFostering(new UnitOfWork.Web(messages, em, user), dto, acceptedWarnings);
                if (errorsWithEvent.errors.hasErrors()) {
                    responseDict.put(id, new PartialResponse(FORBIDDEN, errorsWithEvent.errors));
                } else {
                    responseDict.put(id, new PartialResponse(OK, errorsWithEvent.it));
                }
            }

            return ok(Utils.toJson(responseDict));
        });
    }

    private static FosteringReportingDTO dtoFromObjectNode(ObjectNode fosteringNode, Long id) {
        FosteringReportingDTO dto = new FosteringReportingDTO();
        Fostering fromJson = fromJson(fosteringNode, Fostering.class);
        dto.setId(id);
        dto.setWhen(fromJson.getActorDate());
        dto.setFromSowNumber(Utils.getTextProperty(fosteringNode.path("fromServing").path("sow"), "sowNumber"));
        dto.setToSowNumber(Utils.getTextProperty(fosteringNode.path("toServing").path("sow"), "sowNumber"));
        dto.setFromLocationId(fromJson.getFromLocationId());
        dto.setToLocationId(fromJson.getToLocationId());
        dto.setComment(fromJson.getComment());
        dto.setAmount(fromJson.getAmount());
        dto.setGiltAnimalId(Utils.getTextProperty(fosteringNode.path("gilt"), "animalId"));
        Long actorId = fromJson.getActorId();
        dto.setUserId(actorId == null ? 0 : actorId);
        return dto;
    }

    @WithTenant({@Group(farm = FarmRole.weaningRead), @Group(farm = FarmRole.weaningWrite)})
    public Result read(Long id, play.mvc.Http.Request request) {
        return read(request, Fostering.class, id);
    }

    @WithTenant({@Group(farm = FarmRole.weaningWrite)})
    public Result delete(Long id, Http.Request request) throws Throwable {
        return doWithPG(request, (EntityManager em) -> {
            Fostering w = em.find(Fostering.class, id);
            if (w == null) {
                return notFound();
            }
            em.remove(w);
            em.flush();
            return ok(Utils.toJson(w));
        });
    }

    public static class FosteringFilters {
        private String fromLocationId;
        private String toLocationId;
        private String amount;
        private CfDateInterval updateDate;
        private CfDateInterval actorDate;
        private String fromServing_sow_sowNumber;
        private String fromServing_sow_animalId;
        private String fromServing_sow_breed;
        private String toServing_sow_sowNumber;
        private String toServing_sow_animalId;
        private String toServing_sow_breed;
        private String gilt_animalId;

        private String createAccountId;
        private String updateAccountId;
        private String createUi;
        private String actorId;
        private String comment;
        private CfDateInterval createDate;

        public String getFromServing_sow_animalId() {
            return fromServing_sow_animalId;
        }

        public void setFromServing_sow_animalId(String fromServing_sow_animalId) {
            this.fromServing_sow_animalId = fromServing_sow_animalId;
        }

        public String getToServing_sow_animalId() {
            return toServing_sow_animalId;
        }

        public void setToServing_sow_animalId(String toServing_sow_animalId) {
            this.toServing_sow_animalId = toServing_sow_animalId;
        }

        public String getFromLocationId() {
            return fromLocationId;
        }

        public void setFromLocationId(String fromLocationId) {
            this.fromLocationId = fromLocationId;
        }

        public String getToLocationId() {
            return toLocationId;
        }

        public void setToLocationId(String toLocationId) {
            this.toLocationId = toLocationId;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }

        public CfDateInterval getUpdateDate() {
            return updateDate;
        }

        public void setUpdateDate(CfDateInterval updateDate) {
            this.updateDate = updateDate;
        }

        public CfDateInterval getActorDate() {
            return actorDate;
        }

        public void setActorDate(CfDateInterval actorDate) {
            this.actorDate = actorDate;
        }

        public String getFromServing_sow_sowNumber() {
            return fromServing_sow_sowNumber;
        }

        public void setFromServing_sow_sowNumber(String fromServing_sow_sowNumber) {
            this.fromServing_sow_sowNumber = fromServing_sow_sowNumber;
        }

        public String getFromServing_sow_breed() {
            return fromServing_sow_breed;
        }

        public void setFromServing_sow_breed(String fromServing_sow_breed) {
            this.fromServing_sow_breed = fromServing_sow_breed;
        }

        public String getToServing_sow_sowNumber() {
            return toServing_sow_sowNumber;
        }

        public void setToServing_sow_sowNumber(String toServing_sow_sowNumber) {
            this.toServing_sow_sowNumber = toServing_sow_sowNumber;
        }

        public String getToServing_sow_breed() {
            return toServing_sow_breed;
        }

        public void setToServing_sow_breed(String toServing_sow_breed) {
            this.toServing_sow_breed = toServing_sow_breed;
        }

        public String getCreateAccountId() {
            return createAccountId;
        }

        public void setCreateAccountId(String createAccountId) {
            this.createAccountId = createAccountId;
        }

        public String getUpdateAccountId() {
            return updateAccountId;
        }

        public void setUpdateAccountId(String updateAccountId) {
            this.updateAccountId = updateAccountId;
        }

        public String getCreateUi() {
            return createUi;
        }

        public void setCreateUi(String createUi) {
            this.createUi = createUi;
        }

        public String getActorId() {
            return actorId;
        }

        public void setActorId(String actorId) {
            this.actorId = actorId;
        }

        public String getComment() {
            return comment;
        }

        public void setComment(String comment) {
            this.comment = comment;
        }

        public CfDateInterval getCreateDate() {
            return createDate;
        }

        public void setCreateDate(CfDateInterval createDate) {
            this.createDate = createDate;
        }

        public String getGilt_animalId() {
            return gilt_animalId;
        }

        public void setGilt_animalId(String gilt_animalId) {
            this.gilt_animalId = gilt_animalId;
        }
    }

}
