package controllers;

import jakarta.persistence.EntityManager;

import static controllers.GridColumn.ColumnType.*;
import static controllers.GridColumn.Filter.boolean3;
import static jooq.public_.tables.BenchmarkPersonaKpi.BENCHMARK_PERSONA_KPI;
public class BenchmarkKpiColumns extends ScreenColumns<Object> {
     public BenchmarkKpiColumns(EntityManager em) {
        addCol(col("assign").type(bool).filter(boolean3("js.label.boolean")).width(15).cannotTriggerInsert(true).sortable(true).isStartingColumn(true));
        addCol(col("name").name("js.label.kpi").type(text).width(25).sortable(true));
        addCol(col("inhabitant").name("js.label.type").type(text).width(25).sortable(true));
        addCol(col(BENCHMARK_PERSONA_KPI.CREATE_ACCOUNT_ID).name("js.label.createaccountid").template(em, GridColumn.ColumnTemplate.createAccountId));
        addCol(col(BENCHMARK_PERSONA_KPI.CREATE_DATE).name("js.label.createdate").template(em, GridColumn.ColumnTemplate.createDate));
    }
}
