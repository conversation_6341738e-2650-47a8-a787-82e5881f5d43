package controllers;

import com.cloudfarms.pigs.sync.entities.TestResult;
import models.BreedingAnimal;
import models.ForSlaughterType;
import models.GiltState;
import models.handlers.SettingsHandler;
import play.i18n.Messages;
import security.*;

import jakarta.persistence.EntityManager;

import static controllers.GridColumn.Align.*;
import static controllers.GridColumn.ColumnType.*;
import static controllers.GridColumn.Editor.*;
import static controllers.GridColumn.Filter.*;
import static controllers.GridColumn.Formatter.*;
import static controllers.GridColumn.Validator.mandatory;

public class BreedingAnimalColumns extends ScreenColumns<BreedingAnimal> {

    public BreedingAnimalColumns(final Messages messages, final ApplicationUser user, EntityManager em, SettingsHandler settingsHandler, FlexFieldColumns flexFieldColumns) {
        addCol(col("$$$selected").template(em, GridColumn.ColumnTemplate.selector));
        addCol(col("sowNumber").type(text).editor(maxLength(30)).width(13).sortable(true));
        if (user.hasModule(CloudfarmsModule.genesus)) {
            addCol(col("testStart").type(date).optional(true).sortable(true).filter(filterDateInterval));
            addCol(col("genesusMisc.testStartLocationId").name("js.label.genesusMisc.testStartLocation").formatter(formatLocation(em)).width(12).align(right).editor(locations("['ALL']", LocationUsageType.individuals, "testStart")).filter(filterLocation).filterValue("null").sortable(false));
            addCol(col("genesusMisc.testEndLocationId").name("js.label.genesusMisc.testEndLocation").formatter(formatLocation(em)).width(12).align(right).editor(locations("['ALL']", LocationUsageType.individuals, "testedUntil")).filter(filterLocation).filterValue("null").sortable(false));

            addCol(col("testedUntil").type(date).optional(true).sortable(true).filter(filterDateInterval));
            addCol(col("testStartWeight").localizedName(messages.apply("js.label.teststartweight.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
                .type(number).editor(weightKg(3, 6)).formatter(formatWeightKg(3)).filter(filterWeightKg).sortable(true));
            addCol(col("testEndWeight").localizedName(messages.apply("js.label.testendweight.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
                .type(number).editor(weightKg(3, 6)).formatter(formatWeightKg(3)).filter(filterWeightKg).sortable(true));

            addCol(col("genesusMisc.weaningDate").type(date).name("js.label.genesusMisc.weaningDate").optional(true).sortable(true).filter(filterDateInterval));
            addCol(col("genesusMisc.weaningWeight").localizedName(messages.apply("js.label.genesusMisc.weaningWeight.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
                .type(number).editor(weightKg(3, 6)).formatter(formatWeightKg(3)).filter(filterWeightKg).sortable(true));
            addCol(col("genesusMisc.weaningLocationId").name("js.label.genesusMisc.weaningLocation").formatter(formatLocation(em)).width(12).align(right).editor(locations("['ALL']", LocationUsageType.individuals, "genesusMisc.weaningDate")).filter(filterLocation).filterValue("null").sortable(false));

            addCol(col("genesusMisc.midDate").type(date).name("js.label.genesusMisc.midDate").optional(true).sortable(true).filter(filterDateInterval));
            addCol(col("genesusMisc.midWeight").localizedName(messages.apply("js.label.genesusMisc.midWeight.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
                .type(number).editor(weightKg(3, 6)).formatter(formatWeightKg(3)).filter(filterWeightKg).sortable(true));
            addCol(col("genesusMisc.midLocationId").name("js.label.genesusMisc.midLocation").formatter(formatLocation(em)).width(12).align(right).editor(locations("['ALL']", LocationUsageType.individuals, "genesusMisc.midDate")).filter(filterLocation).filterValue("null").sortable(false));

            addCol(col("genesusMisc.feedIntakeStartDate").type(date).name("js.label.genesusMisc.feedIntakeStartDate").optional(true).sortable(true).filter(filterDateInterval));
            addCol(col("genesusMisc.feedIntakeStartWeight").localizedName(messages.apply("js.label.genesusMisc.feedIntakeStartWeight.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
                .type(number).editor(weightKg(3, 6)).formatter(formatWeightKg(3)).filter(filterWeightKg).sortable(true));
            addCol(col("genesusMisc.feedIntakeStartLocationId").name("js.label.genesusMisc.feedIntakeStartLocation").formatter(formatLocation(em)).width(12).align(right).editor(locations("['ALL']", LocationUsageType.individuals, "genesusMisc.feedIntakeStartDate")).filter(filterLocation).filterValue("false").sortable(false));

            addCol(col("genesusMisc.feedIntakeEndDate").type(date).name("js.label.genesusMisc.feedIntakeEndDate").optional(true).sortable(true).filter(filterDateInterval));
            addCol(col("genesusMisc.feedIntakeEndWeight").localizedName(messages.apply("js.label.genesusMisc.feedIntakeEndWeight.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
                .type(number).editor(weightKg(3, 6)).formatter(formatWeightKg(3)).filter(filterWeightKg).sortable(true));
            addCol(col("genesusMisc.feedIntakeEndLocationId").name("js.label.genesusMisc.feedIntakeEndLocation").formatter(formatLocation(em)).width(12).align(right).editor(locations("['ALL']", LocationUsageType.individuals, "genesusMisc.feedIntakeEndDate")).filter(filterLocation).filterValue("null").sortable(false));

            addCol(col("genesusMisc.miscellaneousDate").type(date).name("js.label.genesusMisc.miscDate").optional(true).sortable(true).filter(filterDateInterval));
            addCol(col("genesusMisc.miscellaneousWeight").localizedName(messages.apply("js.label.genesusMisc.miscWeight.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
                .type(number).editor(weightKg(3, 6)).formatter(formatWeightKg(3)).filter(filterWeightKg).sortable(true));
            addCol(col("genesusMisc.miscellaneousLocationId").name("js.label.genesusMisc.miscLocation").formatter(formatLocation(em)).width(12).align(right).editor(locations("['ALL']", LocationUsageType.individuals, "miscellaneousDate")).filter(filterLocation).filterValue("null").sortable(false));
            addCol(col("genesusMisc.miscellaneousDescription").type(text).name("js.label.genesusMisc.miscDescription").editor(maxLength(300)).width(13).sortable(true).optional(true));
        }
        addCol(col("animalId").type(text).editor(maxLength(30)).width(13).validator(mandatory).sortable(true));
        addCol(col("externalName").type(text).editor(maxLength(30)).width(13).sortable(true).optional(true));
        addCol(col("earTagNumber").type(text).editor(maxLength(30)).width(11).sortable(true).filter(filterText).cannotTriggerInsert(true));
        addCol(col("smallNumber").type(text).editor(maxLength(30)).width(7).sortable(true).filter(filterText).cannotTriggerInsert(true));
        addCol(col("farmNumber").type(text).editor(maxLength(30)).width(7).sortable(true).filter(filterText).cannotTriggerInsert(true));
        addCol(col("pigCentralRn").type(text).editor(maxLength(30)).width(12).sortable(true));
        addCol(col("giltDate").type(dateTime).sortable(true).filter(filterDateInterval).cannotTriggerInsert(true).optional(true));
        if(supportsInhabitants(settingsHandler.getFarmType(user), "SOW")) {
            addCol(col("heatDate").type(dateTime).sortable(true).filter(filterDateInterval).cannotTriggerInsert(true).optional(true));
            addCol(col("secondHeat").type(dateTime).sortable(true).filter(filterDateInterval).cannotTriggerInsert(true).optional(true));
            addCol(col("thirdHeat").type(dateTime).sortable(true).filter(filterDateInterval).cannotTriggerInsert(true).optional(true));
        }
        addCol(col("birthDate").type(date).sortable(true).filter(filterDateInterval).width(11).cannotTriggerInsert(true));
        addCol(col("breedInfo.useCode").name("js.label.usecode").type(number).editor(null).width(9).optional(true));
        addCol(col("ageDays").type(number).name("js.label.age.days").formatter(formatDaysSince("birthDate")).editor(null).width(12).sortable(true));
        addCol(col("ageWeeks").type(number).name("js.label.age.weeks").formatter(formatAgeWeeksSince("birthDate", settingsHandler)).editor(null).width(12).sortable(true));
        addCol(col("locationId").name("js.label.location").formatter(formatLocation(em)).width(12).align(right).cannotTriggerInsert(true).editor(locations("['ALL']", LocationUsageType.individuals, "giltDate")).filter(filterLocation).filterValue("null").sortable(true));
        addCol(col("lastSeen").type(dateTime).editor(null).cannotTriggerInsert(true).sortable(true));
        addCol(col("quarantine").type(bool).sortable(true).cannotTriggerInsert(true).width(11).filter(boolean3("js.label.quarantine")));
        addCol(col("breed").type(text).editor(breedEditor).width(6).sortable(true).cannotTriggerInsert(true));
        addCol(col("active").type(bool).sortable(true).cannotTriggerInsert(true).width(8).editor(null).filter(boolean3("js.label.active")).filterValue("'true'"));
        addCol(col("ownUse").type(bool).sortable(false).cannotTriggerInsert(true).width(8).filter(boolean3()).optional(true));
        addCol(col("forSlaughterFlag").type(bool).sortable(false).cannotTriggerInsert(true).width(8).filter(boolean3()).optional(true));
        addCol(col("forSale").type(bool).sortable(false).cannotTriggerInsert(true).width(8).filter(boolean3()).optional(true));
        addCol(col("sold").type(bool).sortable(false).cannotTriggerInsert(true).width(8).editor(null).filter(boolean3()));
        addCol(col("soldTo").type(text).sortable(true).cannotTriggerInsert(true).width(12).editor(null).optional(true).filter(filterText));
        addCol(col("deliveredTo").type(text).sortable(true).cannotTriggerInsert(true).width(12).editor(null).optional(true).filter(filterText));
        addCol(col("deliveredToCountryName").type(text).name("js.label.address.country.name").sortable(true).cannotTriggerInsert(true).width(12).editor(null).optional(true).filter(filterText));
        addCol(col("soldDate").type(date).sortable(true).cannotTriggerInsert(true).width(8).editor(null).optional(true).filter(filterDateInterval));
        addCol(col("state").type(text).formatter(formatEnum("giltstate")).width(8).cannotTriggerInsert(true).optional(true).filter(filterEnum("giltstate", GiltState.class)));
        addCol(col("sex").type(text).editor(sexEditor).width(6).align(center).sortable(true).formatter(formatSex).filter(filterSex).cannotTriggerInsert(true));
        addCol(col("forSlaughter").type(date).filter(filterDateInterval).sortable(true).cannotTriggerInsert(true).optional(true));
        addCol(col("forSlaughterTypeCode").name("js.label.forslaughtertype").editor(codesAutoComplete(ForSlaughterType.TYPE_NAME)).formatter(formatCodeTypes(em, ForSlaughterType.TYPE_NAME)).width(10).filter(filterText).sortable(true).cannotTriggerInsert(true).optional(true));
        addCol(col("castrated").type(bool).filter(boolean3()).optional(true));
        addCol(col("rfidLf").type(text).editor(maxLengthEmptyAsNull(30)).width(15).filter(filterText).sortable(true).optional(true));
        addCol(col("rfidUhf").type(text).editor(maxLength(30)).width(15).filter(filterText).sortable(true).optional(true));
        addCol(col("nationalId").type(text).width(15).editor(maxLength(30)).optional(true));
        addCol(col("nationalIdMarkingDate").type(date).optional(true));
        if (user.hasModule(CloudfarmsModule.abcs)) {
            addCol(col("pbb").type(text).width(15).editor(null).optional(true));
        }
        addCol(col("birthWeight").localizedName(messages.apply("js.label.birthweights.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
            .type(number).editor(weightKg(3, 6)).formatter(formatWeightKg(3)).filter(filterWeightKg).sortable(true).cannotTriggerInsert(true));
        addCol(col("observation").type(bool).sortable(true).cannotTriggerInsert(true).width(8).filter(boolean3()).optional(true));
        addCol(col("testResult").type(text).editor(null).width(6).align(left).formatter(formatEnum("testresult")).filter(filterIndex("testresult", TestResult.class)));
        if (!user.hasModule(CloudfarmsModule.genesus)) {
            addCol(col("testedUntil").type(date).width(10).align(left).editor(null));
            addCol(col("testStartWeight").localizedName(messages.apply("js.label.teststartweight.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
                .type(number).formatter(formatWeightKg(3)).width(7).editor(null));
            addCol(col("testEndWeight").localizedName(messages.apply("js.label.testendweight.uw", messages.apply("js.label.weight.unit." + settingsHandler.getFarmWeightUnit(user))))
                .type(number).formatter(formatWeightKg(3)).filter(filterWeightKg).width(10).editor(null));
        }
//        addCol(col("feedConversionRate").name("js.label.pigtesting.feedconversionrate").formatter(formatDecimalPlaces(3)).type(number).width(7).editor(null));
//        addCol(col("dailyWeightGain").name("js.label.pigtesting.dailyweightgain").type(number).width(7).editor(null));
//        addCol(col("lastFeedIntake").name("js.label.pigtesting.lastfeedintake").type(number).width(7).editor(null));
//        addCol(col("firstFeedTime").name("js.label.pigtesting.firstfeedtime").type(dateTime).width(10).align(left).editor(null));
//        addCol(col("lastFeedTime").name("js.label.pigtesting.lastfeedtime").type(dateTime).width(10).align(left).editor(null));
//        addCol(col("lastBackFat").name("js.label.backfat").type(number).editor(null).sortable(true).formatter(formatDecimalPlaces(0)).optional(true));
//        addCol(col("lastBackFatDate").name("js.label.backfat.date").type(dateTime).editor(null).sortable(true).optional(true));
        flexFieldColumns.addFlexFields(em, "", this::addCol);
        addCol(col("breedInfo.teats").type(number).width(7).align(right).sortable(true).filter(filterNumber).name("js.label.teats").editor(decimalPlaces(0)).formatter(formatDecimalPlaces(0)).cannotTriggerInsert(true));
        addCol(col("breedInfo.teatsLeft").type(number).width(7).align(right).sortable(true).filter(filterNumber).name("js.label.teatsleft").editor(decimalPlaces(0)).formatter(formatDecimalPlaces(0)).cannotTriggerInsert(true).optional(true));
        addCol(col("breedInfo.teatsRight").type(number).width(7).align(right).sortable(true).filter(filterNumber).name("js.label.teatsright").editor(decimalPlaces(0)).formatter(formatDecimalPlaces(0)).cannotTriggerInsert(true).optional(true));
        addCol(col("breedInfo.index").type(number).editor(decimalPlaces(0)).width(8).align(right).sortable(true).filter(filterNumber).name("js.label.index").cannotTriggerInsert(true).aggAvg());
        addCol(col("breedInfo.danavlFarmNumber").name("js.label.danavlfarmnumber").type(text).editor(maxLengthReadOnly(100)).width(15).align(left).cannotTriggerInsert(true).sortable(true).filter(filterText));
        addCol(col("breedInfo.danavlName").name("js.label.danavlname").type(text).editor(maxLengthReadOnly(100)).width(12).align(left).cannotTriggerInsert(true).sortable(true).filter(filterText));
        addCol(col("breedInfo.damId").type(text).formatter(linkFormatter("SowColumns", "sows", "animalId", "")).editor(maxLength(30)).sortable(true).width(12).align(left).filter(filterText).name("js.label.damid").cannotTriggerInsert(true));
        addCol(col("breedInfo.damNumber").type(text).editor(maxLength(30)).sortable(true).width(8).align(left).filter(filterText).name("js.label.damnumber").cannotTriggerInsert(true));
        addCol(col("breedInfo.damBreed").type(text).editor(maxLength(30)).sortable(true).width(9).align(left).filter(filterText).name("js.label.dambreed").cannotTriggerInsert(true));
        addCol(col("breedInfo.femaleIndex").width(8).align(right).sortable(true).filter(filterText).name("js.label.femaleindex").cannotTriggerInsert(true).aggAvg());
        addCol(col("breedInfo.sireId").type(text).formatter(linkFormatter("BoarsColumns", "boars", "animalId", "")).editor(maxLength(30)).sortable(true).width(12).align(left).filter(filterText).name("js.label.sireid").cannotTriggerInsert(true));
        addCol(col("breedInfo.sireNumber").editor(maxLength(30)).sortable(true).type(text).width(8).align(left).filter(filterText).name("js.label.sirenumber").cannotTriggerInsert(true));
        addCol(col("breedInfo.sireBreed").editor(maxLength(30)).sortable(true).type(text).width(8).align(left).filter(filterText).name("js.label.sirebreed").cannotTriggerInsert(true));
        addCol(col("breedInfo.sireLine").type(text).editor(maxLength(30)).width(8).align(left).name("js.label.sireline").filter(filterText).sortable(true));
        addCol(col("breedInfo.damLine").type(text).editor(maxLength(30)).width(8).align(left).name("js.label.damline").filter(filterText).sortable(true));
        addCol(col("breedInfo.maleIndex").width(8).align(right).sortable(true).filter(filterText).name("js.label.maleindex").cannotTriggerInsert(true).aggAvg());
        addCol(col("breedInfo.lastIndexUpdateDate").name("js.label.lastindexupdatedate").type(date).sortable(true).width(13));
        addCol(col("breedInfo.lastIndexDownloadDate").name("js.label.lastindexdownloaddate").type(timestamp).editor(null).sortable(true).width(13));
        addCol(col("breedInfo.entryDate").type(date).name("js.label.entrydate").sortable(true).width(13));
        addCol(col("breedInfo.exitDate").type(date).editor(null).name("js.label.exitdate").sortable(true).width(13));
        addCol(col("breedInfo.dailyGainSmall").type(text).editor(null).name("js.label.dailygainsmall").sortable(true).aggAvg());
        addCol(col("breedInfo.dailyGainLarge").type(text).editor(null).name("js.label.dailygainlarge").sortable(true).aggAvg());
        addCol(col("breedInfo.feedUnits").type(text).editor(null).name("js.label.feedunits").sortable(true).aggAvg());
        addCol(col("breedInfo.leanMeatPercentage").type(text).editor(null).name("js.label.leanmeatpercentage").sortable(true).aggAvg());
        addCol(col("breedInfo.litterSize").type(text).editor(null).name("js.label.littersize").sortable(true).aggAvg());
        addCol(col("breedInfo.sustainability").type(text).editor(null).name("js.label.sustainability").sortable(true).aggAvg());
        addCol(col("breedInfo.backfat").width(11).type(text).align(right).editor(null).name("js.label.backfat").sortable(true));
        addCol(col("breedInfo.lp5").width(11).type(text).align(right).editor(null).name("js.label.lp5").sortable(true));
        addCol(col("breedInfo.slaughterLoss").type(text).editor(null).name("js.label.slaughterloss").sortable(true).aggAvg());
        addCol(col("breedInfo.strength").type(text).editor(null).name("js.label.strength").sortable(true).aggAvg());
        addCol(col("breedInfo.exitType").type(text).editor(null).name("js.label.exittype").sortable(true));
        addCol(col("breedInfo.exitCode").type(text).editor(null).name("js.label.exitcode").sortable(true));
        addCol(col("breedInfo.f4date").type(date).editor(null).name("js.label.f4date").sortable(true).width(13).optional(true));
        addCol(col("breedInfo.f4state").type(text).editor(null).name("js.label.f4state").sortable(true).optional(true));
        addCol(col("breedInfo.oldAnimalId").type(text).editor(null).name("js.label.oldanimalid").sortable(true).optional(true));
//        // Special temporay columns for Rønshauge
//        if (user.hasModule(CloudfarmsModule.danGen)) {
//            addCol(col("breedInfoBak.index").type(number).editor(null).width(8).align(right).sortable(true).filter(filterNumber).name("js.label.index.danbred").cannotTriggerInsert(true).aggAvg().optional(true));
//            addCol(col("breedInfoBak.dailyGainSmall").type(text).editor(null).name("js.label.dailygainsmall.danbred").sortable(true).aggAvg().optional(true));
//            addCol(col("breedInfoBak.dailyGainLarge").type(text).editor(null).name("js.label.dailygainlarge.danbred").sortable(true).aggAvg().optional(true));
//            addCol(col("breedInfoBak.feedUnits").type(text).editor(null).name("js.label.feedunits.danbred").sortable(true).aggAvg().optional(true));
//            addCol(col("breedInfoBak.leanMeatPercentage").type(text).editor(null).name("js.label.leanmeatpercentage.danbred").sortable(true).aggAvg().optional(true));
//            addCol(col("breedInfoBak.litterSize").type(text).editor(null).name("js.label.littersize.danbred").sortable(true).aggAvg().optional(true));
//            addCol(col("breedInfoBak.sustainability").type(text).editor(null).name("js.label.sustainability.danbred").sortable(true).aggAvg().optional(true));
//            addCol(col("breedInfoBak.slaughterLoss").type(text).editor(null).name("js.label.slaughterloss.danbred").sortable(true).aggAvg().optional(true));
//            addCol(col("breedInfoBak.strength").type(text).editor(null).name("js.label.strength.danbred").sortable(true).aggAvg().optional(true));
//        }
        if (user.hasModule(CloudfarmsModule.danGen)) {
            addCol(col("breedInfo.dailyGainSmallInTest").type(number).formatter(formatDecimalPlaces(1)).editor(null).name("js.label.dailygainsmallintest").sortable(false).filter(null));
            addCol(col("breedInfo.dailyGainLargeInTest").type(number).formatter(formatDecimalPlaces(1)).editor(null).name("js.label.dailygainlargeintest").sortable(false).filter(null));
            addCol(col("breedInfo.lp1").type(number).formatter(formatDecimalPlaces(2)).editor(null).name("js.label.lp1").sortable(true).aggAvg());
            addCol(col("breedInfo.pigletSurvival").type(number).formatter(formatDecimalPlaces(1)).editor(null).name("js.label.pigletsurvival").sortable(true).aggAvg());
            addCol(col("breedInfo.longevity").type(number).formatter(formatDecimalPlaces(2)).editor(null).name("js.label.longevity").sortable(true).aggAvg());
            addCol(col("breedInfo.boarFertility").type(number).formatter(formatDecimalPlaces(2)).editor(null).name("js.label.boarfertility").sortable(true).aggAvg());
            // addCol(col("breedInfo.leanMeatInTest").type(number).formatter(formatDecimalPlaces(1)).editor(null).name("js.label.leanmeatpercentageintest").sortable(false).filter(null));
        }
//        if (false && /* WAS TMP FOR DanGen */ (user.getRoles().contains(FarmRole.admin) || user.getRoles().contains(FarmRole.danishGeneticsRead)) && user.hasModule(CloudfarmsModule.danGen)) {
//            if ((settingsHandler.getFarmType(user) >= 1 && user.getFarm().countryCode().contains("DK") || settingsHandler.getFarmType(user) == 3)) {
//                addCol(col("breedInfo.dgi_adg_large").type(number).formatter(formatDecimalPlaces(4)).editor(null).sortable(false).filter(null).optional(true));
//                addCol(col("breedInfo.dgi_adg_small_dir").type(number).formatter(formatDecimalPlaces(4)).editor(null).sortable(false).filter(null).optional(true));
//                addCol(col("breedInfo.dgi_adg_small_mat").type(number).formatter(formatDecimalPlaces(4)).editor(null).sortable(false).filter(null).optional(true));
//                addCol(col("breedInfo.dgi_backfat").type(number).formatter(formatDecimalPlaces(4)).editor(null).sortable(false).filter(null).optional(true));
//                // addCol(col("breedInfo.dgi_logevity").type(number).formatter(formatDecimalPlaces(4)).editor(null).sortable(false).filter(null).optional(true));
//                addCol(col("breedInfo.dgi_lp5").type(number).formatter(formatDecimalPlaces(4)).editor(null).sortable(false).filter(null).optional(true));
//            }
//            addCol(col("breedInfo.dgi_index_scaled").type(number).formatter(formatDecimalPlaces(0)).editor(null).sortable(false).filter(null).optional(true));
//            // addCol(col("breedInfo.dgi_male_fertility").type(number).formatter(formatDecimalPlaces(4)).editor(null).sortable(false).filter(null).optional(true));
//        }
        if (user.hasModule(CloudfarmsModule.breeding) && (settingsHandler.getFarmType(user) == 2 || settingsHandler.getFarmType(user) == 3 /* Breeder or AI*/)) {
            addCol(col("breedInfo.dnaTestId").type(text).editor(maxLength(30)).name("js.label.gentest.id").sortable(true).filter(filterText).optional(true).cannotTriggerInsert(true));
            addCol(col("breedInfo.dnaTestActorId").width(10).name("js.label.gentest.who").editor(colleagueEditor).formatter(formatColleague(em)).filter(filterColleague).cannotTriggerInsert(true).sortable(true));
            addCol(col("breedInfo.dnaTestDate").type(dateTime).name("js.label.gentest.date").sortable(true).cannotTriggerInsert(true).validator(mandatory).filter(filterDateInterval).cannotTriggerInsert(true));
        }
        if (user.hasModule(CloudfarmsModule.danavl) && user.hasModule(CloudfarmsModule.breeding) && settingsHandler.getFarmType(user) == 2) {
            addCol(col("genomeDate").type(date).editor(null));
            addCol(col("testState").name("js.label.danbred.teststate").type(text).editor(null).width(12).align(left).formatter(formatTranslateNotEmptyWithCode("js.danavl.teststatus", false)));
            addCol(col("genomeState").name("js.label.danbred.genomestate").type(text).editor(null).width(12).align(left).formatter(formatTranslateNotEmptyWithCode("js.danavl.genomestatus", false)));
        }
        if (user.hasModule(CloudfarmsModule.danGen) && user.hasModule(CloudfarmsModule.breeding) && settingsHandler.getFarmType(user) == 2 /* Breeder */) {
            addCol(col("bloodTest.drawDate1").type(date).name("js.label.bloodtest.drawdate.1").optional(true).sortable(true).cannotTriggerInsert(true).filter(filterDateInterval));
            addCol(col("bloodTest.resultDate1").type(date).name("js.label.bloodtest.resultdate.1").optional(true).sortable(true).cannotTriggerInsert(true).filter(filterDateInterval));
            addCol(col("bloodTest.testNumber1").type(text).editor(maxLength(30)).name("js.label.bloodtest.testnumber.1").sortable(true).filter(filterText).optional(true).cannotTriggerInsert(true));
            addCol(col("bloodTest.drawDate2").type(date).name("js.label.bloodtest.drawdate.2").optional(true).sortable(true).cannotTriggerInsert(true).filter(filterDateInterval));
            addCol(col("bloodTest.resultDate2").type(date).name("js.label.bloodtest.resultdate.2").optional(true).sortable(true).cannotTriggerInsert(true).filter(filterDateInterval));
            addCol(col("bloodTest.testNumber2").type(text).editor(maxLength(30)).name("js.label.bloodtest.testnumber.2").sortable(true).filter(filterText).optional(true).cannotTriggerInsert(true));
            addCol(col("bloodTest.drawDate3").type(date).name("js.label.bloodtest.drawdate.3").optional(true).sortable(true).cannotTriggerInsert(true).filter(filterDateInterval));
            addCol(col("bloodTest.resultDate3").type(date).name("js.label.bloodtest.resultdate.3").optional(true).sortable(true).cannotTriggerInsert(true).filter(filterDateInterval));
            addCol(col("bloodTest.testNumber3").type(text).editor(maxLength(30)).name("js.label.bloodtest.testnumber.3").sortable(true).filter(filterText).optional(true).cannotTriggerInsert(true));
            addCol(col("bloodTest.drawDate4").type(date).name("js.label.bloodtest.drawdate.4").optional(true).sortable(true).cannotTriggerInsert(true).filter(filterDateInterval));
            addCol(col("bloodTest.resultDate4").type(date).name("js.label.bloodtest.resultdate.4").optional(true).sortable(true).cannotTriggerInsert(true).filter(filterDateInterval));
            addCol(col("bloodTest.testNumber4").type(text).editor(maxLength(30)).name("js.label.bloodtest.testnumber.4").sortable(true).filter(filterText).optional(true).cannotTriggerInsert(true));
            addCol(col("bloodTest.drawDate5").type(date).name("js.label.bloodtest.drawdate.5").optional(true).sortable(true).cannotTriggerInsert(true).filter(filterDateInterval));
            addCol(col("bloodTest.resultDate5").type(date).name("js.label.bloodtest.resultdate.5").optional(true).sortable(true).cannotTriggerInsert(true).filter(filterDateInterval));
            addCol(col("bloodTest.testNumber5").type(text).editor(maxLength(30)).name("js.label.bloodtest.testnumber.5").sortable(true).filter(filterText).optional(true).cannotTriggerInsert(true));
            addCol(col("bloodTest.drawDate6").type(date).name("js.label.bloodtest.drawdate.6").optional(true).sortable(true).cannotTriggerInsert(true).filter(filterDateInterval));
            addCol(col("bloodTest.resultDate6").type(date).name("js.label.bloodtest.resultdate.6").optional(true).sortable(true).cannotTriggerInsert(true).filter(filterDateInterval));
            addCol(col("bloodTest.testNumber6").type(text).editor(maxLength(30)).name("js.label.bloodtest.testnumber.6").sortable(true).filter(filterText).optional(true).cannotTriggerInsert(true));
        }
        addCol(col("comment").type(text).editor(maxLength(150)).sortable(true));
        addCol(col("createAccountId").formatter(formatColleague(em)).width(10).filter(filterColleague).sortable(true));
        addCol(col("createDate").type(timestamp).field("createDate").sortable(true));
        addCol(col("updateDate").type(timestamp).sortable(true));
        addCol(col("updateAccountId").formatter(formatColleague(em)).width(10).filter(filterColleague).sortable(true));
        addCol(col("defectCodes").type(text).optional(true).sortable(false).editor(maxLength(1000)));
        addCol(col("lastWithdrawalDate").name("js.label.lastwithdrawaldate").type(dateTime).editor(null).optional(true).filter(filterDateInterval).cannotTriggerInsert(true).sortable(true).width(10));
    }
}
