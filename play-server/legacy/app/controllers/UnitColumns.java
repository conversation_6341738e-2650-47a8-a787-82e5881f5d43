package controllers;

import models.Unit;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static controllers.GridColumn.ColumnType.bool;
import static controllers.GridColumn.ColumnType.text;
import static controllers.GridColumn.Editor.maxLength;
import static controllers.GridColumn.Formatter.formatTranslate;

/**
 * Created by Juraj on 12.5.2014.
 */
public class UnitColumns extends ScreenColumns<Unit> {
    public UnitColumns() {
        addCol(col("unit").type(text).formatter(formatTranslate("js.unit", false)).width(20));
        addCol(col("name").type(text).editor(null).width(20));
        addCol(col("description").type(text).editor(null).width(20));
        addCol(col("medicine").type(bool).editor(null).name("js.label.for.medicine").filter(GridColumn.Filter.boolean3()).filterValue("''").width(12));
    }
}
