package controllers.feed;

import com.fasterxml.jackson.databind.ser.FilterProvider;
import com.fasterxml.jackson.databind.ser.impl.SimpleFilterProvider;
import controllers.*;
import jooq.farm.Tables;
import jooq.farm.tables.*;
import models.*;
import models.handlers.FeedHandler;
import org.apache.commons.lang3.StringUtils;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.*;
import play.api.http.FileMimeTypes;
import play.data.*;
import play.i18n.Messages;
import play.mvc.*;
import play.mvc.Result;
import security.*;
import utils.Utils;
import utils.*;

import javax.inject.*;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Connection;
import java.util.*;


@Singleton
public class FeedConsumptionController extends RestController {


    private final FeedHandler feedHandler;
    private final FormFactory formFactory;

    @Inject
    public FeedConsumptionController(play.Application application, FeedHandler feedHandler, FileMimeTypes fileMimeTypes, FormFactory formFactory) {
        super(application, fileMimeTypes);

        this.feedHandler = feedHandler;
        this.formFactory = formFactory;
    }

    private static final String ALIAS_LOCATION = "location";
    private static final String ALIAS_FEED_CONSUMPTION = "x";
    private static final String ALIAS_FEED_STATION = "feedstation";
    private static final String ALIAS_FEED_SYSTEM = "feedsystem";

    private static final Map<String, Field[]> FEED_CONSUMPTION_SORT_COLS = new HashMap<>();

    static {
        FEED_CONSUMPTION_SORT_COLS.put("id", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).ID));
        FEED_CONSUMPTION_SORT_COLS.put("locationId", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).LOCATION_ID));
        FEED_CONSUMPTION_SORT_COLS.put("feedName", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).FEEDNAME));
        FEED_CONSUMPTION_SORT_COLS.put("description", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).DESCRIPTION));
        FEED_CONSUMPTION_SORT_COLS.put("consumption", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).CONSUMPTION));
        FEED_CONSUMPTION_SORT_COLS.put("dryMatter", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).DRYMATTER));
        FEED_CONSUMPTION_SORT_COLS.put("feedUnit", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).FEEDUNIT));
        FEED_CONSUMPTION_SORT_COLS.put("fromDate", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).FROMDATE));
        FEED_CONSUMPTION_SORT_COLS.put("measureDate", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).MEASUREDATE));
        FEED_CONSUMPTION_SORT_COLS.put("inhabitanttype_code", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).INHABITANTTYPE_CODE));
        FEED_CONSUMPTION_SORT_COLS.put("price", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).PRICE));
        FEED_CONSUMPTION_SORT_COLS.put("rawProteinPercent", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).RAWPROTEIN_PERCENT));
        FEED_CONSUMPTION_SORT_COLS.put("gcalcium", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).G_CALCIUM));
        FEED_CONSUMPTION_SORT_COLS.put("gphosforKg", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).G_PHOSFOR_KG));
        FEED_CONSUMPTION_SORT_COLS.put("gkaliumKg", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).G_KALIUM_KG));
        FEED_CONSUMPTION_SORT_COLS.put("gsodiumKg", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).G_SODIUM_KG));
        FEED_CONSUMPTION_SORT_COLS.put("comment", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).COMMENT));
        FEED_CONSUMPTION_SORT_COLS.put("feedStation", fields(Tables.FEED_STATION.as(ALIAS_FEED_STATION).NAME));
        FEED_CONSUMPTION_SORT_COLS.put("feedSystem", fields(Tables.FEED_SYSTEM.as(ALIAS_FEED_SYSTEM).NAME));
        FEED_CONSUMPTION_SORT_COLS.put("actorId", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).ACTOR_ID));
        FEED_CONSUMPTION_SORT_COLS.put("actorDate", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).ACTOR_DATE));
        FEED_CONSUMPTION_SORT_COLS.put("createDate", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).CREATE_DATE));
        FEED_CONSUMPTION_SORT_COLS.put("updateDate", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).UPDATE_DATE));
        FEED_CONSUMPTION_SORT_COLS.put("createAccountId", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).CREATE_ACCOUNT_ID));
        FEED_CONSUMPTION_SORT_COLS.put("updateAccountId", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).UPDATE_ACCOUNT_ID));
        FEED_CONSUMPTION_SORT_COLS.put("createUi", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).CREATE_UI));
        FEED_CONSUMPTION_SORT_COLS.put("type", fields(Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION).TYPE));
        FEED_CONSUMPTION_SORT_COLS.put("supplierId", fields(Tables.BUSINESS.as("sup").COMPANYNAME));
    }

    private static class Mappers {
        public final RecordMapper<Record, FeedConsumption> feedConsumptionRecordMapper;

        private Mappers(SelectSeekStepN<Record> query) {
            this.feedConsumptionRecordMapper = new CfRecordMapper<>("feedc___", query.fields(), FeedConsumption.class, null, JooqUtils.CF_JOOQ_CONFIG);
        }
    }

    public static FeedConsumption fromRecord(Record record, final Mappers mappers) {
        return mappers.feedConsumptionRecordMapper.map(record);
    }

    @WithTenant({@Group(farm = FarmRole.feedRead), @Group(farm = FarmRole.feedWrite)})
    public Result list(int from, int count, String order, Http.Request request) {
        final ApplicationUser user = applicationUserHandler.current(request);
        return withEm(request, em -> {
            FilterProvider filtering = new SimpleFilterProvider();
            final SelectSeekStepN<Record> query = prepareJooqQuery(request, user, order);
            Connection db = em.unwrap(Connection.class);
            final Configuration configWithDb = JooqUtils.CF_JOOQ_CONFIG.derive(new DefaultConnectionProvider(db));
            query.attach(configWithDb);
            final Mappers mappers = new Mappers(query);

            int filteredCount;
            final org.jooq.Result<Record> fetch = query.limit(from, count).fetch();
            final List<FeedConsumption> list = new ArrayList<>();
            fetch.into(record -> {
                FeedConsumption x = fromRecord(record, mappers);
                list.add(x);
            });

            if (list.size() < count) {
                filteredCount = from + list.size();
            } else {
                final SelectSeekStepN<Record> countQuery = prepareJooqQuery(request, user, "");
                filteredCount = fetchCount(em, countQuery);
            }

            Result result = ok(Utils.toJson(list, JsonLevel.Overview.class, filtering))
                .withHeader("x-cf-Items-Count", Integer.toString(filteredCount));


            SelectConditionStep<Record1<BigDecimal>> statisticsQuery = prepareStatisticsQuery(request, user);
            statisticsQuery.attach(configWithDb);
            Record record = statisticsQuery.fetchOne();
            result = withHeader(result, record, "consumptionSum");

            return result;
        });
    }

    @WithTenant({@Group(farm = FarmRole.feedRead), @Group(farm = FarmRole.feedWrite)})
    public Result excel(String order, String columns, String viewColumns, Http.Request request) throws IOException {
        final ApplicationUser user = applicationUserHandler.current(request);
        final Messages messages = messagesApi.preferred(request);
        return withEm(request, em -> {
            final SelectSeekStepN<Record> query = prepareJooqQuery(request, user, order);
            Connection db = em.unwrap(Connection.class);
            final Configuration configWithDb = JooqUtils.CF_JOOQ_CONFIG.derive(new DefaultConnectionProvider(db));
            query.attach(configWithDb);
            final Mappers mappers = new Mappers(query);
            Cursor<Record> cursor = query.fetchLazy();

            final Select<Record> countQuery = prepareJooqQuery(request, user, "");
            int filteredCount = fetchCount(em, countQuery);
            HashMap<String, Integer> columnsWidth = RestController.getViewColumns(viewColumns);
            final ScreenColumns<?> screenColumns = new FeedConsumptionColumns(messages, user, em, settingsHandler);
            return excel(request, new JooqRowIterator<FeedConsumption>(cursor) {
                @Override
                public FeedConsumption recordToTarget(Record record) {
                    return fromRecord(record, mappers);
                }
            }, screenColumns.getColOrder(), screenColumns.getColDefs(), columns, "feed_consumption", filteredCount, messages.at("js.title.feedconsumption"), columnsWidth);
        });
    }

    private SelectSeekStepN<Record> prepareJooqQuery(Http.Request request, ApplicationUser user, String order) {
        final Feedconsumption feedconsumption = Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION);
        final jooq.farm.tables.Location location = Tables.LOCATION.as(ALIAS_LOCATION);
        final FeedStation feedstation = Tables.FEED_STATION.as(ALIAS_FEED_STATION);
        final FeedSystem feedSystem = Tables.FEED_SYSTEM.as(ALIAS_FEED_SYSTEM);
        final jooq.farm.tables.Business supplier = Tables.BUSINESS.as("sup");

        List<Condition> conditions = applyConditions(request, user, feedconsumption, feedstation, feedSystem);
        if (order == null)
            order = "measureDate desc";

        List<Field<?>> fields = JooqUtils.makeFields(JooqUtils.prefixFields("feedc___", feedconsumption.fields()));
        fields.add(feedstation.NAME.as("feedc___feedStation"));
        fields.add(feedSystem.NAME.as("feedc___feedSystem"));

        SelectOnConditionStep<Record> query = DSL.select(fields)
            .from(feedconsumption)
            .leftOuterJoin(location).on(location.ID.equal(feedconsumption.LOCATION_ID))
            .leftOuterJoin(feedstation).on(feedstation.ID.equal(feedconsumption.FEED_STATION_ID))
            .leftOuterJoin(feedSystem).on(feedstation.FEED_SYSTEM_ID.equal(feedSystem.ID));

        if (StringUtils.contains(order, "supplierId"))
            query = query.leftOuterJoin(supplier).on(supplier.ID.eq(feedconsumption.SUPPLIER_ID));

        return query
            .where(conditions)
            .orderBy(order2(order, FEED_CONSUMPTION_SORT_COLS, feedconsumption.ID.desc()));
    }

    private List<Condition> applyConditions(Http.Request request, ApplicationUser user, Feedconsumption feedConsumption, FeedStation feedStation, FeedSystem feedSystem) {
        List<Condition> conditions = new ArrayList<>();
        Form<FeedConsumptionFilters> filterForm = formFactory.form(FeedConsumptionFilters.class);
        FeedConsumptionFilters filters = filterForm.bindFromRequest(request).get();

        cfFilters.textExact(conditions, filters.inhabitanttype_code, feedConsumption.INHABITANTTYPE_CODE);
        cfFilters.filterLocation(user, conditions, filters.locationId, feedConsumption.LOCATION_ID);
        cfFilters.text(conditions, filters.feedName, feedConsumption.FEEDNAME);
        cfFilters.text(conditions, filters.description, feedConsumption.DESCRIPTION);
        cfFilters.text(conditions, filters.comment, feedConsumption.COMMENT);

        cfFilters.bigDecimalInterval(conditions, filters.consumption, feedConsumption.CONSUMPTION, false);
        cfFilters.bigDecimalInterval(conditions, filters.feedUnit, feedConsumption.FEEDUNIT, false);
        cfFilters.bigDecimalInterval(conditions, filters.dryMatter, feedConsumption.DRYMATTER, false);
        cfFilters.bigDecimalInterval(conditions, filters.price, feedConsumption.PRICE, false);
        cfFilters.bigDecimalInterval(conditions, filters.rawProteinPercent, feedConsumption.RAWPROTEIN_PERCENT, false);
        cfFilters.bigDecimalInterval(conditions, filters.gcalcium, feedConsumption.G_CALCIUM, false);
        cfFilters.bigDecimalInterval(conditions, filters.gphosforKg, feedConsumption.G_PHOSFOR_KG, false);
        cfFilters.bigDecimalInterval(conditions, filters.gkaliumKg, feedConsumption.G_KALIUM_KG, false);
        cfFilters.bigDecimalInterval(conditions, filters.gsodiumKg, feedConsumption.G_SODIUM_KG, false);
        cfFilters.text(conditions, filters.feedStation, feedStation.NAME);
        cfFilters.text(conditions, filters.feedSystem, feedSystem.NAME);
        cfFilters.dateIntervalNoTimezone(conditions, filters.fromDate, feedConsumption.FROMDATE);
        cfFilters.dateIntervalNoTimezone(conditions, filters.measureDate, feedConsumption.MEASUREDATE);
        cfFilters.longNumber(conditions, filters.actorId, feedConsumption.ACTOR_ID);
        cfFilters.dateInterval(user, conditions, filters.updateDate, feedConsumption.UPDATE_DATE);
        cfFilters.dateInterval(user, conditions, filters.createDate, feedConsumption.CREATE_DATE);
        cfFilters.longNumber(conditions, filters.createAccountId, feedConsumption.CREATE_ACCOUNT_ID);
        cfFilters.longNumber(conditions, filters.updateAccountId, feedConsumption.UPDATE_ACCOUNT_ID);
        cfFilters.ui(conditions, filters.createUi, feedConsumption.CREATE_UI);

        cfFilters.business(conditions, filters.supplierId, feedConsumption.SUPPLIER_ID);
        cfFilters.text(conditions, filters.recipeName, feedConsumption.RECIPE_NAME);
        cfFilters.shortInterval(conditions, filters.type, feedConsumption.TYPE, true);
        return conditions;
    }

    private SelectConditionStep<Record1<BigDecimal>> prepareStatisticsQuery(Http.Request request, ApplicationUser user) {
        final Feedconsumption feedconsumption = Tables.FEEDCONSUMPTION.as(ALIAS_FEED_CONSUMPTION);
        final jooq.farm.tables.Location location = Tables.LOCATION.as(ALIAS_LOCATION);
        final FeedStation feedstation = Tables.FEED_STATION.as(ALIAS_FEED_STATION);
        final FeedSystem feedSystem = Tables.FEED_SYSTEM.as(ALIAS_FEED_SYSTEM);
        final List<Condition> conditions = applyConditions(request, user, feedconsumption, feedstation, feedSystem);

        return DSL
            .select(DSL.sum(feedconsumption.CONSUMPTION).as("consumptionSum"))
            .from(feedconsumption)
            .leftOuterJoin(location).on(location.ID.equal(feedconsumption.LOCATION_ID))
            .leftOuterJoin(feedstation).on(feedstation.ID.equal(feedconsumption.FEED_STATION_ID))
            .leftOuterJoin(feedSystem).on(feedstation.FEED_SYSTEM_ID.equal(feedSystem.ID))
            .where(conditions);
    }

    @WithTenant({@Group(farm = FarmRole.feedRead), @Group(farm = FarmRole.feedWrite)})
    public Result read(Long id, Http.Request request) {
        return read(request, FeedConsumption.class, id);
    }

    @WithTenant({@Group(farm = FarmRole.feedWrite)})
    public Result update(Long id, Http.Request request) {
        return withEm(request, (em) -> update(request, em, FeedConsumption.class, id));
    }

    @WithTenant({@Group(farm = FarmRole.feedWrite)})
    public Result delete(Long id, Http.Request request) throws Throwable {
        return delete(request, FeedConsumption.class, id);
    }

    @WithTenant
    public Result feedNamesFromLastYear(Http.Request request) {
        Date oneYearAgo = new Date(System.currentTimeMillis() - 365L * 24 * 60 * 60 * 1500); // 1500 so that we cover 1,5 years
        return withEm(request, em -> ok(Utils.toJson(feedHandler.uniqueFeedNames(em, oneYearAgo))));
    }

    @WithTenant
    public Result getLastFeed(String feedName, Http.Request request) {
        return withEm(request, em -> {
            FeedConsumption fc = feedHandler.lastFeed(em, feedName);
            return fc == null ? ok() : ok(Utils.toJson(fc));
        });
    }

    public static class FeedConsumptionFilters {
        private String actorId;
        private String comment;
        private String consumption;
        private String createAccountId;
        private CfDateInterval createDate;
        private String createUi;
        private String description;
        private String feedName;
        private String feedUnit;
        private String dryMatter;
        private String gcalcium;
        private String gkaliumKg;
        private String gphosforKg;
        private String gsodiumKg;
        private String inhabitanttype_code;
        private String locationId;
        private String feedStation;
        private String feedSystem;
        private CfDateInterval fromDate;
        private CfDateInterval measureDate;
        private String price;
        private String rawProteinPercent;
        private String updateAccountId;
        private CfDateInterval updateDate;
        private String supplierId;
        private String recipeName;
        private String type;

        public String getRecipeName() {
            return recipeName;
        }

        public void setRecipeName(String recipeName) {
            this.recipeName = recipeName;
        }

        public String getSupplierId() {
            return supplierId;
        }

        public void setSupplierId(String supplierId) {
            this.supplierId = supplierId;
        }

        public String getActorId() {
            return actorId;
        }

        public void setActorId(String actorId) {
            this.actorId = actorId;
        }

        public String getComment() {
            return comment;
        }

        public void setComment(String comment) {
            this.comment = comment;
        }

        public String getConsumption() {
            return consumption;
        }

        public void setConsumption(String consumption) {
            this.consumption = consumption;
        }

        public String getCreateAccountId() {
            return createAccountId;
        }

        public void setCreateAccountId(String createAccountId) {
            this.createAccountId = createAccountId;
        }

        public CfDateInterval getCreateDate() {
            return createDate;
        }

        public void setCreateDate(CfDateInterval createDate) {
            this.createDate = createDate;
        }

        public String getCreateUi() {
            return createUi;
        }

        public void setCreateUi(String createUi) {
            this.createUi = createUi;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public String getFeedName() {
            return feedName;
        }

        public void setFeedName(String feedName) {
            this.feedName = feedName;
        }

        public String getFeedUnit() {
            return feedUnit;
        }

        public void setFeedUnit(String feedUnit) {
            this.feedUnit = feedUnit;
        }

        public String getDryMatter() {
            return dryMatter;
        }

        public void setDryMatter(String dryMatter) {
            this.dryMatter = dryMatter;
        }

        public String getGcalcium() {
            return gcalcium;
        }

        public void setGcalcium(String gcalcium) {
            this.gcalcium = gcalcium;
        }

        public String getGkaliumKg() {
            return gkaliumKg;
        }

        public void setGkaliumKg(String gkaliumKg) {
            this.gkaliumKg = gkaliumKg;
        }

        public String getGphosforKg() {
            return gphosforKg;
        }

        public void setGphosforKg(String gphosforKg) {
            this.gphosforKg = gphosforKg;
        }

        public String getGsodiumKg() {
            return gsodiumKg;
        }

        public void setGsodiumKg(String gsodiumKg) {
            this.gsodiumKg = gsodiumKg;
        }

        public String getInhabitanttype_code() {
            return inhabitanttype_code;
        }

        public void setInhabitanttype_code(String inhabitanttype_code) {
            this.inhabitanttype_code = inhabitanttype_code;
        }

        public String getLocationId() {
            return locationId;
        }

        public void setLocationId(String locationId) {
            this.locationId = locationId;
        }

        public String getFeedStation() {
            return feedStation;
        }

        public void setFeedStation(String feedStation) {
            this.feedStation = feedStation;
        }

        public String getFeedSystem() {
            return feedSystem;
        }

        public void setFeedSystem(String feedSystem) {
            this.feedSystem = feedSystem;
        }

        public CfDateInterval getFromDate() {
            return fromDate;
        }

        public void setFromDate(CfDateInterval fromDate) {
            this.fromDate = fromDate;
        }

        public CfDateInterval getMeasureDate() {
            return measureDate;
        }

        public void setMeasureDate(CfDateInterval measureDate) {
            this.measureDate = measureDate;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getRawProteinPercent() {
            return rawProteinPercent;
        }

        public void setRawProteinPercent(String rawProteinPercent) {
            this.rawProteinPercent = rawProteinPercent;
        }

        public String getUpdateAccountId() {
            return updateAccountId;
        }

        public void setUpdateAccountId(String updateAccountId) {
            this.updateAccountId = updateAccountId;
        }

        public CfDateInterval getUpdateDate() {
            return updateDate;
        }

        public void setUpdateDate(CfDateInterval updateDate) {
            this.updateDate = updateDate;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }
    }
}
